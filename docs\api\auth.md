# Authentication API

The Authentication API handles user authentication through Discord OAuth and provides endpoints for retrieving the current user's profile.

## Endpoints

### Discord OAuth Callback

Handles the callback from Discord OAuth and returns a JWT token.

```
POST /api/auth/discord
```

#### Request Body

| Parameter | Type   | Required | Description                                |
|-----------|--------|----------|--------------------------------------------|
| code      | string | Yes      | Authorization code from Discord OAuth      |

#### Response

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "60d21b4667d0d8992e610c85",
      "discordId": "123456789012345678",
      "discordUsername": "username",
      "discordAvatar": "avatar_hash",
      "email": "<EMAIL>",
      "isMentor": false,
      "role": "user",
      "proficiencies": [
        {
          "name": "Streaming Setup",
          "category": "Account Setup",
          "isSelected": true
        }
      ],
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  },
  "message": "Authentication successful"
}
```

#### Status Codes

| Status Code | Description                                                  |
|-------------|--------------------------------------------------------------|
| 200         | Success                                                      |
| 400         | Bad Request - Invalid or missing code                        |
| 401         | Unauthorized - Failed to authenticate with Discord           |
| 500         | Server Error - Failed to process the request                 |

#### Example Request

```bash
curl -X POST http://localhost:3000/api/auth/discord \
  -H "Content-Type: application/json" \
  -d '{"code": "discord_oauth_code"}'
```

### Get Current User

Retrieves the profile of the currently authenticated user.

```
GET /api/auth/me
```

#### Headers

| Header        | Value                        | Required |
|---------------|------------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN        | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "discordId": "123456789012345678",
    "discordUsername": "username",
    "discordAvatar": "avatar_hash",
    "email": "<EMAIL>",
    "isMentor": false,
    "role": "user",
    "proficiencies": [
      {
        "name": "Streaming Setup",
        "category": "Account Setup",
        "isSelected": true
      }
    ],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Status Codes

| Status Code | Description                                                  |
|-------------|--------------------------------------------------------------|
| 200         | Success                                                      |
| 401         | Unauthorized - Invalid or missing token                      |
| 500         | Server Error - Failed to process the request                 |

#### Example Request

```bash
curl -X GET http://localhost:3000/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Authentication Flow

1. **Frontend**: Redirect user to Discord OAuth URL
   ```
   https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI&response_type=code&scope=identify%20email
   ```

2. **Discord**: User authorizes the application and is redirected back to your application with a code

3. **Frontend**: Exchange the code for a JWT token by calling the `/api/auth/discord` endpoint

4. **Backend**: Validates the code with Discord, creates or updates the user in the database, and returns a JWT token

5. **Frontend**: Store the JWT token and include it in subsequent API requests

## Error Handling

Common authentication errors:

- **Invalid Discord Code**: The provided Discord authorization code is invalid or expired
- **Discord API Error**: Failed to communicate with Discord API
- **Invalid JWT Token**: The provided JWT token is invalid, expired, or malformed
- **User Not Found**: The user associated with the token no longer exists
