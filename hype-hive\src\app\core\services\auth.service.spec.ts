import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';
import { User } from '../models/user.model';
import { environment } from '../../../environments/environment';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let routerSpy: jasmine.SpyObj<Router>;

  const mockUser: User = {
    id: '1',
    discordId: '123456789',
    username: 'testuser',
    email: '<EMAIL>',
    displayName: 'Test User',
    avatar: 'avatar_url',
    isMentor: false,
    proficiencies: {
      streaming: 'beginner',
      contentCreation: 'intermediate',
      communityBuilding: 'advanced'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: Router, useValue: routerSpyObj }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  describe('Authentication', () => {
    it('should be created', () => {
      expect(service).toBeTruthy();
    });

    it('should authenticate user with Discord callback', () => {
      const code = 'discord_auth_code';
      const mockResponse = {
        token: 'jwt_token',
        user: mockUser
      };

      service.handleDiscordCallback(code).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(service.isAuthenticated()).toBe(true);
        expect(service.getCurrentUser()).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/discord/callback`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ code });
      req.flush(mockResponse);
    });

    it('should handle authentication errors', () => {
      const code = 'invalid_code';
      const errorResponse = { error: 'Invalid authorization code' };

      service.handleDiscordCallback(code).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.error).toEqual(errorResponse);
          expect(service.isAuthenticated()).toBe(false);
        }
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/discord/callback`);
      req.flush(errorResponse, { status: 401, statusText: 'Unauthorized' });
    });

    it('should get current user from API', () => {
      localStorage.setItem('token', 'valid_token');

      service.getCurrentUserFromAPI().subscribe(user => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/me`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Authorization')).toBe('Bearer valid_token');
      req.flush(mockUser);
    });

    it('should handle unauthorized user request', () => {
      localStorage.setItem('token', 'invalid_token');

      service.getCurrentUserFromAPI().subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.status).toBe(401);
          expect(service.isAuthenticated()).toBe(false);
        }
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/me`);
      req.flush({ error: 'Unauthorized' }, { status: 401, statusText: 'Unauthorized' });
    });
  });

  describe('Token Management', () => {
    it('should store and retrieve token', () => {
      const token = 'test_token';
      service.setToken(token);

      expect(service.getToken()).toBe(token);
      expect(localStorage.getItem('token')).toBe(token);
    });

    it('should check if user is authenticated', () => {
      expect(service.isAuthenticated()).toBe(false);

      service.setToken('valid_token');
      expect(service.isAuthenticated()).toBe(true);

      service.logout();
      expect(service.isAuthenticated()).toBe(false);
    });

    it('should clear token on logout', () => {
      service.setToken('test_token');
      service.setCurrentUser(mockUser);

      expect(service.isAuthenticated()).toBe(true);
      expect(service.getCurrentUser()).toEqual(mockUser);

      service.logout();

      expect(service.isAuthenticated()).toBe(false);
      expect(service.getCurrentUser()).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });
  });

  describe('User Management', () => {
    it('should set and get current user', () => {
      expect(service.getCurrentUser()).toBeNull();

      service.setCurrentUser(mockUser);
      expect(service.getCurrentUser()).toEqual(mockUser);
    });

    it('should update user profile', () => {
      const updateData = {
        displayName: 'Updated Name',
        bio: 'Updated bio',
        proficiencies: {
          streaming: 'advanced',
          contentCreation: 'intermediate',
          communityBuilding: 'beginner'
        }
      };

      const updatedUser = { ...mockUser, ...updateData };

      service.updateProfile(updateData).subscribe(user => {
        expect(user).toEqual(updatedUser);
        expect(service.getCurrentUser()).toEqual(updatedUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/profile`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(updatedUser);
    });

    it('should submit mentor application', () => {
      const applicationData = {
        reason: 'Want to help others',
        experience: 'Streaming for 2 years',
        expertise: ['streaming', 'contentCreation'],
        availability: 'weekends'
      };

      const userWithApplication = {
        ...mockUser,
        mentorApplication: {
          ...applicationData,
          status: 'pending',
          submittedAt: new Date()
        }
      };

      service.submitMentorApplication(applicationData).subscribe(user => {
        expect(user.mentorApplication).toBeDefined();
        expect(user.mentorApplication?.status).toBe('pending');
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/mentor-application`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(applicationData);
      req.flush(userWithApplication);
    });
  });

  describe('Navigation', () => {
    it('should redirect to login', () => {
      service.redirectToLogin();
      expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
    });

    it('should redirect to Discord OAuth', () => {
      const expectedUrl = `https://discord.com/api/oauth2/authorize?client_id=${environment.discord.clientId}&redirect_uri=${encodeURIComponent(environment.discord.redirectUri)}&response_type=code&scope=identify%20email`;

      // Mock window.location.href
      const originalLocation = window.location;
      delete (window as any).location;
      window.location = { ...originalLocation, href: '' };

      service.redirectToDiscordAuth();
      expect(window.location.href).toBe(expectedUrl);

      // Restore original location
      window.location = originalLocation;
    });
  });

  describe('Observable Streams', () => {
    it('should emit authentication state changes', () => {
      let authState: boolean | undefined;

      service.isAuthenticated$.subscribe(state => {
        authState = state;
      });

      expect(authState).toBe(false);

      service.setToken('test_token');
      expect(authState).toBe(true);

      service.logout();
      expect(authState).toBe(false);
    });

    it('should emit current user changes', () => {
      let currentUser: User | null | undefined;

      service.currentUser$.subscribe(user => {
        currentUser = user;
      });

      expect(currentUser).toBeNull();

      service.setCurrentUser(mockUser);
      expect(currentUser).toEqual(mockUser);

      service.logout();
      expect(currentUser).toBeNull();
    });
  });
});
