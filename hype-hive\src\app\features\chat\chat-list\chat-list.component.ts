import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ChatService } from '../../../core/services/chat.service';
import { SocketService } from '../../../core/services/socket.service';
import { Chat } from '../../../core/models/chat.model';
import { User } from '../../../core/models/user.model';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-chat-list',
  templateUrl: './chat-list.component.html',
  styleUrls: ['./chat-list.component.scss'],
  standalone: false
})
export class ChatListComponent implements OnInit, OnDestroy {
  chats: Chat[] = [];
  isLoading = true;
  errorMessage = '';
  currentPage = 1;
  totalPages = 1;
  hasMoreChats = false;
  currentUser: User | null = null;
  
  private subscriptions: Subscription[] = [];
  public socketConnected = false;

  constructor(
    private chatService: ChatService,
    private socketService: SocketService,
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Get current user
    this.currentUser = this.authService.getCurrentUser();

    // Check if user is authenticated before initializing socket
    if (this.authService.isAuthenticated()) {
      // Initialize socket connection
      this.socketService.initializeSocket();
    }

    // Subscribe to socket connection status
    this.subscriptions.push(
      this.socketService.getConnectionStatus().subscribe(connected => {
        this.socketConnected = connected;
      })
    );

    // Subscribe to authentication changes
    this.subscriptions.push(
      this.authService.isAuthenticated$.subscribe(isAuthenticated => {
        if (isAuthenticated && !this.socketConnected) {
          // User just authenticated, initialize socket
          this.socketService.initializeSocket();
        } else if (!isAuthenticated) {
          // User logged out, disconnect socket
          this.socketService.disconnect();
        }
      })
    );

    // Load chats
    this.loadChats();
  }

  /**
   * Load user chats
   */
  loadChats(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.subscriptions.push(
      this.chatService.getUserChats(this.currentPage).subscribe({
        next: (result) => {
          this.chats = result.chats;
          this.totalPages = result.pagination.pages;
          this.hasMoreChats = this.currentPage < this.totalPages;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to load chats';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Load more chats
   */
  loadMoreChats(): void {
    if (this.isLoading || !this.hasMoreChats) {
      return;
    }
    
    this.currentPage++;
    this.isLoading = true;
    
    this.subscriptions.push(
      this.chatService.getUserChats(this.currentPage).subscribe({
        next: (result) => {
          this.chats = [...this.chats, ...result.chats];
          this.hasMoreChats = this.currentPage < result.pagination.pages;
          this.isLoading = false;
        },
        error: (error) => {
          this.currentPage--; // Revert page increment
          this.errorMessage = error.message || 'Failed to load more chats';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Navigate to chat detail
   * @param chatId Chat ID
   */
  openChat(chatId: string): void {
    this.router.navigate(['/chat', chatId]);
  }

  /**
   * Create a new chat
   */
  createNewChat(): void {
    this.router.navigate(['/chat/new']);
  }

  /**
   * Get chat display name
   * @param chat Chat object
   * @returns Display name for the chat
   */
  getChatDisplayName(chat: Chat): string {
    if (chat.isGroupChat && chat.groupName) {
      return chat.groupName;
    }
    
    // For direct messages, show the other participant's name
    if (this.currentUser && chat.participants) {
      const otherParticipant = chat.participants.find(
        p => p._id !== this.currentUser?._id
      );
      
      if (otherParticipant) {
        return otherParticipant.discordUsername;
      }
    }
    
    return 'Chat';
  }

  /**
   * Get chat avatar
   * @param chat Chat object
   * @returns Avatar URL
   */
  getChatAvatar(chat: Chat): string {
    // For direct messages, show the other participant's avatar
    if (!chat.isGroupChat && this.currentUser && chat.participants) {
      const otherParticipant = chat.participants.find(
        p => p._id !== this.currentUser?._id
      );
      
      if (otherParticipant && otherParticipant.discordAvatar) {
        return otherParticipant.discordAvatar;
      }
    }
    
    // Default avatar for group chats or if no avatar is available
    // Use helplogo.png as fallback since it exists in public folder
    return '/helplogo.png';
  }

  /**
   * Get last message preview
   * @param chat Chat object
   * @returns Last message preview
   */
  getLastMessagePreview(chat: Chat): string {
    if (!chat.lastMessage) {
      return 'No messages yet';
    }
    
    // Truncate message if too long
    const maxLength = 30;
    const content = chat.lastMessage.content;
    
    if (content.length <= maxLength) {
      return content;
    }
    
    return content.substring(0, maxLength) + '...';
  }

  /**
   * Format date for display
   * @param date Date to format
   * @returns Formatted date string
   */
  formatDate(date?: Date): string {
    if (!date) {
      return '';
    }
    
    const now = new Date();
    const messageDate = new Date(date);
    
    // If today, show time
    if (messageDate.toDateString() === now.toDateString()) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // If this year, show month and day
    if (messageDate.getFullYear() === now.getFullYear()) {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
    
    // Otherwise show full date
    return messageDate.toLocaleDateString();
  }

  /**
   * Check if the user has unread messages in a chat
   * @param chat Chat object
   * @returns True if there are unread messages
   */
  hasUnreadMessages(chat: Chat): boolean {
    if (!this.currentUser || !chat.messages || chat.messages.length === 0) {
      return false;
    }
    
    // Check if any messages are not read by the current user
    return chat.messages.some(message => {
      const senderId = typeof message.sender === 'string' ? message.sender : message.sender._id;
      return senderId !== this.currentUser?._id &&
             !message.readBy.some(user => 
                typeof user === 'string' 
                  ? user === this.currentUser?._id 
                  : user._id === this.currentUser?._id
              );
    });
  }

  /**
   * Get unread message count for a chat
   * @param chat Chat object
   * @returns Number of unread messages
   */
  getUnreadCount(chat: Chat): number {
    if (!this.currentUser || !chat.messages) {
      return 0;
    }
    // Count messages not read by the current user
    return chat.messages.filter(message => {
      const senderId = typeof message.sender === 'string' ? message.sender : message.sender?._id;
      return senderId !== this.currentUser?._id &&
        !message.readBy.some(user =>
          typeof user === 'string'
            ? user === this.currentUser?._id
            : user && user._id === this.currentUser?._id
        );
    }).length;
  }

  /**
   * Check if the chat has a Discord avatar
   * @param chat Chat object
   * @returns True if user has Discord avatar
   */
  hasDiscordAvatar(chat: Chat): boolean {
    if (!chat.isGroupChat && this.currentUser && chat.participants) {
      const otherParticipant = chat.participants.find(
        p => p._id !== this.currentUser?._id
      );
      
      return !!(otherParticipant && otherParticipant.discordAvatar);
    }
    
    return false;
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
