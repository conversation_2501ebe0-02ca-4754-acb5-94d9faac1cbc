# Build and Deploy Script for HypeHive

# Set error action preference to stop on error
$ErrorActionPreference = "Stop"

# Define colors for console output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Display header
Write-ColorOutput "Green" "====================================================="
Write-ColorOutput "Green" "  HypeHive Build and Deployment Script"
Write-ColorOutput "Green" "====================================================="
Write-Output ""

try {
    # Step 1: Install backend dependencies
    Write-ColorOutput "Cyan" "Step 1: Installing backend dependencies..."
    Set-Location -Path "backend"
    npm install
    if ($LASTEXITCODE -ne 0) { throw "Failed to install backend dependencies" }
    Write-ColorOutput "Green" "✓ Backend dependencies installed successfully"
    Write-Output ""

    # Step 2: Install frontend dependencies
    Write-ColorOutput "Cyan" "Step 2: Installing frontend dependencies..."
    Set-Location -Path "..\hype-hive"
    npm install
    if ($LASTEXITCODE -ne 0) { throw "Failed to install frontend dependencies" }
    Write-ColorOutput "Green" "✓ Frontend dependencies installed successfully"
    Write-Output ""

    # Step 3: Build frontend for production
    Write-ColorOutput "Cyan" "Step 3: Building frontend for production..."
    npm run build -- --configuration=production
    if ($LASTEXITCODE -ne 0) { throw "Failed to build frontend" }
    Write-ColorOutput "Green" "✓ Frontend built successfully"
    Write-Output ""

    # Step 4: Return to root directory
    Set-Location -Path ".."

    # Step 5: Create deployment package
    Write-ColorOutput "Cyan" "Step 4: Creating deployment package..."
    
    # Create deployment directory if it doesn't exist
    if (-not (Test-Path -Path "deploy")) {
        New-Item -ItemType Directory -Path "deploy"
    }
    
    # Copy backend files
    Write-Output "Copying backend files..."
    Copy-Item -Path "backend\*" -Destination "deploy\backend" -Recurse -Force
    
    # Copy frontend dist files
    Write-Output "Copying frontend files..."
    Copy-Item -Path "hype-hive\dist\hype-hive\browser\*" -Destination "deploy\frontend" -Recurse -Force
    
    # Copy deployment scripts
    Write-Output "Copying deployment scripts..."
    Copy-Item -Path "deploy-scripts\*" -Destination "deploy" -Recurse -Force
    
    Write-ColorOutput "Green" "✓ Deployment package created successfully"
    Write-Output ""

    # Step 6: Display success message
    Write-ColorOutput "Green" "====================================================="
    Write-ColorOutput "Green" "  Build and packaging completed successfully!"
    Write-ColorOutput "Green" "====================================================="
    Write-Output ""
    Write-Output "The deployment package is available in the 'deploy' directory."
    Write-Output "To deploy to production, follow these steps:"
    Write-Output ""
    Write-Output "1. Upload the contents of the 'deploy' directory to your production server"
    Write-Output "2. Configure environment variables on your production server"
    Write-Output "3. Start the backend server using 'npm start' or a process manager like PM2"
    Write-Output "4. Configure your web server to serve the frontend files"
    Write-Output ""
    Write-ColorOutput "Yellow" "Note: Make sure to update the environment.prod.ts file with your production URLs before building."
}
catch {
    # Display error message
    Write-ColorOutput "Red" "====================================================="
    Write-ColorOutput "Red" "  Error: $($_.Exception.Message)"
    Write-ColorOutput "Red" "====================================================="
    Write-Output ""
    exit 1
}
finally {
    # Return to root directory
    Set-Location -Path $PSScriptRoot
}
