.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.profile-content {
  width: 100%;
  max-width: 800px;
}

.profile-card {
  width: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.profile-avatar {
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #6441a5; // Twitch purple
  color: white;
}

.mentor-badge {
  background-color: #6441a5;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.profile-section {
  margin-top: 1.5rem;
}

.category-section {
  margin-bottom: 1.5rem;
}

h3 {
  color: #6441a5;
  border-bottom: 2px solid #6441a5;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

h4 {
  color: #333;
  margin-bottom: 0.5rem;
}

.proficiency-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.no-proficiencies {
  font-style: italic;
  color: #666;
}

.loading-container, .error-container, .not-authenticated {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.error-container mat-icon, .not-authenticated mat-icon {
  font-size: 3rem;
  height: 3rem;
  width: 3rem;
  margin-bottom: 1rem;
}

.not-authenticated h2 {
  color: #6441a5;
  margin-bottom: 1rem;
}

button {
  margin-top: 1rem;
}

// Mentor Application Section
.mentor-application-section {
  margin-bottom: 2rem;

  h3 {
    color: #6441a5;
    border-bottom: 2px solid #6441a5;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }

  .mentor-info {
    color: #666;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .application-status {
    margin-bottom: 1rem;

    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 500;
      margin-bottom: 0.5rem;

      &.status-pending {
        background-color: rgba(255, 152, 0, 0.1);
        color: #ff9800;
        border: 1px solid rgba(255, 152, 0, 0.3);
      }

      &.status-approved {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4caf50;
        border: 1px solid rgba(76, 175, 80, 0.3);
      }

      &.status-rejected {
        background-color: rgba(244, 67, 54, 0.1);
        color: #f44336;
        border: 1px solid rgba(244, 67, 54, 0.3);
      }

      mat-icon {
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }
    }

    .status-description {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
      margin: 0;
    }
  }

  .apply-button {
    background: linear-gradient(135deg, #6441a5 0%, #9146ff 100%);
    color: white;
    font-weight: 500;

    mat-icon {
      margin-right: 0.5rem;
    }

    &:hover {
      background: linear-gradient(135deg, #5a3a94 0%, #8039e6 100%);
    }
  }
}

mat-divider {
  margin: 1.5rem 0;
}