import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private tokenKey = 'discord_token';

  constructor(private router: Router, @Inject(PLATFORM_ID) private platformId: Object) {}

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Get token from localStorage (browser only)
    let token: string | null = null;
    if (this.isBrowser()) {
      token = localStorage.getItem(this.tokenKey);
    }

    // Clone request with credentials and optional token header
    const headers: any = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    request = request.clone({
      setHeaders: headers,
      withCredentials: true // Include cookies for server-side auth
    });
    // Handle the request and catch any errors
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        // Handle 401 Unauthorized errors
        if (error.status === 401) {
          // Clear token and redirect to login (browser only)
          if (this.isBrowser()) {
            localStorage.removeItem(this.tokenKey);
          }
          this.router.navigate(['/auth/login']);
        }
        return throwError(() => error);
      })
    );
  }
}
