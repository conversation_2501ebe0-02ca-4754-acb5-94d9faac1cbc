.mentor-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

.mentor-avatar {
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #6441a5; // Twitch purple
  color: white;
}

.mentor-badge {
  background-color: #6441a5;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.mentor-bio {
  margin: 1rem 0;
  color: #555;
  line-height: 1.5;
  
  &.no-bio {
    font-style: italic;
    color: #999;
  }
}

.mentor-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  
  a {
    color: #6441a5;
  }
}

mat-card-content {
  flex-grow: 1;
}

mat-card-actions {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  
  button {
    flex: 1;
  }
}
