/**
 * Enhanced Deployment script for HypeHive frontend
 * 
 * This script prepares the application for deployment to production with:
 * - Versioning and cache busting
 * - CDN integration
 * - Build cleanup
 * - Smart caching strategies
 * 
 * Usage: node deploy.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { cleanBuildArtifacts, createVersionFile } = require('./scripts/clean-build');
const CacheManager = require('./scripts/cache-clear');
const CDNManager = require('./scripts/cdn-manager');

// Configuration
const config = {
  // Environment variables to check
  requiredEnvVars: [
    'DISCORD_CLIENT_ID',
    'DISCORD_REDIRECT_URI',
    'API_URL'
  ],
  
  // Environment file path
  envFilePath: path.join(__dirname, 'src/environments/environment.prod.ts'),
  
  // Build options
  buildOptions: {
    enableVersioning: true,
    enableCDN: true,
    enableCacheBusting: true,
    enableCleanup: true
  }
};

/**
 * Update package.json version for cache busting
 */
async function updateVersion() {
  if (!config.buildOptions.enableVersioning) {
    console.log('⏭️  Versioning disabled, skipping version update');
    return;
  }

  console.log('📋 Updating version for cache busting...');
  
  try {
    execSync('npm version patch --no-git-tag-version', { stdio: 'inherit' });
    console.log('✅ Version updated successfully');
  } catch (error) {
    console.error('❌ Error updating version:', error.message);
    process.exit(1);
  }
}

/**
 * Clean build artifacts
 */
async function performCleanup() {
  if (!config.buildOptions.enableCleanup) {
    console.log('⏭️  Cleanup disabled, skipping cleanup');
    return;
  }

  console.log('🧹 Performing build cleanup...');
  
  try {
    await cleanBuildArtifacts();
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
}

/**
 * Generate cache manifest and CDN configuration
 */
async function setupCaching() {
  if (!config.buildOptions.enableCacheBusting) {
    console.log('⏭️  Cache busting disabled, skipping cache setup');
    return;
  }

  console.log('🗂️  Setting up caching strategies...');
  
  try {
    const cacheManager = new CacheManager();
    await cacheManager.createCacheHeaders();
    await cacheManager.generateCacheManifest();
    
    console.log('✅ Cache setup completed');
  } catch (error) {
    console.error('❌ Error setting up cache:', error.message);
    process.exit(1);
  }
}

/**
 * Configure CDN
 */
async function setupCDN() {
  if (!config.buildOptions.enableCDN) {
    console.log('⏭️  CDN disabled, skipping CDN setup');
    return;
  }

  console.log('🌐 Setting up CDN configuration...');
  
  try {
    const cdnManager = new CDNManager();
    await cdnManager.generateCDNConfig();
    await cdnManager.createNginxConfig();
    
    console.log('✅ CDN setup completed');
  } catch (error) {
    console.error('❌ Error setting up CDN:', error.message);
    process.exit(1);
  }
}

/**
 * Purge CDN cache after deployment
 */
async function purgeCDNCache() {
  if (!config.buildOptions.enableCDN) {
    console.log('⏭️  CDN disabled, skipping cache purge');
    return;
  }

  console.log('🗑️  Purging CDN cache...');
  
  try {
    const cdnManager = new CDNManager();
    await cdnManager.purgeCache();
    
    console.log('✅ CDN cache purged');
  } catch (error) {
    console.error('❌ Error purging CDN cache:', error.message);
    // Don't exit on purge failure, it's not critical
  }
}

/**
 * Read required variables from environment.prod.ts (camelCase keys)
 */
function getEnvVarsFromProdFile() {
  const envFile = fs.readFileSync(config.envFilePath, 'utf8');
  const vars = {};
  // Extract values using regex for camelCase keys
  vars.DISCORD_CLIENT_ID = envFile.match(/clientId:\s*['"]([^'"]+)['"]/)[1];
  vars.DISCORD_REDIRECT_URI = envFile.match(/redirectUri:\s*['"]([^'"]+)['"]/)[1];
  vars.API_URL = envFile.match(/apiUrl:\s*['"]([^'"]+)['"]/)[1];
  return vars;
}

/**
 * Check required environment variables
 */
function checkEnvironmentVariables() {
  console.log('Checking environment variables...');
  let envVars = {};
  try {
    envVars = getEnvVarsFromProdFile();
  } catch (e) {
    console.error('Error reading environment.prod.ts:', e.message);
    process.exit(1);
  }
  const missingVars = [];
  config.requiredEnvVars.forEach(envVar => {
    if (!envVars[envVar]) {
      missingVars.push(envVar);
    }
  });
  if (missingVars.length > 0) {
    console.error('Error: Missing required environment variables:');
    missingVars.forEach(v => console.error('- ' + v));
    process.exit(1);
  }
  console.log('All required environment variables are set.');
}

/**
 * Update environment.prod.ts file with environment variables
 */
function updateEnvironmentFile() {
  console.log('Updating environment.prod.ts file...');
  
  try {
    const envFileContent = `export const environment = {
  production: true,
  discord: {
    clientId: '${process.env.DISCORD_CLIENT_ID}',
    redirectUri: '${process.env.DISCORD_REDIRECT_URI}',
    apiEndpoint: 'https://discord.com/api/v10'
  },
  apiUrl: '${process.env.API_URL}',
  socketUrl: '${process.env.SOCKET_URL}',
  monitoring: {
    errorTrackingEnabled: true,
    analyticsEnabled: true
  }
};`;
    
    fs.writeFileSync(config.envFilePath, envFileContent);
    console.log('Environment file updated successfully.');
  } catch (error) {
    console.error('Error updating environment file:', error.message);
    process.exit(1);
  }
}

/**
 * Build the application for production
 */
function buildApplication() {
  console.log('🏗️  Building application for production...');
  
  try {
    execSync('npm run build:prod', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
  } catch (error) {
    console.error('❌ Error building application:', error.message);
    process.exit(1);
  }
}

/**
 * Main deployment function
 */
async function deploy() {
  console.log('🚀 Starting enhanced deployment process...');
  console.log('='.repeat(50));

  try {
    // Step 1: Cleanup
    await performCleanup();
    
    // Step 2: Version update
    await updateVersion();
    
    // Step 3: Environment check and update
    const missingEnvVars = config.requiredEnvVars.filter(
      v => !process.env[v]
    );

    if (missingEnvVars.length === 0) {
      updateEnvironmentFile();
    } else {
      console.warn('⚠️  Warning: Not all required environment variables are set.');
      console.warn('The existing environment.prod.ts will be used for the build.');
      missingEnvVars.forEach(v => console.warn('- ' + v));
    }

    // Step 4: Build application
    buildApplication();

    // Step 5: Setup caching
    await setupCaching();

    // Step 6: Setup CDN
    await setupCDN();

    // Step 7: Purge CDN cache
    await purgeCDNCache();

    console.log('='.repeat(50));
    console.log('🎉 Enhanced deployment completed successfully!');
    console.log('📁 The production build is available in the dist/ directory');
    console.log('🌐 CDN configuration is available in dist/assets/cdn-config.json');
    console.log('⚙️  Nginx configuration is available in dist/config/nginx.conf');
    
    // Display deployment summary
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`📋 Deployed version: ${packageJson.version}`);
    console.log(`🕐 Build time: ${new Date().toISOString()}`);
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

// Run deployment
deploy();
