.new-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
}

.new-chat-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  
  button {
    margin-right: 0.5rem;
  }
  
  h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
  }
}

.new-chat-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  
  form {
    display: flex;
    flex-direction: column;
    
    .form-group {
      margin-bottom: 1rem;
      
      &.group-chat-toggle {
        margin: 1rem 0;
      }
    }
  }
}

.search-input {
  width: 100%;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-top: -1rem;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .mentor-badge {
    background-color: #3f51b5;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
  }
}

.selected-users {
  margin: 1rem 0;
  
  h3 {
    margin: 0 0 0.5rem;
    font-size: 1rem;
    font-weight: 500;
  }
  
  .user-chips {
    display: flex;
    flex-wrap: wrap;
  }
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin-bottom: 1rem;
  
  mat-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
    height: 1.25rem;
    width: 1.25rem;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  
  button {
    &:not(:last-child) {
      margin-right: 0.5rem;
    }
  }
}
