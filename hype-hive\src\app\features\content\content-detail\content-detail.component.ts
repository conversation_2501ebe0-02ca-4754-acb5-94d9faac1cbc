import { <PERSON>mpo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ContentService, Content } from '../../../core/services/content.service';
import { AuthService } from '../../../core/services/auth.service';
import { User } from '../../../core/models/user.model';

// Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

interface ExtendedContent extends Content {
  isLiked?: boolean;
}

interface Comment {
  _id: string;
  content: string;
  createdBy: {
    _id: string;
    displayName: string;
  };
  createdAt: string;
}

@Component({
  selector: 'app-content-detail',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule, 
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatChipsModule,
    MatDividerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './content-detail.component.html',
  styleUrls: ['./content-detail.component.scss']
})
export class ContentDetailComponent implements OnInit, OnDestroy {
  content: ExtendedContent | null = null;
  loading = true;
  isLoading = true; // Template uses isLoading
  error: string | null = null;
  private subscriptions = new Subscription();
  
  // Comment-related properties
  commentText = new FormControl('', [Validators.required, Validators.maxLength(1000)]);
  isSubmittingComment = false;
  commentError: string | null = null;
  isAuthenticated = false;
  currentUser: User | null = null;
  
  // Video properties
  safeEmbedUrl: SafeResourceUrl | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private contentService: ContentService,
    private authService: AuthService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication status and current user
    this.authService.isAuthenticated$.subscribe(isAuth => {
      this.isAuthenticated = isAuth;
    });
    
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    
    // Get content ID from route
    const contentId = this.route.snapshot.paramMap.get('id');
    if (contentId) {
      this.loadContent(contentId);
    } else {
      this.error = 'Content ID not found';
      this.loading = false;
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadContent(id: string): void {
    this.loading = true;
    this.isLoading = true;
    this.error = null;

    const contentSub = this.contentService.getContentById(id).subscribe({
      next: (content) => {
        this.content = content;
        // Determine if current user has liked this content
        if (this.currentUser?._id && content.likedBy) {
          this.content.isLiked = content.likedBy.includes(this.currentUser._id);
        }
        // Set up safe embed URL if needed
        if (content.embedUrl) {
          this.safeEmbedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(content.embedUrl);
        }
        this.loading = false;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading content:', error);
        this.error = 'Failed to load content';
        this.loading = false;
        this.isLoading = false;
      }
    });

    this.subscriptions.add(contentSub);
  }

  onLike(): void {
    if (!this.content || !this.isAuthenticated) {
      return;
    }

    const likeSub = this.contentService.toggleLike(this.content._id).subscribe({
      next: (response) => {
        if (this.content) {
          this.content.isLiked = response.liked;
          this.content.likes = response.likes;
        }
      },
      error: (error) => {
        console.error('Error toggling like:', error);
      }
    });

    this.subscriptions.add(likeSub);
  }

  toggleLike(): void {
    this.onLike();
  }

  onSubmitComment(): void {
    if (!this.content || !this.isAuthenticated || this.commentText.invalid || !this.commentText.value?.trim()) {
      return;
    }

    this.isSubmittingComment = true;
    this.commentError = null;

    const commentSub = this.contentService.addComment(this.content._id, this.commentText.value.trim()).subscribe({
      next: (comment) => {
        if (this.content) {
          // Add the new comment to the beginning of the comments array
          if (!this.content.comments) {
            this.content.comments = [];
          }
          this.content.comments.unshift(comment);
        }
        this.commentText.reset();
        this.isSubmittingComment = false;
      },
      error: (error) => {
        console.error('Error submitting comment:', error);
        this.commentError = 'Failed to submit comment. Please try again.';
        this.isSubmittingComment = false;
      }
    });

    this.subscriptions.add(commentSub);
  }

  goBack(): void {
    this.router.navigate(['/content']);
  }

  getVideoUrl(): string {
    if (!this.content?.videoUrl) return '';
    
    // If it's already a full URL, return as is
    if (this.content.videoUrl.startsWith('http')) {
      return this.content.videoUrl;
    }
    
    // Otherwise, construct the full URL
    return `${window.location.origin}/${this.content.videoUrl}`;
  }

  getThumbnailUrl(): string {
    if (!this.content?.thumbnailUrl) return '';
    
    // If it's already a full URL, return as is
    if (this.content.thumbnailUrl.startsWith('http')) {
      return this.content.thumbnailUrl;
    }
    
    // Otherwise, construct the full URL
    return `${window.location.origin}/${this.content.thumbnailUrl}`;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
  }

  getDifficultyClass(difficulty: string): string {
    return `difficulty-${difficulty}`;
  }

  get isLiked(): boolean {
    return this.content?.isLiked || false;
  }

  addComment(): void {
    this.onSubmitComment();
  }

  trackComment(index: number, comment: Comment): string {
    return comment._id;
  }
}
