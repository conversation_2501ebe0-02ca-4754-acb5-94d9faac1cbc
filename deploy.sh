#!/bin/bash

# HypeHive Deployment Script
# This script automates the deployment process for HypeHive

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="HypeHive"
BACKEND_DIR="backend"
FRONTEND_DIR="hype-hive"
DISCORD_BOT_DIR="discord-bot"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed."
        exit 1
    fi
    
    # Check Python (for Discord bot)
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3 is not installed. Discord bot deployment will be skipped."
    fi
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 is not installed. Installing PM2..."
        npm install -g pm2
    fi
    
    log_success "Prerequisites check completed"
}

run_security_audit() {
    log_info "Running security audit..."
    
    cd "$BACKEND_DIR"
    if [ -f "scripts/security-audit.js" ]; then
        node scripts/security-audit.js
        if [ $? -ne 0 ]; then
            log_error "Security audit failed. Please address security issues before deployment."
            exit 1
        fi
    else
        log_warning "Security audit script not found"
    fi
    cd ..
    
    log_success "Security audit completed"
}

deploy_backend() {
    log_info "Deploying backend..."
    
    cd "$BACKEND_DIR"
    
    # Install dependencies
    log_info "Installing backend dependencies..."
    npm ci --production
    
    # Check environment configuration
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating from template..."
        if [ -f ".env.development" ]; then
            cp .env.development .env
            log_warning "Please update .env with production values before starting the server"
        else
            log_error "No environment template found. Please create .env file."
            exit 1
        fi
    fi
    
    # Run tests
    log_info "Running backend tests..."
    npm test
    if [ $? -ne 0 ]; then
        log_warning "Some tests failed. Continuing with deployment..."
    fi
    
    # Start/restart backend with PM2
    log_info "Starting backend with PM2..."
    pm2 delete hype-hive-backend 2>/dev/null || true
    pm2 start src/index.js --name "hype-hive-backend" --env production
    
    cd ..
    log_success "Backend deployment completed"
}

deploy_frontend() {
    log_info "Deploying frontend..."
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies
    log_info "Installing frontend dependencies..."
    npm ci
    
    # Check Angular CLI
    if ! command -v ng &> /dev/null; then
        log_info "Installing Angular CLI..."
        npm install -g @angular/cli
    fi
    
    # Build for production
    log_info "Building frontend for production..."
    ng build --configuration=production --build-optimizer --aot
    
    if [ $? -ne 0 ]; then
        log_error "Frontend build failed"
        exit 1
    fi
    
    # Copy built files to web server directory (adjust path as needed)
    WEB_DIR="/var/www/html"
    if [ -d "$WEB_DIR" ] && [ -w "$WEB_DIR" ]; then
        log_info "Copying built files to web server..."
        sudo cp -r dist/hype-hive/* "$WEB_DIR/"
        sudo chown -R www-data:www-data "$WEB_DIR"
    else
        log_warning "Web server directory not accessible. Built files are in dist/hype-hive/"
    fi
    
    cd ..
    log_success "Frontend deployment completed"
}

deploy_discord_bot() {
    if [ ! -d "$DISCORD_BOT_DIR" ]; then
        log_warning "Discord bot directory not found. Skipping bot deployment."
        return
    fi
    
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3 not available. Skipping Discord bot deployment."
        return
    fi
    
    log_info "Deploying Discord bot..."
    
    cd "$DISCORD_BOT_DIR"
    
    # Install Python dependencies
    log_info "Installing Discord bot dependencies..."
    pip3 install -r requirements.txt
    
    # Start/restart bot with PM2
    log_info "Starting Discord bot with PM2..."
    pm2 delete hype-hive-bot 2>/dev/null || true
    pm2 start bot.py --name "hype-hive-bot" --interpreter python3
    
    cd ..
    log_success "Discord bot deployment completed"
}

setup_nginx() {
    if ! command -v nginx &> /dev/null; then
        log_warning "Nginx not installed. Skipping Nginx configuration."
        return
    fi
    
    log_info "Setting up Nginx configuration..."
    
    # Create Nginx configuration (basic template)
    NGINX_CONFIG="/etc/nginx/sites-available/hype-hive"
    
    if [ ! -f "$NGINX_CONFIG" ]; then
        log_info "Creating Nginx configuration..."
        sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
server {
    listen 80;
    server_name localhost;

    # Frontend
    location / {
        root /var/www/html;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # File uploads
    location /uploads/ {
        root $(pwd)/backend;
        expires 1y;
        add_header Cache-Control "public";
    }
}
EOF
        
        # Enable the site
        sudo ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/
        sudo nginx -t && sudo systemctl reload nginx
        
        log_success "Nginx configuration created and enabled"
    else
        log_info "Nginx configuration already exists"
    fi
}

save_pm2_config() {
    log_info "Saving PM2 configuration..."
    pm2 save
    pm2 startup
    log_success "PM2 configuration saved"
}

show_status() {
    log_info "Deployment Status:"
    echo ""
    
    # PM2 status
    pm2 list
    echo ""
    
    # Health checks
    log_info "Health Checks:"
    
    # Backend health
    if curl -s http://localhost:3000/api/health > /dev/null; then
        log_success "Backend API is responding"
    else
        log_error "Backend API is not responding"
    fi
    
    # Frontend (if Nginx is running)
    if command -v nginx &> /dev/null && systemctl is-active --quiet nginx; then
        if curl -s http://localhost > /dev/null; then
            log_success "Frontend is accessible"
        else
            log_error "Frontend is not accessible"
        fi
    fi
    
    echo ""
    log_info "Deployment completed!"
    log_info "Backend API: http://localhost:3000/api"
    log_info "Frontend: http://localhost"
    log_info "Health Check: http://localhost:3000/api/health"
    log_info "Performance Metrics: http://localhost:3000/api/performance"
}

# Main deployment process
main() {
    log_info "Starting $PROJECT_NAME deployment..."
    echo ""
    
    check_prerequisites
    run_security_audit
    deploy_backend
    deploy_frontend
    deploy_discord_bot
    setup_nginx
    save_pm2_config
    
    echo ""
    show_status
    
    echo ""
    log_success "$PROJECT_NAME deployment completed successfully!"
    log_warning "Don't forget to:"
    echo "  1. Update environment variables with production values"
    echo "  2. Configure SSL certificates for HTTPS"
    echo "  3. Set up proper domain name and DNS"
    echo "  4. Configure firewall rules"
    echo "  5. Set up monitoring and backups"
}

# Run main function
main "$@"
