import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface CDNConfig {
  version: string;
  buildDate: string;
  baseUrl: string;
  assets: { [key: string]: AssetInfo };
  cacheRules: CacheRule[];
}

export interface AssetInfo {
  hash: string;
  size: number;
  lastModified: string;
  url: string;
  cacheStrategy: string;
}

export interface CacheRule {
  pattern: string;
  headers: { [key: string]: string };
}

@Injectable({
  providedIn: 'root'
})
export class CdnService {
  private cdnConfig$ = new BehaviorSubject<CDNConfig | null>(null);
  private baseUrl = '';
  private version = '';

  constructor(private http: HttpClient) {
    this.loadCDNConfig();
  }

  /**
   * Load CDN configuration from assets
   */
  private loadCDNConfig(): void {
    this.http.get<CDNConfig>('/assets/cdn-config.json')
      .pipe(
        catchError(() => of(null))
      )
      .subscribe(config => {
        if (config) {
          this.cdnConfig$.next(config);
          this.baseUrl = config.baseUrl;
          this.version = config.version;
        }
      });
  }

  /**
   * Get CDN configuration
   */
  getCDNConfig(): Observable<CDNConfig | null> {
    return this.cdnConfig$.asObservable();
  }

  /**
   * Get versioned asset URL with cache busting
   */
  getAssetUrl(path: string): string {
    const config = this.cdnConfig$.value;
    if (!config) {
      return path;
    }

    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    const asset = config.assets[cleanPath];
    
    if (asset) {
      return `${asset.url}?v=${this.version}&h=${asset.hash}`;
    }

    // Fallback for assets not in manifest
    return `${this.baseUrl}/${cleanPath}?v=${this.version}`;
  }

  /**
   * Get cache strategy for a file
   */
  getCacheStrategy(filename: string): string {
    const config = this.cdnConfig$.value;
    if (!config) {
      return 'default';
    }

    const asset = config.assets[filename];
    return asset?.cacheStrategy || 'default';
  }

  /**
   * Check if asset exists in CDN
   */
  assetExists(path: string): boolean {
    const config = this.cdnConfig$.value;
    if (!config) {
      return false;
    }

    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return !!config.assets[cleanPath];
  }

  /**
   * Get all assets of a specific type
   */
  getAssetsByType(extension: string): AssetInfo[] {
    const config = this.cdnConfig$.value;
    if (!config) {
      return [];
    }

    return Object.entries(config.assets)
      .filter(([path]) => path.endsWith(extension))
      .map(([, asset]) => asset);
  }

  /**
   * Get cache headers for a specific file
   */
  getCacheHeaders(filename: string): { [key: string]: string } {
    const config = this.cdnConfig$.value;
    if (!config) {
      return {};
    }

    const ext = filename.split('.').pop()?.toLowerCase();
    const matchingRule = config.cacheRules.find(rule => 
      rule.pattern.includes(`*.${ext}`) || rule.pattern.includes(filename)
    );

    return matchingRule?.headers || {};
  }

  /**
   * Preload critical assets
   */
  preloadCriticalAssets(): void {
    const config = this.cdnConfig$.value;
    if (!config) {
      return;
    }

    // Preload CSS files
    const cssAssets = this.getAssetsByType('.css');
    cssAssets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = asset.url;
      link.as = 'style';
      document.head.appendChild(link);
    });

    // Preload critical JS files
    const jsAssets = this.getAssetsByType('.js').slice(0, 3); // Limit to first 3 JS files
    jsAssets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = asset.url;
      link.as = 'script';
      document.head.appendChild(link);
    });
  }

  /**
   * Add cache-busting query parameters to URL
   */
  addCacheBusting(url: string): string {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}v=${this.version}&t=${Date.now()}`;
  }

  /**
   * Get CDN statistics
   */
  getCDNStats(): Observable<any> {
    const config = this.cdnConfig$.value;
    if (!config) {
      return of({});
    }

    const stats = {
      totalAssets: Object.keys(config.assets).length,
      totalSize: Object.values(config.assets).reduce((sum, asset) => sum + asset.size, 0),
      cacheStrategies: this.getCacheStrategyStats(),
      version: config.version,
      buildDate: config.buildDate
    };

    return of(stats);
  }

  /**
   * Get cache strategy statistics
   */
  private getCacheStrategyStats(): { [key: string]: number } {
    const config = this.cdnConfig$.value;
    if (!config) {
      return {};
    }

    const stats: { [key: string]: number } = {};
    Object.values(config.assets).forEach(asset => {
      stats[asset.cacheStrategy] = (stats[asset.cacheStrategy] || 0) + 1;
    });

    return stats;
  }

  /**
   * Clear CDN cache (for admin use)
   */
  clearCDNCache(): Observable<any> {
    return this.http.post('/api/admin/cdn/clear-cache', {});
  }

  /**
   * Update CDN configuration
   */
  updateCDNConfig(): Observable<any> {
    return this.http.post('/api/admin/cdn/update-config', {})
      .pipe(
        map(() => {
          this.loadCDNConfig(); // Reload config after update
          return { success: true };
        })
      );
  }

  /**
   * Get current version
   */
  getCurrentVersion(): string {
    return this.version;
  }

  /**
   * Get base CDN URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}
