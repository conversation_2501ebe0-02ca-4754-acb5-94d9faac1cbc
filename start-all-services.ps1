#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Starts all HypeHive services (Backend, SSR Frontend, Discord Bot) with proper logging
.DESCRIPTION
    This script starts the backend server, SSR frontend server, and Discord bot concurrently
    with comprehensive logging and error handling.
.PARAMETER Mode
    Startup mode: 'dev' for development, 'prod' for production
.PARAMETER LogLevel
    Log level: 'info', 'debug', 'warn', 'error'
.PARAMETER RestartOnFailure
    Whether to restart services on failure
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet('dev', 'prod')]
    [string]$Mode = 'dev',
    
    [Parameter(Mandatory=$false)]
    [ValidateSet('info', 'debug', 'warn', 'error')]
    [string]$LogLevel = 'info',
    
    [Parameter(Mandatory=$false)]
    [switch]$RestartOnFailure
)

# Configuration
$SCRIPT_DIR = $PSScriptRoot
$LOG_DIR = Join-Path $SCRIPT_DIR "logs"
$BACKEND_DIR = Join-Path $SCRIPT_DIR "backend"
$FRONTEND_DIR = Join-Path $SCRIPT_DIR "hype-hive"
$DISCORD_BOT_DIR = Join-Path $SCRIPT_DIR "discord-bot"

# Create logs directory if it doesn't exist
if (-not (Test-Path $LOG_DIR)) {
    New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
}

# Log file paths
$MASTER_LOG = Join-Path $LOG_DIR "services-master.log"
$BACKEND_LOG = Join-Path $LOG_DIR "backend.log"
$FRONTEND_LOG = Join-Path $LOG_DIR "frontend.log"
$DISCORD_LOG = Join-Path $LOG_DIR "discord-bot.log"

# Service tracking
$Services = @{
    'Backend' = $null
    'Frontend' = $null
    'DiscordBot' = $null
}

# Colors for output
$Colors = @{
    'Info' = 'Green'
    'Warning' = 'Yellow'
    'Error' = 'Red'
    'Debug' = 'Cyan'
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = 'Info',
        [string]$Service = 'Master'
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] [$Service] $Message"
    
    # Write to master log
    Add-Content -Path $MASTER_LOG -Value $logEntry
    
    # Write to console with color
    $color = $Colors[$Level]
    if ($color) {
        Write-Host $logEntry -ForegroundColor $color
    } else {
        Write-Host $logEntry
    }
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..." -Level 'Info'
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Log "Node.js version: $nodeVersion" -Level 'Info'
    } catch {
        Write-Log "Node.js not found. Please install Node.js." -Level 'Error'
        return $false
    }
    
    # Check Python
    try {
        $pythonVersion = python --version
        Write-Log "Python version: $pythonVersion" -Level 'Info'
    } catch {
        Write-Log "Python not found. Please install Python." -Level 'Error'
        return $false
    }
    
    # Check required directories
    $requiredDirs = @($BACKEND_DIR, $FRONTEND_DIR, $DISCORD_BOT_DIR)
    foreach ($dir in $requiredDirs) {
        if (-not (Test-Path $dir)) {
            Write-Log "Required directory not found: $dir" -Level 'Error'
            return $false
        }
    }
    
    Write-Log "All prerequisites met." -Level 'Info'
    return $true
}

function Install-Dependencies {
    Write-Log "Installing dependencies..." -Level 'Info'
    
    # Install backend dependencies
    Write-Log "Installing backend dependencies..." -Level 'Info'
    Push-Location $BACKEND_DIR
    try {
        npm install 2>&1 | Tee-Object -FilePath $BACKEND_LOG -Append
        Write-Log "Backend dependencies installed successfully" -Level 'Info'
    } catch {
        Write-Log "Failed to install backend dependencies: $_" -Level 'Error'
        Pop-Location
        return $false
    }
    Pop-Location
    
    # Install frontend dependencies
    Write-Log "Installing frontend dependencies..." -Level 'Info'
    Push-Location $FRONTEND_DIR
    try {
        npm install 2>&1 | Tee-Object -FilePath $FRONTEND_LOG -Append
        Write-Log "Frontend dependencies installed successfully" -Level 'Info'
    } catch {
        Write-Log "Failed to install frontend dependencies: $_" -Level 'Error'
        Pop-Location
        return $false
    }
    Pop-Location
    
    # Install Discord bot dependencies
    Write-Log "Installing Discord bot dependencies..." -Level 'Info'
    Push-Location $DISCORD_BOT_DIR
    try {
        python -m pip install -r requirements.txt 2>&1 | Tee-Object -FilePath $DISCORD_LOG -Append
        Write-Log "Discord bot dependencies installed successfully" -Level 'Info'
    } catch {
        Write-Log "Failed to install Discord bot dependencies: $_" -Level 'Error'
        Pop-Location
        return $false
    }
    Pop-Location
    
    return $true
}

function Start-BackendService {
    Write-Log "Starting backend service..." -Level 'Info' -Service 'Backend'
    
    Push-Location $BACKEND_DIR
    try {
        $cmd = if ($Mode -eq 'dev') { 'npm run dev' } else { 'npm start' }
        $process = Start-Process -FilePath 'cmd' -ArgumentList '/c', $cmd -RedirectStandardOutput $BACKEND_LOG -RedirectStandardError $BACKEND_LOG -PassThru -NoNewWindow
        $Services['Backend'] = $process
        Write-Log "Backend service started (PID: $($process.Id))" -Level 'Info' -Service 'Backend'
        return $true
    } catch {
        Write-Log "Failed to start backend service: $_" -Level 'Error' -Service 'Backend'
        return $false
    } finally {
        Pop-Location
    }
}

function Start-FrontendService {
    Write-Log "Starting frontend SSR service..." -Level 'Info' -Service 'Frontend'
    
    Push-Location $FRONTEND_DIR
    try {
        if ($Mode -eq 'dev') {
            $cmd = 'npm run start:local'
        } else {
            # Build first for production
            Write-Log "Building frontend for production..." -Level 'Info' -Service 'Frontend'
            npm run build:prod 2>&1 | Tee-Object -FilePath $FRONTEND_LOG -Append
            $cmd = 'npm run serve:ssr:hype-hive'
        }
        
        $process = Start-Process -FilePath 'cmd' -ArgumentList '/c', $cmd -RedirectStandardOutput $FRONTEND_LOG -RedirectStandardError $FRONTEND_LOG -PassThru -NoNewWindow
        $Services['Frontend'] = $process
        Write-Log "Frontend service started (PID: $($process.Id))" -Level 'Info' -Service 'Frontend'
        return $true
    } catch {
        Write-Log "Failed to start frontend service: $_" -Level 'Error' -Service 'Frontend'
        return $false
    } finally {
        Pop-Location
    }
}

function Start-DiscordBotService {
    Write-Log "Starting Discord bot service..." -Level 'Info' -Service 'DiscordBot'
    
    Push-Location $DISCORD_BOT_DIR
    try {
        $process = Start-Process -FilePath 'python' -ArgumentList 'start.py' -RedirectStandardOutput $DISCORD_LOG -RedirectStandardError $DISCORD_LOG -PassThru -NoNewWindow
        $Services['DiscordBot'] = $process
        Write-Log "Discord bot service started (PID: $($process.Id))" -Level 'Info' -Service 'DiscordBot'
        return $true
    } catch {
        Write-Log "Failed to start Discord bot service: $_" -Level 'Error' -Service 'DiscordBot'
        return $false
    } finally {
        Pop-Location
    }
}

function Test-ServiceHealth {
    param([string]$ServiceName)
    
    $process = $Services[$ServiceName]
    if ($process -and -not $process.HasExited) {
        return $true
    }
    return $false
}

function Stop-AllServices {
    Write-Log "Stopping all services..." -Level 'Info'
    
    foreach ($serviceName in $Services.Keys) {
        $process = $Services[$serviceName]
        if ($process -and -not $process.HasExited) {
            try {
                $process.Kill()
                Write-Log "$serviceName service stopped" -Level 'Info' -Service $serviceName
            } catch {
                Write-Log "Failed to stop $serviceName service: $_" -Level 'Warning' -Service $serviceName
            }
        }
    }
}

function Monitor-Services {
    Write-Log "Starting service monitoring..." -Level 'Info'
    
    $healthCheckInterval = 10 # seconds
    $maxRestarts = 3
    $restartCounts = @{
        'Backend' = 0
        'Frontend' = 0
        'DiscordBot' = 0
    }
    
    while ($true) {
        Start-Sleep -Seconds $healthCheckInterval
        
        foreach ($serviceName in $Services.Keys) {
            if (-not (Test-ServiceHealth $serviceName)) {
                Write-Log "$serviceName service is not running" -Level 'Warning' -Service $serviceName
                
                if ($RestartOnFailure -and $restartCounts[$serviceName] -lt $maxRestarts) {
                    Write-Log "Attempting to restart $serviceName service (attempt $($restartCounts[$serviceName] + 1)/$maxRestarts)" -Level 'Info' -Service $serviceName
                    
                    $success = switch ($serviceName) {
                        'Backend' { Start-BackendService }
                        'Frontend' { Start-FrontendService }
                        'DiscordBot' { Start-DiscordBotService }
                    }
                    
                    if ($success) {
                        Write-Log "$serviceName service restarted successfully" -Level 'Info' -Service $serviceName
                    } else {
                        $restartCounts[$serviceName]++
                        Write-Log "Failed to restart $serviceName service" -Level 'Error' -Service $serviceName
                    }
                } else {
                    Write-Log "$serviceName service failed permanently or restart disabled" -Level 'Error' -Service $serviceName
                }
            }
        }
        
        # Check if all services are dead
        $allDead = $true
        foreach ($serviceName in $Services.Keys) {
            if (Test-ServiceHealth $serviceName) {
                $allDead = $false
                break
            }
        }
        
        if ($allDead) {
            Write-Log "All services have stopped. Exiting monitor." -Level 'Warning'
            break
        }
    }
}

# Main execution
function Main {
    Write-Log "Starting HypeHive services in $Mode mode..." -Level 'Info'
    Write-Log "Log files will be saved to: $LOG_DIR" -Level 'Info'
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites check failed. Exiting." -Level 'Error'
        exit 1
    }
    
    # Install dependencies
    if (-not (Install-Dependencies)) {
        Write-Log "Dependency installation failed. Exiting." -Level 'Error'
        exit 1
    }
    
    # Start services
    $servicesStarted = 0
    
    if (Start-BackendService) { $servicesStarted++ }
    Start-Sleep -Seconds 3
    
    if (Start-FrontendService) { $servicesStarted++ }
    Start-Sleep -Seconds 3
    
    if (Start-DiscordBotService) { $servicesStarted++ }
    
    if ($servicesStarted -eq 0) {
        Write-Log "No services started successfully. Exiting." -Level 'Error'
        exit 1
    }
    
    Write-Log "Started $servicesStarted out of 3 services" -Level 'Info'
    Write-Log "Services are running. Press Ctrl+C to stop all services." -Level 'Info'
    
    # Set up signal handling
    Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
        Stop-AllServices
    }
    
    # Handle Ctrl+C
    $null = Register-ObjectEvent -InputObject ([System.Console]) -EventName CancelKeyPress -Action {
        Write-Log "Received shutdown signal. Stopping all services..." -Level 'Info'
        Stop-AllServices
        [System.Environment]::Exit(0)
    }
    
    # Start monitoring
    Monitor-Services
}

# Run the main function
try {
    Main
} catch {
    Write-Log "Script execution failed: $_" -Level 'Error'
    Stop-AllServices
    exit 1
}
