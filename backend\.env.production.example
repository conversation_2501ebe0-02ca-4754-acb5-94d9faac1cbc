# Production Environment Configuration

# Server Configuration
PORT=3000
NODE_ENV=production

# MongoDB Configuration (Use MongoDB Atlas or production instance)
MONGODB_URI=mongodb+srv://username:<EMAIL>/hype-hive?retryWrites=true&w=majority

# JWT Configuration (Use strong, unique secret)
JWT_SECRET=your_production_jwt_secret_minimum_32_characters_long
JWT_EXPIRES_IN=7d

# Discord OAuth Configuration
DISCORD_CLIENT_ID=your_production_discord_client_id
DISCORD_CLIENT_SECRET=your_production_discord_client_secret
DISCORD_REDIRECT_URI=https://your-production-domain.com/api/auth/discord/callback
DISCORD_API_ENDPOINT=https://discord.com/api/v10
DISCORD_BOT_TOKEN=your_production_discord_bot_token

# Frontend URL (Production domain)
FRONTEND_URL=https://your-production-domain.com

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760 # 10MB

# Logging Configuration
LOG_LEVEL=info

# Discord Bot Configuration
DISCORD_GUILD_ID=your_production_discord_guild_id
BACKEND_API_URL=https://your-production-api-domain.com/api
