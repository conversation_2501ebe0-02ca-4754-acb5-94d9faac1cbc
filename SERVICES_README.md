# HypeHive Services Management

This directory contains scripts to manage all HypeHive services (Backend, Frontend SSR, Discord Bot) with comprehensive logging and monitoring.

## Available Scripts

### 1. start-all-services.bat (Simple)
Basic batch script for Windows that starts all services in separate windows.

**Usage:**
```cmd
start-all-services.bat
```

**Features:**
- Starts all three services in separate command windows
- Basic logging to files
- Simple error checking
- Easy to use for development

### 2. start-all-services.ps1 (Advanced)
PowerShell script with advanced features for production use.

**Usage:**
```powershell
# Start all services in development mode
.\start-all-services.ps1

# Start in production mode with restart on failure
.\start-all-services.ps1 -Mode prod -RestartOnFailure

# Start with debug logging
.\start-all-services.ps1 -LogLevel debug
```

**Features:**
- Concurrent service startup
- Health monitoring
- Automatic restart on failure
- Comprehensive logging
- Graceful shutdown handling
- Prerequisite checking

### 3. services-manager.ps1 (Professional)
Full-featured service management script with advanced monitoring and control.

**Usage:**
```powershell
# Start all services
.\services-manager.ps1 -Action start -Mode dev

# Start specific services
.\services-manager.ps1 -Action start -Services backend,frontend

# Check service status
.\services-manager.ps1 -Action status

# View logs
.\services-manager.ps1 -Action logs

# View specific service logs with follow
.\services-manager.ps1 -Action logs -Services backend -Follow

# Stop all services
.\services-manager.ps1 -Action stop

# Restart specific service
.\services-manager.ps1 -Action restart -Services discord
```

**Features:**
- Full service lifecycle management
- Real-time health monitoring
- Automatic restart on failure
- Centralized logging
- Process tracking
- Individual service control
- Log following
- Service status reporting

## Service Configuration

### Backend Server
- **Directory:** `backend/`
- **Port:** 3000
- **Development Command:** `npm run dev`
- **Production Command:** `npm start`
- **Health Check:** `http://localhost:3000/health`

### Frontend SSR Server
- **Directory:** `hype-hive/`
- **Port:** 4000
- **Development Command:** `npm run start:local`
- **Production Command:** `npm run serve:ssr:hype-hive`
- **Health Check:** `http://localhost:4000`

### Discord Bot
- **Directory:** `discord-bot/`
- **Command:** `python start.py`
- **No health endpoint** (process monitoring only)

## Log Files

All logs are stored in the `logs/` directory:

- `services-master.log` - Main service management log
- `backend.log` - Backend server logs
- `frontend.log` - Frontend SSR server logs
- `discord-bot.log` - Discord bot logs

## Prerequisites

Before running any scripts, ensure you have:

1. **Node.js** (v18 or higher)
2. **Python** (v3.8 or higher)
3. **NPM dependencies** installed in both `backend/` and `hype-hive/` directories
4. **Python dependencies** installed in `discord-bot/` directory

## Quick Start

1. **For Development (Simple):**
   ```cmd
start-all-services.bat
```

2. **For Development (Advanced):**
   ```powershell
.\services-manager.ps1 -Action start -Mode dev
```

3. **For Production:**
   ```powershell
.\services-manager.ps1 -Action start -Mode prod
```

## Stopping Services

### Using Batch Script
Close all opened command windows manually.

### Using PowerShell Scripts
Press `Ctrl+C` in the script window, or run:
```powershell
.\services-manager.ps1 -Action stop
```

## Monitoring

The advanced scripts provide:
- **Real-time health monitoring** every 30 seconds
- **Automatic restart** on service failure
- **Comprehensive logging** with timestamps
- **Process tracking** with PID management
- **Service status reporting**

## Troubleshooting

### Common Issues

1. **Port already in use:**
   - Check if services are already running
   - Use `.\services-manager.ps1 -Action status` to check
   - Kill existing processes on ports 3000 and 4000

2. **Dependencies not installed:**
   - Run `npm install` in `backend/` and `hype-hive/` directories
   - Run `pip install -r requirements.txt` in `discord-bot/` directory

3. **Python not found:**
   - Ensure Python is installed and in PATH
   - Try `python3` instead of `python` if on macOS/Linux

4. **Permission errors:**
   - Run PowerShell as Administrator
   - Check execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Log Analysis

View logs to diagnose issues:
```powershell
# View all logs
.\services-manager.ps1 -Action logs

# View specific service logs
.\services-manager.ps1 -Action logs -Services backend

# Follow logs in real-time
.\services-manager.ps1 -Action logs -Services backend -Follow
```

## Environment Variables

Make sure to set up environment variables for:
- Database connections
- Discord bot token
- Auth0 configuration
- Other service-specific settings

Check `.env` files in each service directory.

## Development Tips

1. **Use the batch script** for quick local development
2. **Use services-manager.ps1** for production-like testing
3. **Monitor logs** regularly to catch issues early
4. **Set up automatic restart** for long-running testing sessions
5. **Use health checks** to ensure services are responding properly

## Contributing

When adding new services:
1. Update the `$ServiceConfig` in `services-manager.ps1`
2. Add appropriate log file paths
3. Configure health check endpoints if applicable
4. Test with both development and production modes
