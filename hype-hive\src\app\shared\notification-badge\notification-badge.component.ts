import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';

@Component({
  selector: 'app-notification-badge',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatBadgeModule],
  template: `
    <button class="notification-button" [matBadge]="notificationCount" matBadgeColor="warn" [matBadgeHidden]="notificationCount === 0">
      <mat-icon>notifications</mat-icon>
    </button>
  `,
  styles: [`
    .notification-button {
      background: none;
      border: none;
      color: var(--text-inverse);
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .notification-button:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  `]
})
export class NotificationBadgeComponent {
  notificationCount = 0; // This would be populated from a service
}
