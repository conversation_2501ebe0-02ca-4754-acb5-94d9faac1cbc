#!/usr/bin/env node

/**
 * Build optimization script for HypeHive Angular frontend
 * Analyzes bundle sizes and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BuildOptimizer {
  constructor() {
    this.recommendations = [];
    this.metrics = {};
  }

  addRecommendation(category, message, impact = 'medium') {
    this.recommendations.push({ category, message, impact });
  }

  // Analyze bundle sizes
  analyzeBundleSizes() {
    console.log('📦 Analyzing bundle sizes...');
    
    const distPath = path.join(__dirname, '..', 'dist', 'hype-hive');
    
    if (!fs.existsSync(distPath)) {
      this.addRecommendation('Build', 'Run ng build first to analyze bundle sizes', 'high');
      return;
    }
    
    const files = fs.readdirSync(distPath);
    let totalSize = 0;
    const bundles = {};
    
    files.forEach(file => {
      if (file.endsWith('.js') || file.endsWith('.css')) {
        const filePath = path.join(distPath, file);
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        
        bundles[file] = sizeKB;
        totalSize += sizeKB;
        
        // Check for large bundles
        if (file.includes('main') && sizeKB > 500) {
          this.addRecommendation('Bundle Size', 
            `Main bundle is large (${sizeKB}KB) - consider code splitting`, 'high');
        }
        
        if (file.includes('polyfills') && sizeKB > 100) {
          this.addRecommendation('Bundle Size', 
            `Polyfills bundle is large (${sizeKB}KB) - review required polyfills`, 'medium');
        }
        
        if (file.includes('vendor') && sizeKB > 1000) {
          this.addRecommendation('Bundle Size', 
            `Vendor bundle is large (${sizeKB}KB) - consider tree shaking`, 'high');
        }
      }
    });
    
    this.metrics.totalBundleSize = totalSize;
    this.metrics.bundles = bundles;
    
    // General size recommendations
    if (totalSize > 2000) {
      this.addRecommendation('Bundle Size', 
        `Total bundle size is large (${totalSize}KB) - consider optimization strategies`, 'high');
    }
  }

  // Analyze package.json for optimization opportunities
  analyzeDependencies() {
    console.log('📋 Analyzing dependencies...');
    
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const deps = packageJson.dependencies || {};
      
      this.metrics.totalDependencies = Object.keys(deps).length;
      
      // Check for heavy dependencies
      const heavyDeps = {
        '@angular/material': 'Consider using only required Material components',
        'chart.js': 'Consider lighter charting alternatives like Chart.js tree-shaking',
        'rxjs': 'Ensure you\'re using RxJS operators efficiently',
        'zone.js': 'Consider Angular Ivy for better tree-shaking'
      };
      
      Object.entries(heavyDeps).forEach(([dep, recommendation]) => {
        if (deps[dep]) {
          this.addRecommendation('Dependencies', recommendation, 'medium');
        }
      });
      
      // Check for duplicate functionality
      if (deps['moment'] && deps['date-fns']) {
        this.addRecommendation('Dependencies', 
          'Both moment and date-fns detected - choose one for consistency', 'medium');
      }
      
      // Check for Angular version
      const angularVersion = deps['@angular/core'];
      if (angularVersion && !angularVersion.includes('19')) {
        this.addRecommendation('Dependencies', 
          'Consider upgrading to latest Angular version for better performance', 'low');
      }
    }
  }

  // Analyze Angular configuration
  analyzeAngularConfig() {
    console.log('⚙️ Analyzing Angular configuration...');
    
    const angularJsonPath = path.join(__dirname, '..', 'angular.json');
    
    if (fs.existsSync(angularJsonPath)) {
      const angularJson = JSON.parse(fs.readFileSync(angularJsonPath, 'utf8'));
      const buildConfig = angularJson.projects?.['hype-hive']?.architect?.build?.configurations;
      
      if (buildConfig?.production) {
        const prodConfig = buildConfig.production;
        
        // Check optimization settings
        if (!prodConfig.optimization) {
          this.addRecommendation('Build Config', 
            'Enable optimization in production build configuration', 'high');
        }
        
        if (!prodConfig.aot) {
          this.addRecommendation('Build Config', 
            'Enable AOT compilation for better performance', 'high');
        }
        
        if (!prodConfig.buildOptimizer) {
          this.addRecommendation('Build Config', 
            'Enable build optimizer for smaller bundles', 'medium');
        }
        
        if (prodConfig.sourceMap !== false) {
          this.addRecommendation('Build Config', 
            'Disable source maps in production for smaller bundles', 'low');
        }
      }
    }
  }

  // Check for lazy loading opportunities
  analyzeLazyLoading() {
    console.log('🚀 Analyzing lazy loading opportunities...');
    
    const srcPath = path.join(__dirname, '..', 'src', 'app');
    
    if (fs.existsSync(srcPath)) {
      const routingFiles = this.findFiles(srcPath, '-routing.module.ts');
      
      routingFiles.forEach(file => {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for non-lazy loaded routes
        if (content.includes('component:') && !content.includes('loadChildren')) {
          const fileName = path.basename(file);
          this.addRecommendation('Lazy Loading', 
            `Consider lazy loading routes in ${fileName}`, 'medium');
        }
      });
      
      // Check for feature modules
      const moduleFiles = this.findFiles(srcPath, '.module.ts');
      const featureModules = moduleFiles.filter(f => 
        !f.includes('app.module.ts') && !f.includes('shared.module.ts')
      );
      
      if (featureModules.length > 3) {
        this.addRecommendation('Lazy Loading', 
          `${featureModules.length} feature modules detected - ensure they're lazy loaded`, 'medium');
      }
    }
  }

  // Find files with specific pattern
  findFiles(dir, pattern) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.includes('node_modules')) {
          scan(fullPath);
        } else if (stat.isFile() && item.includes(pattern)) {
          files.push(fullPath);
        }
      });
    };
    
    scan(dir);
    return files;
  }

  // Generate build optimization commands
  generateOptimizationCommands() {
    const commands = [
      'ng build --configuration=production --build-optimizer --aot',
      'ng build --configuration=production --source-map=false',
      'ng build --configuration=production --optimization=true',
      'ng build --configuration=production --vendor-chunk=false',
      'ng build --configuration=production --common-chunk=false'
    ];
    
    return commands;
  }

  // Generate report
  generateReport() {
    console.log('\n📊 Build Optimization Report\n');
    console.log('=' .repeat(50));
    
    // Metrics
    if (Object.keys(this.metrics).length > 0) {
      console.log('\n📈 METRICS:');
      Object.entries(this.metrics).forEach(([key, value]) => {
        if (typeof value === 'object') {
          console.log(`   ${key}:`);
          Object.entries(value).forEach(([subKey, subValue]) => {
            console.log(`     ${subKey}: ${subValue}${key.includes('Size') ? 'KB' : ''}`);
          });
        } else {
          console.log(`   ${key}: ${value}${key.includes('Size') ? 'KB' : ''}`);
        }
      });
    }
    
    // High impact recommendations
    const highImpact = this.recommendations.filter(r => r.impact === 'high');
    if (highImpact.length > 0) {
      console.log('\n🚀 HIGH IMPACT RECOMMENDATIONS:');
      highImpact.forEach(rec => {
        console.log(`   ⚡ [${rec.category}] ${rec.message}`);
      });
    }
    
    // Medium impact recommendations
    const mediumImpact = this.recommendations.filter(r => r.impact === 'medium');
    if (mediumImpact.length > 0) {
      console.log('\n📈 MEDIUM IMPACT RECOMMENDATIONS:');
      mediumImpact.forEach(rec => {
        console.log(`   🔧 [${rec.category}] ${rec.message}`);
      });
    }
    
    // Low impact recommendations
    const lowImpact = this.recommendations.filter(r => r.impact === 'low');
    if (lowImpact.length > 0) {
      console.log('\n💡 LOW IMPACT RECOMMENDATIONS:');
      lowImpact.forEach(rec => {
        console.log(`   💡 [${rec.category}] ${rec.message}`);
      });
    }
    
    // Optimization commands
    console.log('\n🛠️ OPTIMIZATION COMMANDS:');
    const commands = this.generateOptimizationCommands();
    commands.forEach(cmd => {
      console.log(`   ${cmd}`);
    });
    
    // Summary
    console.log('\n📋 SUMMARY:');
    console.log(`   High Impact Recommendations: ${highImpact.length}`);
    console.log(`   Medium Impact Recommendations: ${mediumImpact.length}`);
    console.log(`   Low Impact Recommendations: ${lowImpact.length}`);
    
    if (this.recommendations.length === 0) {
      console.log('\n🎉 No optimization recommendations found!');
    } else {
      console.log('\n✅ Review recommendations to improve build performance.');
    }
  }

  // Run all optimizations
  async runOptimization() {
    console.log('⚡ Starting HypeHive Frontend Build Optimization\n');
    
    this.analyzeBundleSizes();
    this.analyzeDependencies();
    this.analyzeAngularConfig();
    this.analyzeLazyLoading();
    
    this.generateReport();
  }
}

// Run the optimization
if (require.main === module) {
  const optimizer = new BuildOptimizer();
  optimizer.runOptimization().catch(console.error);
}

module.exports = BuildOptimizer;
