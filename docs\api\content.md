# Content API

The Content API provides endpoints for managing educational content, including creation, retrieval, updating, and interactions such as likes and comments.

## Endpoints

### Get All Content

Retrieves a list of all content with optional filtering and sorting.

```
GET /api/content
```

#### Query Parameters

| Parameter  | Type    | Required | Description                                                                |
|------------|---------|----------|----------------------------------------------------------------------------|
| page       | integer | No       | Page number (default: 1)                                                   |
| limit      | integer | No       | Number of items per page (default: 10)                                     |
| category   | string  | No       | Filter by category                                                         |
| isPublished| boolean | No       | Filter by published status (true, false)                                   |
| createdBy  | string  | No       | Filter by creator ID                                                       |
| search     | string  | No       | Search in title, description, and tags                                     |
| difficulty | string  | No       | Filter by difficulty (beginner, intermediate, advanced)                    |
| sort       | string  | No       | Sort order (newest, oldest, popular, likes)                                |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "title": "Getting Started with Twitch Streaming",
      "description": "A beginner's guide to setting up your Twitch stream",
      "category": "Account Setup",
      "videoUrl": "https://example.com/video.mp4",
      "embedUrl": "https://youtube.com/embed/abc123",
      "thumbnailUrl": "https://example.com/thumbnail.jpg",
      "createdBy": {
        "id": "60d21b4667d0d8992e610c86",
        "discordUsername": "mentor_username",
        "discordAvatar": "avatar_hash",
        "isMentor": true
      },
      "isPublished": true,
      "tags": ["beginner", "setup", "twitch"],
      "views": 120,
      "likes": 45,
      "difficulty": "beginner",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
    // More content...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

### Get Content by ID

Retrieves a specific content item by its ID.

```
GET /api/content/:id
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "title": "Getting Started with Twitch Streaming",
    "description": "A beginner's guide to setting up your Twitch stream",
    "category": "Account Setup",
    "videoUrl": "https://example.com/video.mp4",
    "embedUrl": "https://youtube.com/embed/abc123",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "createdBy": {
      "id": "60d21b4667d0d8992e610c86",
      "discordUsername": "mentor_username",
      "discordAvatar": "avatar_hash",
      "isMentor": true
    },
    "isPublished": true,
    "tags": ["beginner", "setup", "twitch"],
    "views": 121, // Incremented on view
    "likes": 45,
    "difficulty": "beginner",
    "comments": [
      {
        "id": "60d21b4667d0d8992e610c87",
        "user": {
          "id": "60d21b4667d0d8992e610c88",
          "discordUsername": "commenter_username",
          "discordAvatar": "avatar_hash"
        },
        "text": "This was really helpful, thanks!",
        "createdAt": "2023-01-02T00:00:00.000Z"
      }
      // More comments...
    ],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Create Content

Creates a new content item. This endpoint is restricted to mentors and administrators.

```
POST /api/content
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "title": "Getting Started with Twitch Streaming",
  "description": "A beginner's guide to setting up your Twitch stream",
  "category": "Account Setup",
  "videoUrl": "https://example.com/video.mp4",
  "embedUrl": "https://youtube.com/embed/abc123",
  "thumbnailUrl": "https://example.com/thumbnail.jpg",
  "isPublished": true,
  "tags": ["beginner", "setup", "twitch"],
  "difficulty": "beginner"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "title": "Getting Started with Twitch Streaming",
    "description": "A beginner's guide to setting up your Twitch stream",
    "category": "Account Setup",
    "videoUrl": "https://example.com/video.mp4",
    "embedUrl": "https://youtube.com/embed/abc123",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "createdBy": "60d21b4667d0d8992e610c86",
    "isPublished": true,
    "tags": ["beginner", "setup", "twitch"],
    "views": 0,
    "likes": 0,
    "difficulty": "beginner",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  "message": "Content created successfully"
}
```

### Update Content

Updates an existing content item. Users can only update their own content unless they are administrators.

```
PUT /api/content/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "category": "Account Setup",
  "videoUrl": "https://example.com/updated-video.mp4",
  "embedUrl": "https://youtube.com/embed/updated123",
  "thumbnailUrl": "https://example.com/updated-thumbnail.jpg",
  "isPublished": true,
  "tags": ["beginner", "setup", "twitch", "updated"],
  "difficulty": "intermediate"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "title": "Updated Title",
    "description": "Updated description",
    "category": "Account Setup",
    "videoUrl": "https://example.com/updated-video.mp4",
    "embedUrl": "https://youtube.com/embed/updated123",
    "thumbnailUrl": "https://example.com/updated-thumbnail.jpg",
    "createdBy": "60d21b4667d0d8992e610c86",
    "isPublished": true,
    "tags": ["beginner", "setup", "twitch", "updated"],
    "difficulty": "intermediate",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  },
  "message": "Content updated successfully"
}
```

### Delete Content

Deletes a content item. Users can only delete their own content unless they are administrators.

```
DELETE /api/content/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "message": "Content deleted successfully"
}
```

### Like/Unlike Content

Toggles a like on a content item.

```
POST /api/content/:id/like
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "likes": 46,
    "liked": true
  },
  "message": "Like toggled successfully"
}
```

### Add Comment

Adds a comment to a content item.

```
POST /api/content/:id/comment
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "text": "This was really helpful, thanks!"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c87",
    "user": "60d21b4667d0d8992e610c88",
    "text": "This was really helpful, thanks!",
    "createdAt": "2023-01-02T00:00:00.000Z"
  },
  "message": "Comment added successfully"
}
```
