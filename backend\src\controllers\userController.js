const userService = require('../services/userService');
const logger = require('../config/logger');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { isSystemAdmin } = require('../middleware/security');

/**
 * Get all users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllUsers = catchAsync(async (req, res, next) => {
  const result = await userService.getAllUsers(req.query);
  // Map users to include `id` property for front-end
  const usersWithId = result.users.map(user => ({
    ...user,
    id: user._id.toString()
  }));
  res.status(200).json({
    success: true,
    data: usersWithId,
    pagination: result.pagination
  });
});

/**
 * Get user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserById = catchAsync(async (req, res, next) => {
  const user = await userService.getUserById(req.params.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user
  });
});

/**
 * Update user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateUserProfile = catchAsync(async (req, res, next) => {
  // Check if user is updating their own profile, is an admin, or is a system admin
  const isSelf = req.user._id.toString() === req.params.id;
  const isAdmin = req.user.role === 'admin';
  const isSystemAdminUser = isSystemAdmin(req.user.discordId);
  
  if (!isSelf && !isAdmin && !isSystemAdminUser) {
    return next(new AppError('Not authorized to update this profile', 403));
  }

  const user = await userService.updateUserProfile(req.params.id, req.body);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user,
    message: 'Profile updated successfully'
  });
});

/**
 * Apply for mentor status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const applyForMentor = catchAsync(async (req, res, next) => {
  const user = await userService.applyForMentor(req.user._id);

  res.status(200).json({
    success: true,
    data: user,
    message: 'Mentor application submitted successfully'
  });
});

/**
 * Review mentor application
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const reviewMentorApplication = catchAsync(async (req, res, next) => {
  const { status } = req.body;

  if (!status || !['approved', 'rejected'].includes(status)) {
    return next(new AppError('Invalid status. Must be "approved" or "rejected"', 400));
  }

  const user = await userService.reviewMentorApplication(
    req.params.id,
    status,
    req.user._id
  );

  res.status(200).json({
    success: true,
    data: user,
    message: `Mentor application ${status}`
  });
});

/**
 * Get mentors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMentors = catchAsync(async (req, res, next) => {
  // Add isMentor filter to query
  req.query.isMentor = 'true';

  const result = await userService.getAllUsers(req.query);

  res.status(200).json({
    success: true,
    data: result.users,
    pagination: result.pagination
  });
});

module.exports = {
  getAllUsers,
  getUserById,
  updateUserProfile,
  applyForMentor,
  reviewMentorApplication,
  getMentors
};
