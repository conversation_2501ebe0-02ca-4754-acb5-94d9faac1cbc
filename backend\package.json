{"name": "hype-hive-backend", "version": "1.0.0", "description": "Backend for HypeHive Twitch streaming education platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "node scripts/test.js", "test:unit": "node scripts/test.js unit", "test:integration": "node scripts/test.js integration", "test:watch": "node scripts/test.js watch", "test:coverage": "node scripts/test.js coverage", "test:ci": "node scripts/test.js ci", "test:help": "node scripts/test.js help", "coverage:report": "node scripts/coverage-report.js", "coverage:open": "open coverage/lcov-report/index.html", "setup:admin": "node scripts/setup-admin.js", "check:admin": "node scripts/check-admin.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "eslint": "^9.30.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}