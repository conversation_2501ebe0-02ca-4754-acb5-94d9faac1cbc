.content-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  
  mat-icon { font-size: 48px; color: #666; margin-bottom: 16px; }
  h2 { margin: 16px 0 8px; color: #333; }
  p { color: #666; margin-bottom: 24px; }
}

.content-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
  
  .back-button { margin-top: 8px; color: #6366f1; }
  .header-info { flex: 1; }
  h1 { margin: 0 0 12px; font-size: 2rem; font-weight: 600; color: #1a1a1a; line-height: 1.2; }
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  
  .category-chip { background-color: #6366f1; color: white; }
  .difficulty-beginner { background-color: #10b981; color: white; }
  .difficulty-intermediate { background-color: #f59e0b; color: white; }
  .difficulty-advanced { background-color: #ef4444; color: white; }
  .views, .created-date { 
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 0.9rem;
  }
}

mat-divider { margin-bottom: 24px; }

.video-section, .thumbnail-section { margin-bottom: 24px; }

.video-card, .thumbnail-card {
  padding: 0;
  overflow: hidden;
}

.embed-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
}

.embed-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.direct-video, .content-thumbnail {
  width: 100%;
  height: auto;
  max-height: 600px;
  object-fit: cover;
}

.content-info { margin-bottom: 24px; }

.info-card mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.creator-avatar, .current-user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.creator-details h3 { margin: 0; font-size: 1.1rem; font-weight: 600; }

.mentor-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6366f1;
  font-size: 0.9rem;
  font-weight: 500;
}

.like-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.description {
  margin-bottom: 24px;
  h2 { margin: 0 0 12px; font-size: 1.3rem; font-weight: 600; color: #1a1a1a; }
  p { line-height: 1.6; color: #4a4a4a; margin: 0; }
}

.tags-section h3 { margin: 0 0 12px; font-size: 1.1rem; font-weight: 600; color: #1a1a1a; }

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-chip { background-color: #f3f4f6; color: #374151; font-size: 0.85rem; }

.comments-card mat-card-header h2 { margin: 0; font-size: 1.3rem; font-weight: 600; color: #1a1a1a; }

.comment-form { margin-bottom: 24px; }

.comment-form-header {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.current-user-avatar { width: 40px; height: 40px; flex-shrink: 0; }

.comment-form-content { flex: 1; }

.comment-input { width: 100%; margin-bottom: 12px; }

.comment-input textarea { resize: vertical; min-height: 80px; }

.comment-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.comment-form-actions button { min-width: 80px; }

.login-prompt, .no-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.login-prompt {
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 24px;
}

.login-prompt mat-icon { color: #1976d2; }
.no-comments mat-icon { color: #9ca3af; }

.login-prompt p, .no-comments p { color: #6b7280; margin: 0 0 16px; font-size: 1rem; }

.comment {
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.comment:last-child { border-bottom: none; }

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.comment-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-author { font-weight: 600; color: #1a1a1a; }

.comment-date { color: #6b7280; font-size: 0.85rem; }

.comment-text {
  margin: 0;
  line-height: 1.6;
  color: #4a4a4a;
  padding-left: 44px;
  white-space: pre-wrap;
  word-break: break-word;
}

@media (max-width: 768px) {
  .content-detail-container { padding: 16px; }
  
  .header-info h1 { font-size: 1.5rem; }
  
  .content-meta {
    gap: 8px;
    .views, .created-date { font-size: 0.8rem; }
  }
  
  .info-card mat-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .like-button { width: 100%; justify-content: center; }
  
  .comment-form-header { gap: 12px; }
  .current-user-avatar { width: 32px; height: 32px; }
  
  .comment-form-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .comment-form-actions button { width: 100%; }
  
  .comment-header { gap: 8px; }
  .comment-avatar { width: 28px; height: 28px; }
  .comment-author { font-size: 0.9rem; }
  .comment-date { font-size: 0.8rem; }
  .comment-text { padding-left: 36px; font-size: 0.9rem; }
}