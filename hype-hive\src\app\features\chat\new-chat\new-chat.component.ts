import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription, Observable, of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs/operators';
import { ChatService } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { User } from '../../../core/models/user.model';
import { ChatCreationData } from '../../../core/models/chat.model';

@Component({
  selector: 'app-new-chat',
  templateUrl: './new-chat.component.html',
  styleUrls: ['./new-chat.component.scss'],
  standalone: false
})
export class NewChatComponent implements OnInit, OnDestroy {
  chatForm: FormGroup;
  isLoading = false;
  isSearching = false;
  errorMessage = '';
  searchResults: User[] = [];
  selectedUsers: User[] = [];
  
  private subscriptions: Subscription[] = [];
  private searchTerms = new Subject<string>();

  constructor(
    private fb: FormBuilder,
    private chatService: ChatService,
    private userService: UserService,
    private router: Router
  ) {
    this.chatForm = this.fb.group({
      searchTerm: [''],
      isGroupChat: [false],
      groupName: ['', [Validators.minLength(3), Validators.maxLength(50)]]
    });
  }

  ngOnInit(): void {
    // Set up search
    this.subscriptions.push(
      this.searchTerms.pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap(term => {
          if (!term || term.length < 2) {
            return of([]);
          }
          
          this.isSearching = true;
          return this.userService.searchUsers(term).pipe(
            catchError(() => {
              return of([]);
            })
          );
        })
      ).subscribe(results => {
        this.searchResults = results;
        this.isSearching = false;
      })
    );
    
    // Update group name validator based on isGroupChat value
    this.subscriptions.push(
      this.chatForm.get('isGroupChat')!.valueChanges.subscribe(isGroupChat => {
        const groupNameControl = this.chatForm.get('groupName');
        
        if (isGroupChat) {
          groupNameControl!.setValidators([Validators.required, Validators.minLength(3), Validators.maxLength(50)]);
        } else {
          groupNameControl!.clearValidators();
          groupNameControl!.setValidators([Validators.minLength(3), Validators.maxLength(50)]);
        }
        
        groupNameControl!.updateValueAndValidity();
      })
    );
  }

  /**
   * Search for users
   * @param term Search term
   */
  search(term: string): void {
    this.searchTerms.next(term);
  }

  /**
   * Add user to selected users
   * @param user User to add
   */
  addUser(user: User): void {
    // Check if user is already selected
    if (this.selectedUsers.some(u => u._id === user._id)) {
      return;
    }
    
    // Add user to selected users
    this.selectedUsers.push(user);
    
    // Clear search term
    this.chatForm.get('searchTerm')!.setValue('');
    this.searchResults = [];
  }

  /**
   * Remove user from selected users
   * @param userId User ID to remove
   */
  removeUser(userId: string): void {
    this.selectedUsers = this.selectedUsers.filter(user => user._id !== userId);
  }

  /**
   * Create a new chat
   */
  createChat(): void {
    if (this.selectedUsers.length === 0) {
      this.errorMessage = 'Please select at least one user';
      return;
    }
    
    const isGroupChat = this.chatForm.get('isGroupChat')!.value;
    
    if (isGroupChat && !this.chatForm.get('groupName')!.valid) {
      this.errorMessage = 'Please enter a valid group name';
      return;
    }
    
    this.isLoading = true;
    this.errorMessage = '';
    
    // Prepare chat data
    const chatData: ChatCreationData = {
      participants: this.selectedUsers
        .map(user => user._id)
        .filter((id): id is string => typeof id === 'string'),
      isGroupChat: isGroupChat
    };
    
    if (isGroupChat) {
      chatData.groupName = this.chatForm.get('groupName')!.value;
    }
    
    // Create chat
    this.subscriptions.push(
      this.chatService.createChat(chatData).subscribe({
        next: (chat) => {
          this.isLoading = false;
          this.router.navigate(['/chat', chat._id]);
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to create chat';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Cancel and go back to chat list
   */
  cancel(): void {
    this.router.navigate(['/chat']);
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
