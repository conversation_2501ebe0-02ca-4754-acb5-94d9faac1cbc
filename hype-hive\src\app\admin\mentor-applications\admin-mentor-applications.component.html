<div *ngIf="loading">Loading...</div>
<div *ngIf="error">{{ error }}</div>
<table *ngIf="applications.length">
  <thead>
    <tr>
      <th>Name</th>
      <th>Discord ID</th>
      <th>Proficiencies</th>
      <th>Status</th>
      <th>User</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let app of applications">
      <td>{{ app.name }}</td>
      <td>{{ app.discord_id }}</td>
      <td>{{ app.proficiencies.join(', ') }}</td>
      <td>{{ app.status }}</td>
      <td>{{ app.user?.username || app.user?._id }}</td>
      <td>
        <button (click)="approve(app._id)" [disabled]="app.status !== 'pending'">Approve</button>
        <button (click)="reject(app._id)" [disabled]="app.status !== 'pending'">Reject</button>
      </td>
    </tr>
  </tbody>
</table>
<div *ngIf="!loading && !applications.length">No applications found.</div>
