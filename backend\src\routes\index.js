const express = require('express');
const authRoutes = require('./authRoutes');
const userRoutes = require('./userRoutes');
const contentRoutes = require('./contentRoutes');
const uploadRoutes = require('./uploadRoutes');
const chatRoutes = require('./chatRoutes');
const notificationRoutes = require('./notificationRoutes');
const mentorRoutes = require('./mentorRoutes');
const recommendationRoutes = require('./recommendationRoutes');
const discordBotRoutes = require('./discordBotRoutes');
const errorRoutes = require('./errorRoutes');
const monitoringRoutes = require('./monitoringRoutes');
const rateLimitRoutes = require('./rateLimitRoutes');
const adminRoutes = require('./adminRoutes');

const router = express.Router();
const socketAuthRoutes = require('./socketAuth');
const { cacheStatsMiddleware, clearCacheMiddleware } = require('../middleware/cache');
const { performanceStatsMiddleware, healthCheckMiddleware } = require('../middleware/performance');

// Health check route with performance data
router.get('/health', healthCheckMiddleware);

// Alias for legacy healthcheck endpoint
router.get('/healthcheck', healthCheckMiddleware);

// Performance metrics endpoint
router.get('/performance', performanceStatsMiddleware);

// Cache management endpoints
router.get('/cache/stats', cacheStatsMiddleware);
router.post('/cache/clear', clearCacheMiddleware);

// Mount routes
router.use('/auth', authRoutes);
router.use('/auth', socketAuthRoutes); // Mount socket authentication routes
router.use('/users', userRoutes);
router.use('/content', contentRoutes);
router.use('/upload', uploadRoutes);
router.use('/chat', chatRoutes);
router.use('/notifications', notificationRoutes);
router.use('/mentors', mentorRoutes);
router.use('/recommendations', recommendationRoutes);
router.use('/discord-bot', discordBotRoutes);
router.use('/errors', errorRoutes);
router.use('/monitoring', monitoringRoutes);
router.use('/admin/rate-limit', rateLimitRoutes);
router.use('/admin', adminRoutes);

module.exports = router;
