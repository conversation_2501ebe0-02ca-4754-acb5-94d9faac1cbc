.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary-blue-50) 0%, var(--purple-50) 50%, var(--orange-50) 100%);
}

.login-card {
  max-width: 500px;
  width: 100%;
  padding: 2rem;
  box-shadow: var(--shadow-xl);
  border-radius: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
}

mat-card-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

mat-card-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

mat-card-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.login-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--text-secondary);
  text-align: center;
}

.redirect-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--primary-blue-50);
  border: 1px solid var(--primary-blue-200);
  border-radius: 8px;

  .info-icon {
    color: var(--primary-blue-600);
    font-size: 1.25rem;
    flex-shrink: 0;
  }

  .info-message {
    margin: 0;
    font-size: 0.95rem;
    color: var(--primary-blue-700);
    line-height: 1.4;
  }
}

.discord-button {
  background: var(--gradient-blue-purple);
  color: var(--text-inverse);
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
  width: 100%;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--purple-dark) 100%);
  }
}

.discord-button mat-icon {
  margin-right: 0.5rem;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 1rem;
  color: #6441a5;
}