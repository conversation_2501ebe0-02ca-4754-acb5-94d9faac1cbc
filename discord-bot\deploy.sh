#!/bin/bash

# HypeHive Discord Bot Deployment Script
# This script handles deployment of the Discord bot to various environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="hive-discord-bot"
IMAGE_NAME="hype-hive/discord-bot"
CONTAINER_NAME="hive-discord-bot"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        log_error ".env file not found. Please create one based on .env.example"
        exit 1
    fi
    
    # Check if required environment variables are set
    source "$SCRIPT_DIR/.env"
    if [ -z "$DISCORD_BOT_TOKEN" ]; then
        log_error "DISCORD_BOT_TOKEN is not set in .env file"
        exit 1
    fi
    
    if [ -z "$DISCORD_GUILD_ID" ]; then
        log_error "DISCORD_GUILD_ID is not set in .env file"
        exit 1
    fi
    
    log_success "All requirements met"
}

build_image() {
    log_info "Building Docker image..."
    
    cd "$SCRIPT_DIR"
    docker build -t "$IMAGE_NAME:latest" .
    
    log_success "Docker image built successfully"
}

deploy_local() {
    log_info "Deploying Discord bot locally..."
    
    # Stop existing container if running
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "Stopping existing container..."
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
    
    # Create necessary directories
    mkdir -p "$SCRIPT_DIR/logs"
    mkdir -p "$SCRIPT_DIR/data"
    
    # Run the container
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --env-file "$SCRIPT_DIR/.env" \
        -v "$SCRIPT_DIR/logs:/app/logs" \
        -v "$SCRIPT_DIR/data:/app/data" \
        "$IMAGE_NAME:latest"
    
    log_success "Discord bot deployed locally"
}

deploy_compose() {
    log_info "Deploying with Docker Compose..."
    
    cd "$SCRIPT_DIR"
    docker-compose down
    docker-compose up -d --build
    
    log_success "Discord bot deployed with Docker Compose"
}

check_health() {
    log_info "Checking bot health..."
    
    # Wait for container to start
    sleep 10
    
    # Check if container is running
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_success "Container is running"
        
        # Check logs for successful startup
        if docker logs "$CONTAINER_NAME" 2>&1 | grep -q "Bot is ready"; then
            log_success "Bot started successfully"
        else
            log_warning "Bot may not have started properly. Check logs:"
            docker logs "$CONTAINER_NAME" --tail 20
        fi
    else
        log_error "Container is not running"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi
}

show_logs() {
    log_info "Showing bot logs..."
    docker logs -f "$CONTAINER_NAME"
}

stop_bot() {
    log_info "Stopping Discord bot..."
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        log_success "Bot stopped"
    else
        log_warning "Bot is not running"
    fi
}

show_status() {
    log_info "Bot status:"
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        echo -e "${GREEN}Status: Running${NC}"
        docker ps -f name="$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo -e "${RED}Status: Stopped${NC}"
    fi
}

show_help() {
    echo "HypeHive Discord Bot Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build       Build Docker image"
    echo "  deploy      Deploy bot locally with Docker"
    echo "  compose     Deploy with Docker Compose"
    echo "  health      Check bot health"
    echo "  logs        Show bot logs"
    echo "  stop        Stop the bot"
    echo "  status      Show bot status"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build && $0 deploy    # Build and deploy locally"
    echo "  $0 compose               # Deploy with Docker Compose"
    echo "  $0 logs                  # View logs"
}

# Main script logic
case "${1:-help}" in
    "build")
        check_requirements
        build_image
        ;;
    "deploy")
        check_requirements
        build_image
        deploy_local
        check_health
        ;;
    "compose")
        check_requirements
        deploy_compose
        check_health
        ;;
    "health")
        check_health
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop_bot
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac
