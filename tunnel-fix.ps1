# HypeHive Tunnel Troubleshooting Script
# This script helps diagnose and fix common tunnel connectivity issues

param(
    [switch]$CheckOnly,
    [switch]$StartServices,
    [switch]$LocalMode,
    [switch]$TunnelMode
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Define colors for output
function Write-ColorOutput($ForegroundColor, $Message) {
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Header
Write-ColorOutput "Green" "==========================================="
Write-ColorOutput "Green" "  HypeHive Tunnel Troubleshooting Tool"
Write-ColorOutput "Green" "==========================================="
Write-Output ""

# Function to check if a port is in use
function Test-Port {
    param($Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to check if a URL is accessible
function Test-Url {
    param($Url, $Timeout = 10)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $Timeout -UseBasicParsing -ErrorAction Stop
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = $null
            Error = $_.Exception.Message
        }
    }
}

# Check current status
Write-ColorOutput "Cyan" "Checking current system status..."
Write-Output ""

# Check if services are running
$frontendRunning = Test-Port -Port 4200
$backendRunning = Test-Port -Port 3000

Write-Output "Local Services:"
Write-Output "  Frontend (4200): $(if($frontendRunning) { "✅ Running" } else { "❌ Not running" })"
Write-Output "  Backend (3000):  $(if($backendRunning) { "✅ Running" } else { "❌ Not running" })"
Write-Output ""

# Check tunnel connectivity
Write-Output "Tunnel Connectivity:"
$tunnelCheck = Test-Url -Url "https://hypehive.linksynk.info" -Timeout 5
Write-Output "  Tunnel: $(if($tunnelCheck.Success) { "✅ Connected (Status: $($tunnelCheck.StatusCode))" } else { "❌ Failed - $($tunnelCheck.Error)" })"
Write-Output ""

# Check cloudflared process
$cloudflaredProcess = Get-Process -Name "cloudflared" -ErrorAction SilentlyContinue
Write-Output "Cloudflared Process:"
Write-Output "  Status: $(if($cloudflaredProcess) { "✅ Running (PID: $($cloudflaredProcess.Id))" } else { "❌ Not running" })"
Write-Output ""

# If check only mode, exit here
if ($CheckOnly) {
    Write-ColorOutput "Yellow" "Check complete. Use -StartServices to start missing services."
    exit
}

# Recommendations and actions
Write-ColorOutput "Yellow" "Diagnosis and Recommendations:"
Write-Output ""

if (-not $frontendRunning -and -not $backendRunning) {
    Write-ColorOutput "Red" "❌ Neither frontend nor backend is running"
    Write-Output "   Recommendation: Start both services"
    
    if ($StartServices) {
        Write-ColorOutput "Cyan" "Starting services..."
        
        # Start backend
        Write-Output "Starting backend server..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\backend'; npm start" -WindowStyle Normal
        Start-Sleep 3
        
        # Start frontend  
        Write-Output "Starting frontend server..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\hype-hive'; npm start" -WindowStyle Normal
        Start-Sleep 5
        
        Write-ColorOutput "Green" "✅ Services started. Check the new terminal windows."
    }
}
elseif (-not $frontendRunning) {
    Write-ColorOutput "Yellow" "⚠️ Frontend not running, backend is running"
    if ($StartServices) {
        Write-Output "Starting frontend server..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\hype-hive'; npm start" -WindowStyle Normal
    }
}
elseif (-not $backendRunning) {
    Write-ColorOutput "Yellow" "⚠️ Backend not running, frontend is running"
    if ($StartServices) {
        Write-Output "Starting backend server..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\backend'; npm start" -WindowStyle Normal
    }
}

if (-not $tunnelCheck.Success -and $frontendRunning -and $backendRunning) {
    Write-ColorOutput "Yellow" "⚠️ Local services running but tunnel is down"
    Write-Output "   Options:"
    Write-Output "   1. Check cloudflared tunnel configuration"
    Write-Output "   2. Restart cloudflared service"
    Write-Output "   3. Switch to local development mode"
    
    if ($LocalMode) {
        Write-ColorOutput "Cyan" "Switching to local development mode..."
        # Copy local environment
        Copy-Item "$PSScriptRoot\hype-hive\src\environments\environment.local.ts" "$PSScriptRoot\hype-hive\src\environments\environment.ts" -Force
        Write-ColorOutput "Green" "✅ Switched to local development configuration"
        Write-Output "   Frontend will now use http://localhost:3000 for API calls"
    }
}

if ($TunnelMode -and $tunnelCheck.Success) {
    Write-ColorOutput "Cyan" "Switching to tunnel mode..."
    # Restore tunnel environment
    $tunnelEnv = @"
export const environment = {
  production: false,
  discord: {
    clientId: '1374526014595792937',
    redirectUri: 'https://hypehive.linksynk.info/api/auth/discord/callback',
    apiEndpoint: 'https://discord.com/api/v10'
  },
  apiUrl: 'https://hypehive.linksynk.info/api',
  socketUrl: 'https://hypehive.linksynk.info',
  monitoring: {
    errorTrackingEnabled: true,
    analyticsEnabled: true,
    socketMetricsEnabled: true
  },
  tunnel: {
    enabled: true,
    domain: 'hypehive.linksynk.info',
    localPort: 4200,
    backendPort: 3000
  }
};
"@
    Set-Content "$PSScriptRoot\hype-hive\src\environments\environment.ts" -Value $tunnelEnv
    Write-ColorOutput "Green" "✅ Switched to tunnel configuration"
}

Write-Output ""
Write-ColorOutput "Green" "Troubleshooting complete!"
Write-Output ""
Write-Output "Available commands:"
Write-Output "  .\tunnel-fix.ps1 -CheckOnly           # Check status only"
Write-Output "  .\tunnel-fix.ps1 -StartServices       # Start missing services"
Write-Output "  .\tunnel-fix.ps1 -LocalMode           # Switch to local development"
Write-Output "  .\tunnel-fix.ps1 -TunnelMode          # Switch to tunnel mode"
Write-Output ""
