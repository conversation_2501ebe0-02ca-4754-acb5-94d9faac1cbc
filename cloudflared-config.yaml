tunnel: f75924f7-fd4a-463e-92c3-2f29a3833a0c
credentials-file: C:\Users\<USER>\.cloudflared\f75924f7-fd4a-463e-92c3-2f29a3833a0c.json
ingress:
  # Socket.IO endpoint - must come before API endpoint to avoid path conflicts
  - hostname: hypehive.linksynk.info
    path: /socket.io/
    service: http://localhost:3000
    originRequest:
      noTLSVerify: true
      connectTimeout: 30s
      keepAliveTimeout: 2m
      tcpKeepAlive: 30s
      enableWebSockets: true  # Explicitly enable WebSockets
  
  # API endpoints
  - hostname: hypehive.linksynk.info
    path: /api
    service: http://localhost:3000
    originRequest:
      noTLSVerify: true
      connectTimeout: 10s
      keepAliveTimeout: 30s
  
  # Frontend application
  - hostname: hypehive.linksynk.info
    service: http://localhost:4200
    originRequest:
      noTLSVerify: true
      connectTimeout: 10s
  
  # Default fallback
  - service: http_status:404