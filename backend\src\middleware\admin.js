const axios = require('axios');
const User = require('../models/User');
const AppError = require('../utils/appError');
const { DISCORD_BOT_TOKEN, GUILD_ID } = require('../config/discord');

module.exports = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('+discordId');

    if (!user || !user.discordId) {
      return next(new AppError('You are not connected to Discord.', 403));
    }

    const response = await axios.get(
      `https://discord.com/api/v10/guilds/${GUILD_ID}/members/${user.discordId}`,
      {
        headers: {
          Authorization: `Bot ${DISCORD_BOT_TOKEN}`,
        },
      }
    );

    const member = response.data;
    const adminRole = member.roles.find((role) => role.name === 'Admins');

    if (!adminRole) {
      return next(new AppError('You do not have permission to perform this action.', 403));
    }

    next();
  } catch (error) {
    console.error('Error in admin middleware:', error);
    return next(new AppError('Something went wrong while verifying your permissions.', 500));
  }
};
