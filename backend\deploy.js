/**
 * Deployment script for HypeHive backend
 * 
 * This script prepares the application for deployment to production.
 * It can be run with: node deploy.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load environment variables from .env if present
try {
  require('dotenv').config({ path: path.join(__dirname, '.env') });
  console.log('Loaded environment variables from .env');
} catch (e) {
  console.warn('dotenv not installed, skipping .env loading');
}

// Configuration
const config = {
  // Directories to create
  directories: [
    'logs',
    'uploads',
    'uploads/thumbnails',
    'uploads/videos'
  ],
  
  // Environment variables to check
  requiredEnvVars: [
    'NODE_ENV',
    'PORT',
    'MONGODB_URI',
    'JWT_SECRET',
    'DISCORD_CLIENT_ID',
    'DISCORD_CLIENT_SECRET',
    'DISCORD_REDIRECT_URI',
    'FRONTEND_URL'
  ]
};

/**
 * Create required directories
 */
function createDirectories() {
  console.log('Creating required directories...');
  
  config.directories.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`Created directory: ${dir}`);
    } else {
      console.log(`Directory already exists: ${dir}`);
    }
  });
  
  console.log('Directories created successfully.');
}

/**
 * Check required environment variables
 */
function checkEnvironmentVariables() {
  console.log('Checking environment variables...');
  
  const missingVars = [];
  
  config.requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  });
  
  if (missingVars.length > 0) {
    console.error('Error: Missing required environment variables:');
    missingVars.forEach(envVar => {
      console.error(`- ${envVar}`);
    });
    console.error('Please set these environment variables before deploying.');
    process.exit(1);
  }
  
  console.log('All required environment variables are set.');
}

/**
 * Install production dependencies
 */
function installDependencies() {
  console.log('Installing production dependencies...');
  
  try {
    execSync('npm ci --only=production', { stdio: 'inherit' });
    console.log('Dependencies installed successfully.');
  } catch (error) {
    console.error('Error installing dependencies:', error.message);
    process.exit(1);
  }
}

/**
 * Run database migrations (if needed)
 */
function runMigrations() {
  console.log('Running database migrations...');
  
  // Add migration logic here if needed
  
  console.log('Database migrations completed successfully.');
}

/**
 * Main deployment function
 */
function deploy() {
  console.log('Starting deployment process...');
  
  // Create required directories
  createDirectories();
  
  // Check environment variables
  checkEnvironmentVariables();
  
  // Install dependencies
  installDependencies();
  
  // Run migrations
  runMigrations();
  
  console.log('Deployment completed successfully!');
  console.log('You can now start the server with: npm start');
}

// Run deployment
deploy();
