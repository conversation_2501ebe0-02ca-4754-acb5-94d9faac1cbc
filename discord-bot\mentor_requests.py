import discord
from discord import app_commands, Interaction, ButtonStyle
from discord.ext import commands
import json
import os
from hive_helper import make_api_request, update_mentor_list

PENDING_FILE = 'pending_mentors.json'
PENDING_CHANNEL_NAME = 'Pending-mentors'
ADMIN_ROLE_NAME = 'Admins'

# Helper to load/save pending requests
def load_pending():
    if not os.path.exists(PENDING_FILE):
        return []
    with open(PENDING_FILE, 'r') as f:
        return json.load(f)

def save_pending(pending):
    with open(PENDING_FILE, 'w') as f:
        json.dump(pending, f)

class MentorRequestView(discord.ui.View):
    def __init__(self, user_id):
        super().__init__(timeout=None)
        self.user_id = user_id

    @discord.ui.button(label='Approve', style=ButtonStyle.success, custom_id='approve_mentor')
    async def approve(self, interaction: Interaction, button: discord.ui.Button):
        if not any(role.name == ADMIN_ROLE_NAME for role in interaction.user.roles):
            await interaction.response.send_message('You do not have permission.', ephemeral=True)
            return
        await handle_mentor_decision(interaction, self.user_id, True)

    @discord.ui.button(label='Deny', style=ButtonStyle.danger, custom_id='deny_mentor')
    async def deny(self, interaction: Interaction, button: discord.ui.Button):
        if not any(role.name == ADMIN_ROLE_NAME for role in interaction.user.roles):
            await interaction.response.send_message('You do not have permission.', ephemeral=True)
            return
        await handle_mentor_decision(interaction, self.user_id, False)

async def handle_mentor_decision(interaction, user_id, approved):
    pending = load_pending()
    pending = [p for p in pending if p['user_id'] != user_id]
    save_pending(pending)
    user = await interaction.client.fetch_user(user_id)
    if approved:
        # First sync user data to ensure they exist in the database
        try:
            sync_result = await make_api_request(
                'POST',
                'discord-bot/sync-user',
                {
                    'discordId': str(user_id),
                    'discordUsername': user.display_name,
                    'discordAvatar': str(user.avatar.url) if user.avatar else None
                }
            )

            if not sync_result.get('success'):
                print(f"User sync failed: {sync_result}")
                await user.send('Your mentor request was approved, but there was an error updating the website. Please contact support.')
                await interaction.response.send_message(f'Approved <@{user_id}> as mentor, but user sync failed: {sync_result.get("message", "Unknown error")}', ephemeral=True)
                return

        except Exception as e:
            print(f"User sync exception: {e}")
            await user.send('Your mentor request was approved, but there was an error updating the website.')
            await interaction.response.send_message(f'Approved <@{user_id}> as mentor, but user sync failed: {e}', ephemeral=True)
            return

        # Now call API to approve mentor
        try:
            result = await make_api_request(
                'POST',
                'discord-bot/approve-mentor',
                {'discordId': str(user_id)}
            )
            if result.get('success'):
                await user.send(
                    'Your mentor request has been approved!\n'
                    'You can log in at https://hypehive.linksynk.info\n'
                    'Please update your profile on the website with your proficiency list so it can be reflected in the server and website mentors list.'
                )
                await interaction.response.send_message(f'Approved <@{user_id}> as mentor.', ephemeral=True)
            else:
                error_detail = result.get('message') or result.get('error') or result
                print(f"Mentor approval API error: {error_detail}")
                await user.send('Your mentor request was approved, but there was an error updating the website. Please contact support.')
                await interaction.response.send_message(f'Approved <@{user_id}> as mentor, but backend update failed: {error_detail}', ephemeral=True)
        except Exception as e:
            print(f"Mentor approval exception: {e}")
            await user.send('Your mentor request was approved, but an error occurred updating the website.')
            await interaction.response.send_message(f'Approved <@{user_id}> as mentor, but backend update failed: {e}', ephemeral=True)
    else:
        # First sync user data to ensure they exist in the database
        try:
            sync_result = await make_api_request(
                'POST',
                'discord-bot/sync-user',
                {
                    'discordId': str(user_id),
                    'discordUsername': user.display_name,
                    'discordAvatar': str(user.avatar.url) if user.avatar else None
                }
            )

            if not sync_result.get('success'):
                print(f"User sync failed for denial: {sync_result}")
                await user.send('Your mentor request was denied, but there was an error updating the website. Please contact support.')
                await interaction.response.send_message(f'Denied <@{user_id}> as mentor, but user sync failed: {sync_result.get("message", "Unknown error")}', ephemeral=True)
                return

        except Exception as e:
            print(f"User sync exception for denial: {e}")
            await user.send('Your mentor request was denied, but there was an error updating the website.')
            await interaction.response.send_message(f'Denied <@{user_id}> as mentor, but user sync failed: {e}', ephemeral=True)
            return

        # Now call API to deny mentor request
        try:
            result = await make_api_request(
                'POST',
                'discord-bot/deny-mentor',
                {'discordId': str(user_id)}
            )
            if result.get('success'):
                await user.send('Your mentor request has been denied.')
                await interaction.response.send_message(f'Denied <@{user_id}> as mentor.', ephemeral=True)
            else:
                error_detail = result.get('message') or result.get('error') or result
                print(f"Mentor denial API error: {error_detail}")
                await user.send('Your mentor request was denied, but there was an error updating the website. Please contact support.')
                await interaction.response.send_message(f'Denied <@{user_id}> as mentor, but backend update failed: {error_detail}', ephemeral=True)
        except Exception as e:
            print(f"Mentor denial exception: {e}")
            await user.send('Your mentor request was denied, but an error occurred updating the website.')
            await interaction.response.send_message(f'Denied <@{user_id}> as mentor, but backend update failed: {e}', ephemeral=True)
    await update_pending_channel(interaction.client)
    # Update mentor list in frontend (Discord channel)
    if hasattr(interaction.guild, 'id'):
        await update_mentor_list(interaction.guild)

def get_pending_channel(guild):
    for channel in guild.text_channels:
        if channel.name.lower() == PENDING_CHANNEL_NAME.lower():
            return channel
    return None

async def update_pending_channel(bot):
    for guild in bot.guilds:
        channel = get_pending_channel(guild)
        if not channel:
            continue
        pending = load_pending()
        if not pending:
            await channel.purge(limit=10)
            await channel.send('No pending mentor requests.')
            continue
        await channel.purge(limit=10)
        for req in pending:
            user_id = req['user_id']
            view = MentorRequestView(user_id)
            await channel.send(f'<@{user_id}> requested mentor status.', view=view)

class MentorRequest(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        # Register the slash commands with the command tree
        self.bot.tree.add_command(self.request_mentor)
        self.bot.tree.add_command(self.clear_pending_mentors)
        # Register on_ready listener
        self.bot.add_listener(self.on_ready)

    async def on_ready(self):
        # Register commands for the specific guild for instant appearance
        try:
            from hive_helper import GUILD_ID
            guild = discord.Object(id=GUILD_ID)
            self.bot.tree.copy_global_to(guild=guild)
            await self.bot.tree.sync(guild=guild)
            print('MentorRequest commands synced for guild.')
        except Exception as e:
            print(f"Failed to sync mentor commands for guild: {e}")

    @app_commands.command(name='requestmentor', description='Request mentor status')
    async def request_mentor(self, interaction: Interaction):
        user_id = interaction.user.id
        pending = load_pending()
        if any(p['user_id'] == user_id for p in pending):
            await interaction.response.send_message('You already have a pending mentor request.', ephemeral=True)
            return
        pending.append({'user_id': user_id})
        save_pending(pending)
        await interaction.response.send_message('Your mentor request has been submitted!', ephemeral=True)
        await update_pending_channel(interaction.client)

    @app_commands.command(name='clear_pending_mentors', description='Clear all pending mentor requests (Admin only)')
    async def clear_pending_mentors(self, interaction: Interaction):
        # Only allow admins to clear
        if not any(role.name == ADMIN_ROLE_NAME for role in interaction.user.roles):
            await interaction.response.send_message('You do not have permission to clear pending mentors.', ephemeral=True)
            return
        save_pending([])
        await update_pending_channel(interaction.client)
        await interaction.response.send_message('All pending mentor requests have been cleared.', ephemeral=True)

async def setup(bot):
    await bot.add_cog(MentorRequest(bot))
    await update_pending_channel(bot)