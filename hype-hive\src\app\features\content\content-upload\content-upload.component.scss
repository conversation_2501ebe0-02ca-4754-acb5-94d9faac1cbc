.content-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    
    h1 {
      margin: 0 0 0 16px;
      font-size: 28px;
      font-weight: 500;
      color: #333;
    }
  }
}

.content-form {
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    mat-card-header {
      margin-bottom: 16px;
      
      mat-card-title {
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }
    }
    
    mat-card-content {
      padding-top: 0;
    }
  }
  
  .full-width {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .half-width {
    width: calc(50% - 8px);
    margin-bottom: 16px;
    
    &:first-child {
      margin-right: 16px;
    }
    
    @media (max-width: 768px) {
      width: 100%;
      margin-right: 0;
    }
  }
}

// File Upload Styles
.file-upload-section {
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
  }
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  transition: border-color 0.3s ease;
  
  &:hover {
    border-color: #007bff;
  }
  
  .upload-button {
    margin-bottom: 16px;
    
    mat-icon {
      margin-right: 8px;
    }
  }
  
  .upload-hint {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.file-preview {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    
    .file-name {
      font-weight: 500;
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .video-preview {
    width: 100%;
    max-height: 300px;
    display: block;
  }
  
  .thumbnail-preview {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    display: block;
  }
}

// Tags Styles
.tags-section {
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
  }
  
  .tag-input-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    
    .tag-input {
      flex: 1;
    }
  }
  
  .tags-list {
    margin-bottom: 8px;
    
    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    mat-chip {
      background-color: #e3f2fd;
      color: #1976d2;
      
      mat-icon {
        color: #1976d2;
      }
    }
  }
  
  .tags-hint {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

// Publishing Styles
.publishing-section {
  .publishing-hint {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 14px;
  }
}

// Progress Styles
.progress-section {
  margin-bottom: 20px;
  
  .upload-progress {
    text-align: center;
    
    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
    
    mat-progress-bar {
      margin-bottom: 8px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
}

// Form Actions
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 0;
  border-top: 1px solid #eee;
  margin-top: 20px;
  
  button {
    min-width: 120px;
    
    mat-spinner {
      margin-right: 8px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .content-upload-container {
    padding: 16px;
    
    .header h1 {
      font-size: 24px;
    }
  }
  
  .content-form .form-row {
    grid-template-columns: 1fr;
  }
  
  .file-upload-area {
    padding: 30px 16px;
  }
  
  .tags-section .tag-input-container {
    flex-direction: column;
    align-items: stretch;
    
    button {
      align-self: flex-start;
    }
  }
  
  .form-actions {
    flex-direction: column-reverse;
    
    button {
      width: 100%;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .content-upload-container .header h1 {
    color: #fff;
  }
  
  .form-section mat-card-header mat-card-title {
    color: #fff;
  }
  
  .file-upload-section h3,
  .tags-section h3 {
    color: #ccc;
  }
  
  .file-upload-area {
    border-color: #555;
    
    &:hover {
      border-color: #007bff;
    }
    
    .upload-hint {
      color: #aaa;
    }
  }
  
  .file-preview .preview-header {
    background-color: #333;
    border-color: #555;
    
    .file-name {
      color: #fff;
    }
  }
  
  .tags-section .tags-hint,
  .publishing-section .publishing-hint,
  .progress-section .upload-progress p {
    color: #aaa;
  }
}
