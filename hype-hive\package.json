{"name": "hype-hive", "version": "0.0.6", "scripts": {"ng": "ng", "start": "ng serve", "start:local": "ng serve --configuration=local", "start:tunnel": "ng serve --configuration=development", "build": "ng build", "build:local": "ng build --configuration=local", "build:prod": "npm run version:patch && ng build --configuration=production --verbose", "build:prod:no-ssr": "npm run version:patch && ng build --configuration=production --no-ssr", "build:prod:client-only": "npm run version:patch && ng build --configuration=production --output-mode=static", "build:clean": "npm run clean && ng build --configuration=production", "build:cdn": "npm run build:clean && node scripts/cdn-manager.js generate", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:hype-hive": "node dist/hype-hive/server/server.mjs", "version:patch": "npm version patch --no-git-tag-version", "version:minor": "npm version minor --no-git-tag-version", "version:major": "npm version major --no-git-tag-version", "clean": "node scripts/clean-build.js", "cache:clear": "node scripts/cache-clear.js", "prebuild": "npm run clean", "deploy": "node deploy.js"}, "private": true, "dependencies": {"@angular/animations": "^19.2.11", "@angular/cdk": "^19.2.16", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.16", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.12", "@auth0/auth0-angular": "^2.2.3", "@types/chart.js": "^2.9.41", "chart.js": "^4.4.9", "eslint": "^9.30.1", "express": "^4.18.2", "mongoose": "^8.16.5", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.12", "@angular/cli": "^19.2.12", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "fs-extra": "^11.1.1", "glob": "^8.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "node-fetch": "^3.3.2", "rimraf": "^3.0.2", "typescript": "~5.7.2"}}