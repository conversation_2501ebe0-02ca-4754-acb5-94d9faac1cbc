"""
Error handling utilities for HiveHelper Discord Bot
Provides centralized error handling and logging functionality
"""

import logging
import traceback
import discord
from discord import app_commands, Interaction
from typing import Optional, Any
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)

class BotErrorHandler:
    """Centralized error handling for the Discord bot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.error_count = 0
        self.last_error_time = None
        
    async def handle_command_error(self, interaction: Interaction, error: Exception, command_name: str = "Unknown"):
        """Handle errors from slash commands"""
        self.error_count += 1
        self.last_error_time = datetime.now()
        
        # Log the error with full traceback
        logger.error(f"Command error in /{command_name}: {str(error)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Determine user-friendly error message
        user_message = self._get_user_friendly_message(error)
        
        try:
            if interaction.response.is_done():
                await interaction.followup.send(user_message, ephemeral=True)
            else:
                await interaction.response.send_message(user_message, ephemeral=True)
        except Exception as e:
            logger.error(f"Failed to send error message to user: {e}")
    
    async def handle_api_error(self, error: Exception, endpoint: str = "Unknown") -> dict:
        """Handle API-related errors"""
        logger.error(f"API error for endpoint {endpoint}: {str(error)}")
        
        if "timeout" in str(error).lower():
            logger.warning(f"API timeout for {endpoint}")
            return {"error": "timeout", "message": "API request timed out"}
        elif "connection" in str(error).lower():
            logger.warning(f"Connection error for {endpoint}")
            return {"error": "connection", "message": "Could not connect to API"}
        else:
            logger.error(f"Unexpected API error for {endpoint}: {error}")
            return {"error": "unknown", "message": "Unexpected API error"}
    
    async def handle_discord_error(self, error: Exception, context: str = "Unknown"):
        """Handle Discord API errors"""
        logger.error(f"Discord error in {context}: {str(error)}")
        
        if isinstance(error, discord.Forbidden):
            logger.warning(f"Permission denied in {context}: {error}")
        elif isinstance(error, discord.NotFound):
            logger.warning(f"Resource not found in {context}: {error}")
        elif isinstance(error, discord.HTTPException):
            logger.error(f"Discord HTTP error in {context}: {error}")
        else:
            logger.error(f"Unexpected Discord error in {context}: {error}")
    
    def _get_user_friendly_message(self, error: Exception) -> str:
        """Convert technical errors to user-friendly messages"""
        error_str = str(error).lower()
        
        if "timeout" in error_str:
            return "⏱️ The request timed out. Please try again in a moment."
        elif "connection" in error_str or "network" in error_str:
            return "🌐 Connection error. Please check your internet connection and try again."
        elif "permission" in error_str or "forbidden" in error_str:
            return "🚫 I don't have the necessary permissions to perform this action."
        elif "not found" in error_str:
            return "❓ The requested resource was not found."
        elif "rate limit" in error_str:
            return "⏳ Rate limit exceeded. Please wait a moment before trying again."
        else:
            return "❌ An unexpected error occurred. Please try again later."
    
    async def log_performance_warning(self, operation: str, duration: float, threshold: float = 5.0):
        """Log performance warnings for slow operations"""
        if duration > threshold:
            logger.warning(f"Slow operation detected: {operation} took {duration:.2f} seconds")
    
    def get_error_stats(self) -> dict:
        """Get error statistics"""
        return {
            "total_errors": self.error_count,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None
        }

class RetryHandler:
    """Handle retries for failed operations"""
    
    @staticmethod
    async def retry_with_backoff(func, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        """Retry a function with exponential backoff"""
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                
                delay = min(base_delay * (2 ** attempt), max_delay)
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay:.1f}s: {e}")
                await asyncio.sleep(delay)

class HealthChecker:
    """Monitor bot health and connectivity"""
    
    def __init__(self, bot, backend_url: str):
        self.bot = bot
        self.backend_url = backend_url
        self.last_health_check = None
        self.is_healthy = True
        
    async def check_bot_health(self) -> dict:
        """Check overall bot health"""
        health_status = {
            "bot_connected": self.bot.is_ready(),
            "guilds_count": len(self.bot.guilds),
            "latency": round(self.bot.latency * 1000, 2),  # Convert to ms
            "last_check": datetime.now().isoformat()
        }
        
        # Check if latency is concerning
        if self.bot.latency > 1.0:  # More than 1 second
            logger.warning(f"High bot latency detected: {health_status['latency']}ms")
            health_status["latency_warning"] = True
        
        self.last_health_check = datetime.now()
        return health_status
    
    async def check_backend_connectivity(self) -> dict:
        """Check backend API connectivity"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.backend_url}/health", timeout=5) as response:
                    if response.status == 200:
                        return {"backend_connected": True, "status_code": response.status}
                    else:
                        return {"backend_connected": False, "status_code": response.status}
        except Exception as e:
            logger.error(f"Backend health check failed: {e}")
            return {"backend_connected": False, "error": str(e)}

# Global error handler instance
error_handler = None

def setup_error_handler(bot):
    """Initialize the global error handler"""
    global error_handler
    error_handler = BotErrorHandler(bot)
    return error_handler

def get_error_handler() -> Optional[BotErrorHandler]:
    """Get the global error handler instance"""
    return error_handler

# Decorator for automatic error handling
def handle_errors(command_name: str = None):
    """Decorator to automatically handle errors in bot commands"""
    def decorator(func):
        async def wrapper(interaction: Interaction, *args, **kwargs):
            try:
                return await func(interaction, *args, **kwargs)
            except Exception as e:
                if error_handler:
                    await error_handler.handle_command_error(
                        interaction, e, command_name or func.__name__
                    )
                else:
                    logger.error(f"Error in {func.__name__}: {e}")
                    try:
                        if interaction.response.is_done():
                            await interaction.followup.send(
                                "An unexpected error occurred.", ephemeral=True
                            )
                        else:
                            await interaction.response.send_message(
                                "An unexpected error occurred.", ephemeral=True
                            )
                    except:
                        pass  # Ignore if we can't send error message
        return wrapper
    return decorator
