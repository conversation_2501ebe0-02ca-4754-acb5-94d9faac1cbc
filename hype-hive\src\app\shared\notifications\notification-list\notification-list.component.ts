import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { NotificationService } from '../../../core/services/notification.service';
import { Notification } from '../../../core/models/notification.model';

@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss']
})
export class NotificationListComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  isLoading = true;
  errorMessage = '';
  currentPage = 1;
  hasMoreNotifications = false;
  
  private subscriptions: Subscription[] = [];

  constructor(
    private notificationService: NotificationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadNotifications();
  }

  /**
   * Load notifications
   */
  loadNotifications(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.subscriptions.push(
      this.notificationService.getNotifications(this.currentPage).subscribe({
        next: (result) => {
          this.notifications = result.notifications;
          this.hasMoreNotifications = this.currentPage < result.pagination.pages;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to load notifications';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Load more notifications
   */
  loadMoreNotifications(): void {
    if (this.isLoading || !this.hasMoreNotifications) {
      return;
    }
    
    this.currentPage++;
    this.isLoading = true;
    
    this.subscriptions.push(
      this.notificationService.getNotifications(this.currentPage).subscribe({
        next: (result) => {
          this.notifications = [...this.notifications, ...result.notifications];
          this.hasMoreNotifications = this.currentPage < result.pagination.pages;
          this.isLoading = false;
        },
        error: (error) => {
          this.currentPage--; // Revert page increment
          this.errorMessage = error.message || 'Failed to load more notifications';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notificationService.markAllAsRead().subscribe({
      next: () => {
        // Update local notifications
        this.notifications = this.notifications.map(notification => ({
          ...notification,
          isRead: true
        }));
      },
      error: (error) => {
        console.error('Failed to mark all notifications as read:', error);
      }
    });
  }

  /**
   * Handle notification click
   * @param notification Notification to handle
   */
  handleNotificationClick(notification: Notification): void {
    // Mark as read
    this.notificationService.markAsRead(notification._id).subscribe();
    
    // Navigate based on notification type and related content
    if (notification.relatedContent) {
      const { contentType, contentId } = notification.relatedContent;
      
      switch (contentType) {
        case 'chat':
          this.router.navigate(['/chat', contentId]);
          break;
          
        case 'content':
          this.router.navigate(['/content', contentId]);
          break;
          
        case 'user':
          this.router.navigate(['/profile', contentId]);
          break;
      }
    }
  }

  /**
   * Delete notification
   * @param event Click event
   * @param notificationId Notification ID to delete
   */
  deleteNotification(event: Event, notificationId: string): void {
    event.stopPropagation();
    
    this.notificationService.deleteNotification(notificationId).subscribe({
      next: () => {
        // Remove from local list
        this.notifications = this.notifications.filter(
          notification => notification._id !== notificationId
        );
      },
      error: (error) => {
        console.error('Failed to delete notification:', error);
      }
    });
  }

  /**
   * Format notification time
   * @param date Date to format
   * @returns Formatted time string
   */
  formatTime(date: Date): string {
    if (!date) {
      return '';
    }
    
    const now = new Date();
    const notificationDate = new Date(date);
    const diffMs = now.getTime() - notificationDate.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) {
      return 'just now';
    }
    
    if (diffMins < 60) {
      return `${diffMins}m ago`;
    }
    
    if (diffHours < 24) {
      return `${diffHours}h ago`;
    }
    
    if (diffDays < 7) {
      return `${diffDays}d ago`;
    }
    
    return notificationDate.toLocaleDateString();
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
