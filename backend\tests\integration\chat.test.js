const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/app');
const User = require('../../src/models/User');
const Chat = require('../../src/models/Chat');
const authService = require('../../src/services/authService');
const TestDatabase = require('../helpers/testDb');

describe('Chat API', () => {
  let testDb;
  let user1, user2, user3;
  let user1Token, user2Token, user3Token;

  beforeAll(async () => {
    testDb = new TestDatabase();
    await testDb.connect();
  });

  beforeEach(async () => {
    await testDb.clearDatabase();
    
    // Create test users
    user1 = await User.create({
      discordId: 'test-discord-1',
      discordUsername: 'testuser1',
      discordAvatar: 'avatar1.png',
      email: '<EMAIL>',
      role: 'user'
    });

    user2 = await User.create({
      discordId: 'test-discord-2',
      discordUsername: 'testuser2',
      discordAvatar: 'avatar2.png',
      email: '<EMAIL>',
      role: 'user'
    });

    user3 = await User.create({
      discordId: 'test-discord-3',
      discordUsername: 'testuser3',
      discordAvatar: 'avatar3.png',
      email: '<EMAIL>',
      role: 'mentor'
    });

    // Generate tokens
    user1Token = authService.generateJWT(user1);
    user2Token = authService.generateJWT(user2);
    user3Token = authService.generateJWT(user3);
  });

  afterEach(async () => {
    await testDb.clearDatabase();
  });

  afterAll(async () => {
    await testDb.disconnect();
  });

  describe('POST /api/chat', () => {
    it('should create a direct chat between two users', async () => {
      const chatData = {
        participants: [user2._id.toString()],
        isGroupChat: false
      };

      const response = await request(app)
        .post('/api/chat')
        .set('Authorization', `Bearer ${user1Token}`)
        .send(chatData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('_id');
      expect(response.body.data.participants).toHaveLength(2);
      expect(response.body.data.isGroupChat).toBe(false);
      expect(response.body.data.messages).toHaveLength(0);
    });

    it('should create a group chat with multiple users', async () => {
      const chatData = {
        participants: [user2._id.toString(), user3._id.toString()],
        isGroupChat: true,
        groupName: 'Test Group Chat'
      };

      const response = await request(app)
        .post('/api/chat')
        .set('Authorization', `Bearer ${user1Token}`)
        .send(chatData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.participants).toHaveLength(3);
      expect(response.body.data.isGroupChat).toBe(true);
      expect(response.body.data.groupName).toBe('Test Group Chat');
      expect(response.body.data.groupAdmin.toString()).toBe(user1._id.toString());
    });

    it('should return 400 if no participants provided', async () => {
      const chatData = {
        participants: [],
        isGroupChat: false
      };

      const response = await request(app)
        .post('/api/chat')
        .set('Authorization', `Bearer ${user1Token}`)
        .send(chatData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('participant');
    });

    it('should require authentication', async () => {
      const chatData = {
        participants: [user2._id.toString()],
        isGroupChat: false
      };

      await request(app)
        .post('/api/chat')
        .send(chatData)
        .expect(401);
    });
  });

  describe('GET /api/chat', () => {
    let chat1, chat2;

    beforeEach(async () => {
      // Create test chats
      chat1 = await Chat.create({
        participants: [user1._id, user2._id],
        isGroupChat: false,
        messages: [{
          sender: user1._id,
          content: 'Hello from user1',
          readBy: [user1._id],
          createdAt: new Date()
        }],
        lastMessage: {
          content: 'Hello from user1',
          sender: user1._id,
          createdAt: new Date()
        }
      });

      chat2 = await Chat.create({
        participants: [user1._id, user3._id],
        isGroupChat: false,
        messages: [{
          sender: user3._id,
          content: 'Hello from mentor',
          readBy: [user3._id],
          createdAt: new Date()
        }],
        lastMessage: {
          content: 'Hello from mentor',
          sender: user3._id,
          createdAt: new Date()
        }
      });
    });

    it('should get all chats for authenticated user', async () => {
      const response = await request(app)
        .get('/api/chat')
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toHaveProperty('page');
      expect(response.body.pagination).toHaveProperty('pages');
      expect(response.body.pagination).toHaveProperty('total');
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/chat?page=1&limit=1')
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/chat')
        .expect(401);
    });
  });

  describe('GET /api/chat/:id', () => {
    let chat;

    beforeEach(async () => {
      chat = await Chat.create({
        participants: [user1._id, user2._id],
        isGroupChat: false,
        messages: [{
          sender: user1._id,
          content: 'Test message',
          readBy: [user1._id],
          createdAt: new Date()
        }],
        lastMessage: {
          content: 'Test message',
          sender: user1._id,
          createdAt: new Date()
        }
      });
    });

    it('should get chat by ID for participant', async () => {
      const response = await request(app)
        .get(`/api/chat/${chat._id}`)
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(chat._id.toString());
      expect(response.body.data.messages).toHaveLength(1);
    });

    it('should return 404 for non-participant', async () => {
      await request(app)
        .get(`/api/chat/${chat._id}`)
        .set('Authorization', `Bearer ${user3Token}`)
        .expect(404);
    });

    it('should return 404 for non-existent chat', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      await request(app)
        .get(`/api/chat/${fakeId}`)
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app)
        .get(`/api/chat/${chat._id}`)
        .expect(401);
    });
  });

  describe('POST /api/chat/:id/message', () => {
    let chat;

    beforeEach(async () => {
      chat = await Chat.create({
        participants: [user1._id, user2._id],
        isGroupChat: false,
        messages: [],
        lastMessage: null
      });
    });

    it('should add message to chat', async () => {
      const messageData = {
        content: 'Hello, this is a test message!'
      };

      const response = await request(app)
        .post(`/api/chat/${chat._id}/message`)
        .set('Authorization', `Bearer ${user1Token}`)
        .send(messageData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.content).toBe(messageData.content);
      expect(response.body.data.sender.toString()).toBe(user1._id.toString());
    });

    it('should return 400 if content is empty', async () => {
      const messageData = {
        content: ''
      };

      const response = await request(app)
        .post(`/api/chat/${chat._id}/message`)
        .set('Authorization', `Bearer ${user1Token}`)
        .send(messageData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('content');
    });

    it('should return 404 for non-participant', async () => {
      const messageData = {
        content: 'Test message'
      };

      await request(app)
        .post(`/api/chat/${chat._id}/message`)
        .set('Authorization', `Bearer ${user3Token}`)
        .send(messageData)
        .expect(404);
    });

    it('should require authentication', async () => {
      const messageData = {
        content: 'Test message'
      };

      await request(app)
        .post(`/api/chat/${chat._id}/message`)
        .send(messageData)
        .expect(401);
    });
  });

  describe('PUT /api/chat/:id/read', () => {
    let chat;

    beforeEach(async () => {
      chat = await Chat.create({
        participants: [user1._id, user2._id],
        isGroupChat: false,
        messages: [{
          sender: user2._id,
          content: 'Unread message',
          readBy: [user2._id],
          createdAt: new Date()
        }],
        lastMessage: {
          content: 'Unread message',
          sender: user2._id,
          createdAt: new Date()
        }
      });
    });

    it('should mark messages as read', async () => {
      const response = await request(app)
        .put(`/api/chat/${chat._id}/read`)
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should return 404 for non-participant', async () => {
      await request(app)
        .put(`/api/chat/${chat._id}/read`)
        .set('Authorization', `Bearer ${user3Token}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app)
        .put(`/api/chat/${chat._id}/read`)
        .expect(401);
    });
  });
});
