const webpack = require('webpack');

module.exports = {
  plugins: [
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
        },
        socket: {
          test: /[\\/]node_modules[\\/](socket\.io|engine\.io)/,
          name: 'socket',
          chunks: 'all',
          priority: 20,
        },
        angular: {
          test: /[\\/]node_modules[\\/]@angular[\\/]/,
          name: 'angular',
          chunks: 'all',
          priority: 30,
        },
      },
    },
  },
  resolve: {
    fallback: {
      "buffer": require.resolve("buffer"),
      "crypto": false,
      "stream": false,
      "util": false,
      "url": false,
      "querystring": false,
    }
  }
};
