#!/usr/bin/env python3
"""
Test script for Discord Bot - Backend Integration
Tests all the Discord bot API endpoints
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:3000/api')
TEST_DISCORD_ID = '123456789012345678'  # Test Discord ID
TEST_USERNAME = 'TestUser'

def test_endpoint(method, endpoint, data=None, expected_status=200):
    """Test an API endpoint"""
    url = f"{BACKEND_API_URL}/{endpoint.lstrip('/')}"
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, timeout=10)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        print(f"Testing {method} {endpoint}...")
        print(f"Status: {response.status_code}")
        
        if response.status_code == expected_status:
            try:
                result = response.json()
                print(f"✅ Success: {json.dumps(result, indent=2)}")
                return True
            except:
                print(f"✅ Success (no JSON response)")
                return True
        else:
            print(f"❌ Failed: Expected {expected_status}, got {response.status_code}")
            try:
                error = response.json()
                print(f"Error: {json.dumps(error, indent=2)}")
            except:
                print(f"Error: {response.text}")
            return False
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run integration tests"""
    print("🧪 Discord Bot - Backend Integration Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Health check
    print("\n1. Testing health check...")
    total_tests += 1
    if test_endpoint('GET', 'discord-bot/health'):
        tests_passed += 1
    
    # Test 2: Sync user (create)
    print("\n2. Testing user sync (create)...")
    total_tests += 1
    sync_data = {
        'discordId': TEST_DISCORD_ID,
        'discordUsername': TEST_USERNAME,
        'discordAvatar': 'https://example.com/avatar.png'
    }
    if test_endpoint('POST', 'discord-bot/sync-user', sync_data):
        tests_passed += 1
    
    # Test 3: Get user by Discord ID
    print("\n3. Testing get user by Discord ID...")
    total_tests += 1
    if test_endpoint('GET', f'discord-bot/user/{TEST_DISCORD_ID}'):
        tests_passed += 1
    
    # Test 4: Get mentors
    print("\n4. Testing get mentors...")
    total_tests += 1
    if test_endpoint('GET', 'discord-bot/mentors'):
        tests_passed += 1
    
    # Test 5: Update proficiencies (should fail - user not mentor)
    print("\n5. Testing update proficiencies (should fail - not mentor)...")
    total_tests += 1
    prof_data = {
        'action': 'add',
        'category': 'Account Setup'
    }
    if test_endpoint('PUT', f'discord-bot/user/{TEST_DISCORD_ID}/proficiencies', prof_data, expected_status=403):
        tests_passed += 1
    
    # Test 6: Get non-existent user
    print("\n6. Testing get non-existent user...")
    total_tests += 1
    if test_endpoint('GET', 'discord-bot/user/999999999999999999', expected_status=404):
        tests_passed += 1
    
    # Test 7: Sync user (update)
    print("\n7. Testing user sync (update)...")
    total_tests += 1
    sync_data['discordUsername'] = 'UpdatedTestUser'
    if test_endpoint('POST', 'discord-bot/sync-user', sync_data):
        tests_passed += 1
    
    # Test 8: Invalid sync data
    print("\n8. Testing invalid sync data...")
    total_tests += 1
    invalid_data = {'discordId': TEST_DISCORD_ID}  # Missing username
    if test_endpoint('POST', 'discord-bot/sync-user', invalid_data, expected_status=400):
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Discord bot integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the backend API and database connection.")
        return False

def test_backend_connectivity():
    """Test basic backend connectivity"""
    print("🔌 Testing backend connectivity...")
    
    try:
        # Test main health endpoint
        response = requests.get(f"{BACKEND_API_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running and accessible")
            return True
        else:
            print(f"❌ Backend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        print(f"Make sure the backend is running at {BACKEND_API_URL}")
        return False

if __name__ == '__main__':
    print("Discord Bot Integration Test Suite")
    print(f"Backend URL: {BACKEND_API_URL}")
    print()
    
    # First test basic connectivity
    if not test_backend_connectivity():
        print("\n❌ Backend connectivity test failed. Exiting.")
        exit(1)
    
    # Run integration tests
    success = main()
    
    if success:
        print("\n✅ Integration tests completed successfully!")
        print("The Discord bot should now be able to communicate with the backend.")
    else:
        print("\n❌ Integration tests failed!")
        print("Please check the backend logs and fix any issues before running the Discord bot.")
    
    exit(0 if success else 1)
