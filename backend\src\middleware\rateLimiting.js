const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');

// Get trusted IPs from environment
const getTrustedIPs = () => {
  const trustedIPs = process.env.TRUSTED_IPS;
  if (!trustedIPs) return [];
  return trustedIPs.split(',').map(ip => ip.trim()).filter(Boolean);
};

// Skip rate limiting for trusted IPs
const skipTrustedIPs = (req) => {
  const trustedIPs = getTrustedIPs();
  const clientIP = req.ip || req.connection.remoteAddress;
  
  if (trustedIPs.includes(clientIP)) {
    logger.debug(`Rate limiting skipped for trusted IP: ${clientIP}`);
    return true;
  }
  
  return false;
};

// Rate limiting configuration based on environment
const getRateLimitConfig = (type) => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isTest = process.env.NODE_ENV === 'test';
  
  // Disable rate limiting in test environment
  if (isTest) {
    return { windowMs: 1, max: 1000000 };
  }
  
  const configs = {
    general: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: isProduction ? 100 : 1000, // Limit each IP
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      }
    },
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: isProduction ? 5 : 50, // Stricter limit for auth
      message: {
        error: 'Too many authentication attempts, please try again later.',
        retryAfter: '15 minutes'
      }
    },
    upload: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: isProduction ? 10 : 100, // File upload limit
      message: {
        error: 'Too many file uploads, please try again later.',
        retryAfter: '1 hour'
      }
    },
    contentCreation: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: isProduction ? 20 : 200, // Content creation limit
      message: {
        error: 'Too many content creation requests, please try again later.',
        retryAfter: '1 hour'
      }
    },
    chat: {
      windowMs: 1 * 60 * 1000, // 1 minute
      max: isProduction ? 30 : 300, // Chat message limit
      message: {
        error: 'Too many chat messages, please slow down.',
        retryAfter: '1 minute'
      }
    },
    monitoring: {
      windowMs: 1 * 60 * 1000, // 1 minute
      max: isProduction ? 60 : 600, // Monitoring events limit
      message: {
        error: 'Too many monitoring requests, please try again later.',
        retryAfter: '1 minute'
      }
    }
  };
  
  return configs[type] || configs.general;
};

// Create rate limiter with configuration
const createRateLimiter = (type) => {
  const config = getRateLimitConfig(type);
  
  return rateLimit({
    ...config,
    skip: skipTrustedIPs,
    standardHeaders: true, // Return rate limit info in headers
    legacyHeaders: false, // Disable X-RateLimit-* headers
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip} on ${type} endpoint`);
      res.status(429).json(config.message);
    }
  });
};

// Export rate limiters
const generalLimiter = createRateLimiter('general');
const authLimiter = createRateLimiter('auth');
const uploadLimiter = createRateLimiter('upload');
const contentCreationLimiter = createRateLimiter('contentCreation');
const chatLimiter = createRateLimiter('chat');
const monitoringLimiter = createRateLimiter('monitoring');

// Additional limiters
const mentorApplicationLimiter = createRateLimiter('contentCreation'); // Reuse content creation limits
const passwordResetLimiter = createRateLimiter('auth'); // Reuse auth limits
const profileUpdateLimiter = createRateLimiter('general'); // Reuse general limits

// User-specific limiter factory
const createUserSpecificLimiter = (maxRequests = 100, windowMinutes = 15) => {
  return rateLimit({
    windowMs: windowMinutes * 60 * 1000,
    max: maxRequests,
    skip: skipTrustedIPs,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise fall back to IP
      return req.user?.id || req.ip;
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn(`User-specific rate limit exceeded for user: ${req.user?.id || 'anonymous'} IP: ${req.ip}`);
      res.status(429).json({
        error: 'Too many requests from this account, please try again later.',
        retryAfter: `${windowMinutes} minutes`
      });
    }
  });
};

// Trusted rate limiter (higher limits for trusted sources)
const createTrustedRateLimiter = (type) => {
  const config = getRateLimitConfig(type);
  
  return rateLimit({
    ...config,
    max: config.max * 10, // 10x higher limit for trusted sources
    skip: skipTrustedIPs,
    standardHeaders: true,
    legacyHeaders: false
  });
};

module.exports = {
  generalLimiter,
  authLimiter,
  uploadLimiter,
  contentCreationLimiter,
  chatLimiter,
  mentorApplicationLimiter,
  passwordResetLimiter,
  profileUpdateLimiter,
  monitoringLimiter,
  createUserSpecificLimiter,
  createTrustedRateLimiter,
  getTrustedIPs
};
