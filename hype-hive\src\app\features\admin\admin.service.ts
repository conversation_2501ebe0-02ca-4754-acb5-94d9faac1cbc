import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private apiUrl = '/api/admin';

  constructor(private http: HttpClient) { }

  getStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/stats`).pipe(catchError(this.handleError));
  }

  getRecentUsers(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/recent-users`).pipe(catchError(this.handleError));
  }

  toggleUserStatus(userId: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/users/${userId}/toggle-status`, {}).pipe(catchError(this.handleError));
  }

  getPendingMentorApplications(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/mentor-applications/pending`).pipe(catchError(this.handleError));
  }

  approveMentorApplication(applicationId: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/mentor-applications/${applicationId}/approve`, {}).pipe(catchError(this.handleError));
  }

  rejectMentorApplication(applicationId: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/mentor-applications/${applicationId}/reject`, {}).pipe(catchError(this.handleError));
  }

  deleteContent(contentId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/content/${contentId}`).pipe(catchError(this.handleError));
  }

  editContent(contentId: string, content: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/content/${contentId}`, content).pipe(catchError(this.handleError));
  }

  deleteComment(commentId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/comments/${commentId}`).pipe(catchError(this.handleError));
  }

  editComment(commentId: string, comment: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/comments/${commentId}`, comment).pipe(catchError(this.handleError));
  }

  /**
   * Delete a comment on content
   */
  deleteContentComment(contentId: string, commentId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/content/${contentId}/comment/${commentId}`).pipe(catchError(this.handleError));
  }

  /**
   * Edit a comment on content
   */
  editContentComment(contentId: string, commentId: string, text: string): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/content/${contentId}/comment/${commentId}`, { text }).pipe(catchError(this.handleError));
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred!';
    if (error.error instanceof ErrorEvent) {
      // Client-side errors
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side errors
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    return throwError(() => new Error(errorMessage));
  }
}
