<div class="chat-detail-container">
  <div class="chat-header">
    <button mat-icon-button (click)="goBack()" aria-label="Back">
      <mat-icon>arrow_back</mat-icon>
    </button>
    
    <div class="chat-info" *ngIf="chat">
      <div class="avatar">
        <img [src]="getChatAvatar()" 
             (error)="$any($event.target).src='/helplogo.png'"
             alt="User Avatar">
      </div>
      <div class="name-status">
        <h2>{{ getChatDisplayName() }}</h2>
        <div class="connection-status" *ngIf="!socketConnected && currentUser">
          <mat-icon color="warn">wifi_off</mat-icon>
          <span>Disconnected</span>
        </div>
        <div class="auth-required" *ngIf="!currentUser">
          <mat-icon color="primary">login</mat-icon>
          <span>Login required</span>
        </div>
      </div>
    </div>
  </div>

  <ng-container *ngIf="isLoading && !chat">
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading chat...</p>
    </div>
  </ng-container>

  <ng-container *ngIf="errorMessage">
    <div class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="loadChat()">Try Again</button>
      <button mat-button (click)="goBack()">Go Back</button>
    </div>
  </ng-container>

  <ng-container *ngIf="chat">
    <div class="messages-container" #messageContainer>
      <div class="empty-chat" *ngIf="chat.messages.length === 0">
        <mat-icon>chat</mat-icon>
        <p>No messages yet</p>
        <p class="hint">Type a message below to start the conversation</p>
      </div>

      <div class="message-list" *ngIf="chat.messages.length > 0">
        <app-chat-message 
          *ngFor="let message of chat.messages" 
          [message]="message"
          [isOwnMessage]="isOwnMessage(message)">
        </app-chat-message>
      </div>
    </div>

    <div class="typing-indicator" *ngIf="typingUsers.length > 0">
      <div class="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span class="typing-text">{{ getTypingText() }}</span>
    </div>

    <div class="message-input-container">
      <mat-form-field appearance="outline" class="message-input">
        <input 
          matInput 
          placeholder="Type a message..." 
          [formControl]="messageContent"
          #messageInput
          (keyup.enter)="sendMessage()">
      </mat-form-field>
      
      <button 
        mat-fab 
        color="primary" 
        class="send-button" 
        [disabled]="!messageContent.valid || !socketConnected"
        (click)="sendMessage()">
        <mat-icon>send</mat-icon>
      </button>
    </div>
  </ng-container>
</div>
