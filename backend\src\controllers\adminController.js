const User = require('../models/User');
const Content = require('../models/Content');
const MentorApplication = require('../models/MentorApplication');

// Dashboard statistics
exports.getStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const newUsersThisMonth = await User.countDocuments({
      createdAt: { $gte: new Date(new Date().setDate(1)) },
    });
    const mentors = await User.countDocuments({ role: 'mentor' });
    const pendingApplications = await MentorApplication.countDocuments({ status: 'pending' });

    const totalContent = await Content.countDocuments();
    const publishedContent = await Content.countDocuments({ status: 'published' });
    const contentStats = await Content.aggregate([
      { $group: { _id: null, totalViews: { $sum: '$views' }, totalLikes: { $sum: '$likes' } } },
    ]);

    res.json({
      userStats: {
        totalUsers,
        activeUsers,
        newUsersThisMonth,
        mentors,
        pendingApplications,
      },
      contentStats: {
        totalContent,
        publishedContent,
        totalViews: contentStats[0]?.totalViews || 0,
        totalLikes: contentStats[0]?.totalLikes || 0,
      },
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching dashboard stats', error: error.message });
  }
};

// User management
exports.getRecentUsers = async (req, res) => {
  try {
    const users = await User.find().sort({ createdAt: -1 }).limit(10);
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching recent users', error: error.message });
  }
};

exports.toggleUserStatus = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    user.isActive = !user.isActive;
    await user.save();
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'Error updating user status', error: error.message });
  }
};

// Update user role (admin only)
exports.updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;
    if (!['user', 'mentor', 'admin'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }
    const update = { role, isMentor: role === 'mentor' || role === 'admin' };
    const user = await User.findByIdAndUpdate(id, update, { new: true });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    // Include `id` field for front-end compatibility
    const userObj = user.toObject();
    userObj.id = user._id.toString();
    res.json({ success: true, data: userObj });
  } catch (error) {
    res.status(500).json({ message: 'Error updating user role', error: error.message });
  }
};

// Mentor applications
exports.getPendingMentorApplications = async (req, res) => {
  try {
    const applications = await MentorApplication.find({ status: 'pending' }).populate('user');
    res.json(applications);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching mentor applications', error: error.message });
  }
};

exports.approveMentorApplication = async (req, res) => {
  try {
    const application = await MentorApplication.findByIdAndUpdate(
      req.params.id,
      { status: 'approved' },
      { new: true }
    );
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    await User.findByIdAndUpdate(application.user, { role: 'mentor' });
    res.json(application);
  } catch (error) {
    res.status(500).json({ message: 'Error approving mentor application', error: error.message });
  }
};

exports.rejectMentorApplication = async (req, res) => {
  try {
    const application = await MentorApplication.findByIdAndUpdate(
      req.params.id,
      { status: 'rejected' },
      { new: true }
    );
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    res.json(application);
  } catch (error) {
    res.status(500).json({ message: 'Error rejecting mentor application', error: error.message });
  }
};

// Content management
exports.deleteContent = async (req, res) => {
  try {
    const content = await Content.findByIdAndDelete(req.params.id);
    if (!content) {
      return res.status(404).json({ message: 'Content not found' });
    }
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Error deleting content', error: error.message });
  }
};

exports.editContent = async (req, res) => {
  try {
    const content = await Content.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!content) {
      return res.status(404).json({ message: 'Content not found' });
    }
    res.json(content);
  } catch (error) {
    res.status(500).json({ message: 'Error updating content', error: error.message });
  }
};

// Comment management
exports.deleteComment = async (req, res) => {
  try {
    const comment = await Comment.findByIdAndDelete(req.params.id);
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Error deleting comment', error: error.message });
  }
};

exports.editComment = async (req, res) => {
  try {
    const comment = await Comment.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }
    res.json(comment);
  } catch (error) {
    res.status(500).json({ message: 'Error updating comment', error: error.message });
  }
};
