/**
 * Caching middleware for HypeHive backend
 * Implements in-memory caching for frequently accessed data
 */

const logger = require('../config/logger');

// Simple in-memory cache
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      this.stats.misses++;
      return null;
    }
    
    // Check if expired
    if (item.expiry && Date.now() > item.expiry) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }
    
    this.stats.hits++;
    return item.value;
  }

  set(key, value, ttlSeconds = 300) {
    const expiry = ttlSeconds ? Date.now() + (ttlSeconds * 1000) : null;
    
    this.cache.set(key, {
      value,
      expiry,
      createdAt: Date.now()
    });
    
    this.stats.sets++;
    
    // Clean up expired entries periodically
    if (this.cache.size % 100 === 0) {
      this.cleanup();
    }
  }

  delete(key) {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
    }
    return deleted;
  }

  clear() {
    const size = this.cache.size;
    this.cache.clear();
    this.stats.deletes += size;
  }

  cleanup() {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry && now > item.expiry) {
        this.cache.delete(key);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      logger.debug(`Cache cleanup: removed ${cleaned} expired entries`);
    }
  }

  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;
    
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: `${hitRate}%`
    };
  }
}

// Global cache instance
const cache = new MemoryCache();

/**
 * Cache middleware for API responses
 * @param {number} ttlSeconds - Time to live in seconds
 * @param {function} keyGenerator - Function to generate cache key
 */
const cacheMiddleware = (ttlSeconds = 300, keyGenerator = null) => {
  return (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }
    
    // Generate cache key
    const key = keyGenerator 
      ? keyGenerator(req) 
      : `${req.method}:${req.originalUrl}:${req.user?.id || 'anonymous'}`;
    
    // Try to get from cache
    const cachedResponse = cache.get(key);
    
    if (cachedResponse) {
      logger.debug(`Cache hit for key: ${key}`);
      res.set('X-Cache', 'HIT');
      return res.json(cachedResponse);
    }
    
    // Store original json method
    const originalJson = res.json;
    
    // Override json method to cache response
    res.json = function(data) {
      // Only cache successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        cache.set(key, data, ttlSeconds);
        logger.debug(`Cached response for key: ${key}`);
      }
      
      res.set('X-Cache', 'MISS');
      return originalJson.call(this, data);
    };
    
    next();
  };
};

/**
 * Cache middleware for static content
 */
const staticCacheMiddleware = (maxAge = 3600) => {
  return (req, res, next) => {
    // Set cache headers for static content
    if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
      res.set({
        'Cache-Control': `public, max-age=${maxAge}`,
        'Expires': new Date(Date.now() + maxAge * 1000).toUTCString()
      });
    }
    
    next();
  };
};

/**
 * Cache middleware for user data
 */
const userCacheMiddleware = cacheMiddleware(600, (req) => {
  return `user:${req.user?.id || 'anonymous'}:${req.originalUrl}`;
});

/**
 * Cache middleware for mentor data
 */
const mentorCacheMiddleware = cacheMiddleware(1800, (req) => {
  return `mentors:${req.originalUrl}`;
});

/**
 * Cache middleware for content data
 */
const contentCacheMiddleware = cacheMiddleware(900, (req) => {
  return `content:${req.originalUrl}`;
});

/**
 * Invalidate cache entries by pattern
 */
const invalidateCache = (pattern) => {
  let invalidated = 0;
  
  for (const key of cache.cache.keys()) {
    if (key.includes(pattern)) {
      cache.delete(key);
      invalidated++;
    }
  }
  
  if (invalidated > 0) {
    logger.info(`Invalidated ${invalidated} cache entries matching pattern: ${pattern}`);
  }
  
  return invalidated;
};

/**
 * Cache statistics endpoint middleware
 */
const cacheStatsMiddleware = (req, res) => {
  const stats = cache.getStats();
  res.json({
    success: true,
    cache: stats,
    timestamp: new Date().toISOString()
  });
};

/**
 * Clear cache endpoint middleware
 */
const clearCacheMiddleware = (req, res) => {
  const sizeBefore = cache.cache.size;
  cache.clear();
  
  logger.info(`Cache cleared: ${sizeBefore} entries removed`);
  
  res.json({
    success: true,
    message: `Cleared ${sizeBefore} cache entries`,
    timestamp: new Date().toISOString()
  });
};

// Cleanup expired entries every 5 minutes
setInterval(() => {
  cache.cleanup();
}, 5 * 60 * 1000);

module.exports = {
  cache,
  cacheMiddleware,
  staticCacheMiddleware,
  userCacheMiddleware,
  mentorCacheMiddleware,
  contentCacheMiddleware,
  invalidateCache,
  cacheStatsMiddleware,
  clearCacheMiddleware
};
