const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');

describe('Users API Integration Tests', () => {
  let authToken;
  let testUser;

  beforeEach(async () => {
    // Create test user and get auth token
    testUser = new User(global.testUtils.createTestUser());
    await testUser.save();

    const authService = require('../../src/services/authService');
    authToken = authService.generateJWT(testUser);
  });

  describe('GET /api/users/profile', () => {
    test('should return user profile for authenticated user', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.discordId).toBe(testUser.discordId);
      expect(response.body.username).toBe(testUser.username);
      expect(response.body.proficiencies).toBeDefined();
    });

    test('should return 401 for unauthenticated request', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });
  });

  describe('PUT /api/users/profile', () => {
    test('should update user profile', async () => {
      const updateData = {
        displayName: 'Updated Display Name',
        bio: 'Updated bio',
        proficiencies: {
          streaming: 'intermediate',
          contentCreation: 'advanced',
          communityBuilding: 'beginner'
        }
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.displayName).toBe(updateData.displayName);
      expect(response.body.bio).toBe(updateData.bio);
      expect(response.body.proficiencies.streaming).toBe('intermediate');

      // Verify changes in database
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.displayName).toBe(updateData.displayName);
      expect(updatedUser.proficiencies.streaming).toBe('intermediate');
    });

    test('should validate proficiency levels', async () => {
      const invalidData = {
        proficiencies: {
          streaming: 'invalid_level'
        }
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    test('should not allow updating protected fields', async () => {
      const protectedData = {
        discordId: 'new_discord_id',
        email: '<EMAIL>',
        isMentor: true
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(protectedData)
        .expect(200);

      // Verify protected fields weren't changed
      const user = await User.findById(testUser._id);
      expect(user.discordId).toBe(testUser.discordId);
      expect(user.email).toBe(testUser.email);
      expect(user.isMentor).toBe(false);
    });
  });

  describe('POST /api/users/mentor-application', () => {
    test('should submit mentor application', async () => {
      const applicationData = {
        reason: 'I want to help others learn streaming',
        experience: 'Streaming for 3 years with 10k followers',
        expertise: ['streaming', 'contentCreation'],
        availability: 'weekends'
      };

      const response = await request(app)
        .post('/api/users/mentor-application')
        .set('Authorization', `Bearer ${authToken}`)
        .send(applicationData)
        .expect(200);

      expect(response.body.mentorApplication).toBeDefined();
      expect(response.body.mentorApplication.status).toBe('pending');
      expect(response.body.mentorApplication.reason).toBe(applicationData.reason);

      // Verify in database
      const user = await User.findById(testUser._id);
      expect(user.mentorApplication.status).toBe('pending');
      expect(user.mentorApplication.submittedAt).toBeDefined();
    });

    test('should not allow duplicate applications', async () => {
      // Submit first application
      const applicationData = {
        reason: 'First application',
        experience: 'Some experience',
        expertise: ['streaming'],
        availability: 'weekdays'
      };

      await request(app)
        .post('/api/users/mentor-application')
        .set('Authorization', `Bearer ${authToken}`)
        .send(applicationData)
        .expect(200);

      // Try to submit second application
      const response = await request(app)
        .post('/api/users/mentor-application')
        .set('Authorization', `Bearer ${authToken}`)
        .send(applicationData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    test('should validate required fields', async () => {
      const incompleteData = {
        reason: 'Missing other fields'
      };

      const response = await request(app)
        .post('/api/users/mentor-application')
        .set('Authorization', `Bearer ${authToken}`)
        .send(incompleteData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/users/mentors', () => {
    beforeEach(async () => {
      // Create some mentor users
      const mentor1 = new User({
        ...global.testUtils.createTestMentor(),
        discordId: 'mentor1',
        username: 'mentor1',
        email: '<EMAIL>'
      });

      const mentor2 = new User({
        ...global.testUtils.createTestMentor(),
        discordId: 'mentor2',
        username: 'mentor2',
        email: '<EMAIL>',
        mentorProfile: {
          bio: 'Expert in content creation',
          expertise: ['contentCreation'],
          availability: 'weekdays',
          maxMentees: 3
        }
      });

      await mentor1.save();
      await mentor2.save();
    });

    test('should return list of mentors', async () => {
      const response = await request(app)
        .get('/api/users/mentors')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(2);
      
      response.body.forEach(mentor => {
        expect(mentor.isMentor).toBe(true);
        expect(mentor.mentorProfile).toBeDefined();
      });
    });

    test('should filter mentors by expertise', async () => {
      const response = await request(app)
        .get('/api/users/mentors?expertise=contentCreation')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.length).toBe(1);
      expect(response.body[0].mentorProfile.expertise).toContain('contentCreation');
    });

    test('should allow unauthenticated access to mentor list', async () => {
      const response = await request(app)
        .get('/api/users/mentors')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('GET /api/users/search', () => {
    beforeEach(async () => {
      // Create users with different proficiency levels
      const beginner = new User({
        ...global.testUtils.createTestUser(),
        discordId: 'beginner1',
        username: 'beginner_user',
        email: '<EMAIL>',
        proficiencies: { streaming: 'beginner' }
      });

      const advanced = new User({
        ...global.testUtils.createTestUser(),
        discordId: 'advanced1',
        username: 'advanced_user',
        email: '<EMAIL>',
        proficiencies: { streaming: 'advanced' }
      });

      await beginner.save();
      await advanced.save();
    });

    test('should search users by proficiency level', async () => {
      const response = await request(app)
        .get('/api/users/search?proficiency=streaming&level=beginner')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.length).toBeGreaterThan(0);
      response.body.forEach(user => {
        expect(user.proficiencies.streaming).toBe('beginner');
      });
    });

    test('should search users by username', async () => {
      const response = await request(app)
        .get('/api/users/search?username=beginner')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.length).toBe(1);
      expect(response.body[0].username).toBe('beginner_user');
    });

    test('should return empty array for no matches', async () => {
      const response = await request(app)
        .get('/api/users/search?username=nonexistent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual([]);
    });
  });
});
