import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface Content {
  _id: string;
  title: string;
  description: string;
  category: string;
  videoUrl?: string;
  embedUrl?: string;
  thumbnailUrl?: string;
  createdBy: any;
  isPublished: boolean;
  tags: string[];
  views: number;
  likes: number;
  likedBy: string[];
  comments: any[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  createdAt: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class ContentService {
  private apiUrl = `/api/content`;

  constructor(private http: HttpClient) { }

  /**
   * Get all content
   * @param query Query parameters
   */
  getAllContent(query: any = {}): Observable<{ content: Content[], pagination: any }> {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.keys(query).forEach(key => {
      if (query[key] !== undefined && query[key] !== null) {
        queryParams.append(key, query[key]);
      }
    });

    const url = `${this.apiUrl}?${queryParams.toString()}`;

    return this.http.get<any>(url)
      .pipe(
        map(response => {
          if (response.success) {
            return {
              content: response.data,
              pagination: response.pagination
            };
          } else {
            throw new Error(response.message || 'Failed to get content');
          }
        }),
        catchError(error => {
          console.error('Error getting content:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to get content'));
        })
      );
  }

  /**
   * Get content by ID
   * @param contentId Content ID
   */
  getContentById(contentId: string): Observable<Content> {
    return this.http.get<any>(`${this.apiUrl}/${contentId}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to get content');
          }
        }),
        catchError(error => {
          console.error('Error getting content:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to get content'));
        })
      );
  }

  /**
   * Create new content
   * @param contentData Content data
   */
  createContent(contentData: Partial<Content>): Observable<Content> {
    return this.http.post<any>(this.apiUrl, contentData)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to create content');
          }
        }),
        catchError(error => {
          console.error('Error creating content:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to create content'));
        })
      );
  }

  /**
   * Update content
   * @param contentId Content ID
   * @param contentData Content data to update
   */
  updateContent(contentId: string, contentData: Partial<Content>): Observable<Content> {
    return this.http.put<any>(`${this.apiUrl}/${contentId}`, contentData)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to update content');
          }
        }),
        catchError(error => {
          console.error('Error updating content:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to update content'));
        })
      );
  }

  /**
   * Delete content
   * @param contentId Content ID
   */
  deleteContent(contentId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.apiUrl}/${contentId}`)
      .pipe(
        map(response => {
          if (response.success) {
            return true;
          } else {
            throw new Error(response.message || 'Failed to delete content');
          }
        }),
        catchError(error => {
          console.error('Error deleting content:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to delete content'));
        })
      );
  }

  /**
   * Like or unlike content
   * @param contentId Content ID
   */
  toggleLike(contentId: string): Observable<{ likes: number, liked: boolean }> {
    return this.http.post<any>(`${this.apiUrl}/${contentId}/like`, {})
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to toggle like');
          }
        }),
        catchError(error => {
          console.error('Error toggling like:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to toggle like'));
        })
      );
  }

  /**
   * Add comment to content
   * @param contentId Content ID
   * @param text Comment text
   */
  addComment(contentId: string, text: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/${contentId}/comment`, { text })
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to add comment');
          }
        }),
        catchError(error => {
          console.error('Error adding comment:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to add comment'));
        })
      );
  }
}
