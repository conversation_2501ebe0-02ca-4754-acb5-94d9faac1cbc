@use './logo.scss';

.app-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--purple) 100%);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  overflow: visible;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    position: relative;
    overflow: visible;
  }
}

.logo {
  a {
    text-decoration: none;
    color: var(--text-inverse);

    h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--text-inverse), var(--orange-200));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

.main-nav {
  ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      margin: 0 0.5rem;

      a {
        text-decoration: none;
        color: var(--text-inverse);
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        opacity: 0.9;

        &:hover {
          color: var(--text-inverse);
          background-color: rgba(255, 255, 255, 0.15);
          opacity: 1;
          transform: translateY(-1px);
        }

        &.active {
          color: var(--text-inverse);
          background: var(--orange);
          font-weight: 600;
          opacity: 1;
          box-shadow: var(--shadow-sm);
        }
      }
    }
  }
}

.header-left {
  position: relative;
  display: flex;
  flex-direction: column;

  .logo-container {
    a img {
      height: 50px;
      width: auto;
    }
  }

  .dropdown-nav {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    background: var(--bg-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    padding: 0.5rem 1rem;

    ul {
      display: block;
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        margin: 0.25rem 0;

        a {
          color: var(--text-primary);
          text-decoration: none;
          padding: 0.25rem 0.5rem;
          display: block;

          &:hover, &.active {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 4px;
          }
        }
      }
    }
  }
}

.header-center {
  img {
    max-height: 50px;
    width: auto;
  }
}

.header-right {
  .user-actions {
    display: flex;
    align-items: center;
  }
}

.user-actions {
  display: flex;
  align-items: center;

  > * {
    margin-left: 1rem;
  }

  button {
    background: var(--orange);
    color: var(--text-inverse);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);

    &:hover {
      background: var(--orange-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    mat-icon {
      margin-right: 0.5rem;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }
}

.notifications-container, .user-menu-container {
  position: relative;
  overflow: visible;
}

.notification-trigger, .user-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
  overflow: visible;

  &:hover {
    transform: scale(1.05);
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--orange);
  box-shadow: var(--shadow-sm);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.notifications-dropdown, .user-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  z-index: 1001;
  animation: dropdown-fade 0.2s ease-in-out;
}

.user-dropdown {
  width: 250px;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  overflow: visible;
  border: 1px solid var(--border-light);

  .user-info {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--gradient-blue-purple);
    color: var(--text-inverse);

    .user-avatar-large {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 1rem;
      border: 2px solid var(--orange);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .user-details {
      h3 {
        margin: 0 0 0.25rem;
        font-size: 1rem;
        color: var(--text-inverse);
      }

      .mentor-badge {
        display: inline-block;
        background: var(--orange);
        color: var(--text-inverse);
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .menu-items {
    padding: 0.5rem 0;
    overflow: visible;

    a, button {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      text-decoration: none;
      color: var(--text-primary);
      width: 100%;
      border: none;
      background: none;
      text-align: left;
      cursor: pointer;
      font-family: inherit;
      font-size: 1rem;
      transition: all 0.2s ease;
      overflow: visible;

      &:hover {
        background: var(--bg-secondary);
        color: var(--primary-blue);
      }

      mat-icon {
        margin-right: 0.75rem;
        color: var(--text-muted);
        transition: color 0.2s ease;
        vertical-align: middle;
        flex-shrink: 0;
        font-size: 20px;
        width: 20px;
        height: 20px;
        line-height: 1;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        // Ensure the icon font is properly loaded
        font-family: 'Material Icons' !important;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        font-feature-settings: 'liga';

        // Fix for possible font loading issues
        &:before {
          content: attr(data-icon);
        }
      }

      &:hover mat-icon {
        color: var(--primary-blue);
      }
    }
  }
}

/* Material Icons specific fixes for header dropdown */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

/* Ensure all mat-icon elements in the header have proper font */
.app-header mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
  vertical-align: middle;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 999;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}