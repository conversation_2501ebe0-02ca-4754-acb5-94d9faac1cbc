# HypeHive Tunnel Troubleshooting Guide

This guide helps you diagnose and resolve the tunnel connectivity issues you've been experiencing.

## Problem Analysis

Based on your error logs, you're experiencing:
- "Incoming request ended abruptly: context canceled" errors
- "Request failed" errors between Cloudflare tunnel and your local server
- Multiple connection failures from various Cloudflare IPs

These errors typically indicate:
1. Local server not responding on expected port (4200)
2. Network connectivity issues
3. CORS configuration problems
4. Cloudflare tunnel configuration issues

## Quick Diagnosis

Run the health check script to see current status:
```powershell
node health-check.js
```

Or use the PowerShell troubleshooting tool:
```powershell
.\tunnel-fix.ps1 -CheckOnly
```

## Solution Options

### Option 1: Automatic Fix (Recommended)
Run the troubleshooting script with auto-fix:
```powershell
.\tunnel-fix.ps1 -StartServices
```

This will:
- Check what services are running
- Start missing services automatically
- Provide specific recommendations

### Option 2: Switch to Local Development
If tunnel issues persist, switch to local development mode:
```powershell
.\tunnel-fix.ps1 -LocalMode
```

Then start services locally:
```powershell
# Terminal 1 - Backend
cd backend
npm start

# Terminal 2 - Frontend (local mode)
cd hype-hive
npm run start:local
```

### Option 3: Manual Troubleshooting

#### Step 1: Check Services
```powershell
# Check if ports are in use
netstat -an | findstr ":3000"  # Backend
netstat -an | findstr ":4200"  # Frontend
```

#### Step 2: Start Services Manually
```powershell
# Start backend (Terminal 1)
cd backend
npm start

# Start frontend (Terminal 2)
cd hype-hive
npm start
```

#### Step 3: Check Cloudflared
```powershell
# Check if cloudflared is running
Get-Process cloudflared

# If not running, start it (adjust path as needed)
cloudflared tunnel run <your-tunnel-name>
```

## Environment Configurations

### Development Environments Available:

1. **Tunnel Mode** (default): Uses `http://hypehive.linksynk.info`
2. **Local Mode**: Uses `http://localhost:3000`

### Switch Between Modes:

**To Local Mode:**
```powershell
.\tunnel-fix.ps1 -LocalMode
cd hype-hive
npm run start:local
```

**Back to Tunnel Mode:**
```powershell
.\tunnel-fix.ps1 -TunnelMode
cd hype-hive
npm start
```

## Backend Configuration

The backend has been updated with enhanced CORS support for both tunnel and local development:

- ✅ Supports multiple origins (tunnel + local)
- ✅ Proper error logging for CORS issues
- ✅ Enhanced security headers
- ✅ Better timeout handling

## Common Issues and Solutions

### Issue: "ECONNREFUSED" on localhost:4200
**Solution:** Frontend not running
```powershell
cd hype-hive
npm start
```

### Issue: "ECONNREFUSED" on localhost:3000
**Solution:** Backend not running
```powershell
cd backend
npm start
```

### Issue: Tunnel shows 502/503 errors
**Solutions:**
1. Check if local services are running
2. Restart cloudflared tunnel
3. Switch to local mode temporarily

### Issue: TLS handshake errors with cloudflared
**Error:** `tls: first record does not look like a TLS handshake`
**Solution:** Cloudflared is trying to connect to HTTPS when service is HTTP
1. Check cloudflared configuration uses `http://` not `https://` for local services
2. Verify `.env` file has `FRONTEND_URL=http://localhost:4200` (not https)
3. Restart cloudflared tunnel after fixing configuration

**Example correct cloudflared config:**
```yaml
ingress:
  - hostname: hypehive.linksynk.info
    path: /api
    service: http://localhost:3000  # HTTP, not HTTPS
  - hostname: hypehive.linksynk.info
    service: http://localhost:4200   # HTTP, not HTTPS
  - service: http_status:404
```

### Issue: CORS errors in browser
**Solution:** Backend CORS is now configured for both modes. If issues persist:
```powershell
# Restart backend
cd backend
npm restart
```

## Monitoring Commands

### Real-time Health Check
```powershell
# Check every 30 seconds
while ($true) { 
    .\tunnel-fix.ps1 -CheckOnly
    Start-Sleep 30
    Clear-Host
}
```

### Check Logs
```powershell
# Backend logs
cd backend
npm run logs

# Or check log files directly
Get-Content logs\combined.log -Tail 20 -Wait
```

## Best Practices

1. **Always start backend first**, then frontend
2. **Use local mode during development** when tunnel is unstable
3. **Keep both terminal windows open** to monitor for errors
4. **Check logs regularly** for early problem detection
5. **Use the health check script** before starting work sessions

## Emergency Recovery

If nothing works, try the following:

1. **Kill all processes**
```powershell
taskkill /F /IM node.exe
taskkill /F /IM cloudflared.exe
```
