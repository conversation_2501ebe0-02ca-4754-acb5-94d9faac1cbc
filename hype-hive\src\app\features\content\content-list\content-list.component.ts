import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

import { ContentService, Content } from '../../../core/services/content.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-content-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatPaginatorModule
  ],
  templateUrl: './content-list.component.html',
  styleUrl: './content-list.component.scss'
})
export class ContentListComponent implements OnInit {
  content: Content[] = [];
  isLoading = false;

  // Pagination
  currentPage = 0;
  pageSize = 12;
  totalItems = 0;

  // Filters
  searchTerm = '';
  selectedCategory = '';
  selectedDifficulty = '';

  // Search debouncing
  private searchSubject = new Subject<string>();

  // Categories
  categories = [
    'Account Setup',
    'Bots',
    'Networking',
    'Emotes',
    'Streaming Platforms',
    'Affiliate Roles',
    'Console Help',
    'PC Help'
  ];

  constructor(
    private contentService: ContentService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.setupSearchDebounce();
    this.loadContent();
  }

  /**
   * Setup search debouncing
   */
  setupSearchDebounce(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.searchTerm = searchTerm;
      this.currentPage = 0;
      this.loadContent();
    });
  }

  /**
   * Check if user can create content
   */
  get canCreateContent(): boolean {
    const user = this.authService.getCurrentUser();
    return user?.isMentor || user?.role === 'admin';
  }

  /**
   * Check if there are active filters
   */
  get hasActiveFilters(): boolean {
    return !!(this.searchTerm || this.selectedCategory || this.selectedDifficulty);
  }

  /**
   * Load content with current filters
   */
  loadContent(): void {
    this.isLoading = true;

    const query: any = {
      page: this.currentPage + 1,
      limit: this.pageSize,
      isPublished: true
    };

    if (this.searchTerm) {
      query.search = this.searchTerm;
    }

    if (this.selectedCategory) {
      query.category = this.selectedCategory;
    }

    if (this.selectedDifficulty) {
      query.difficulty = this.selectedDifficulty;
    }

    this.contentService.getAllContent(query).subscribe({
      next: (result) => {
        this.content = result.content;
        this.totalItems = result.pagination?.total || 0;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading content:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Handle search input
   */
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  /**
   * Handle category change
   */
  onCategoryChange(): void {
    this.currentPage = 0;
    this.loadContent();
  }

  /**
   * Handle difficulty change
   */
  onDifficultyChange(): void {
    this.currentPage = 0;
    this.loadContent();
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadContent();
  }
}
