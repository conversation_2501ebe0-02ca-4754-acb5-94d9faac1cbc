import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatCardModule, MatIconModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit {
  isLoading = false;
  errorMessage = '';
  returnUrl = '';
  isFromMentorPage = false;

  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const code = params['code'];
      if (code) {
        this.isLoading = true;
        this.authService.handleAuthCallback(code).subscribe({
          next: () => {
            this.isLoading = false;
            this.router.navigate([this.returnUrl]);
          },
          error: () => {
            this.isLoading = false;
            this.errorMessage = 'Login failed. Please try again.';
          }
        });
        return;
      }

      // Check return URL and set context
      this.returnUrl = params['returnUrl'] || '/profile';
      this.isFromMentorPage = this.returnUrl.includes('/mentors/');

      const authStatus = params['auth'];
      const error = params['error'];

      if (authStatus === 'success') {
        // Authentication was successful, refresh auth status and redirect
        this.isLoading = true;
        this.authService.refreshAuthStatus().subscribe({
          next: (user) => {
            this.isLoading = false;
            if (user) {
              console.log('Authentication successful, redirecting to:', this.returnUrl);
              this.router.navigate([this.returnUrl]);
            } else {
              this.errorMessage = 'Authentication completed but failed to load profile. Please try logging in again.';
            }
          },
          error: (err) => {
            this.isLoading = false;
            console.error('Failed to get user profile after auth:', err);
            this.errorMessage = 'Authentication completed but failed to load profile. Please try logging in again.';
          }
        });
      } else if (error) {
        this.errorMessage = `Authentication error: ${decodeURIComponent(error)}`;
      }
    });
  }

  /**
   * Login with Discord (server-side OAuth flow)
   */
  loginWithDiscord(): void {
    // Use the stored return URL
    window.location.href = this.authService.getDiscordAuthUrl(this.returnUrl);
  }
}
