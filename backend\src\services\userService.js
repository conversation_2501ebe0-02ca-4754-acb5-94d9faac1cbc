const User = require('../models/User');
const logger = require('../config/logger');
const { AppError } = require('../middleware/errorHandler');

/**
 * Get all users with optimized query performance
 * @param {Object} query - Query parameters for filtering
 * @returns {Promise<Array>} - Array of users with pagination metadata
 */
const getAllUsers = async (query = {}) => {
  try {
    const filter = {};

    // Apply filters if provided
    if (query.role) filter.role = query.role;
    if (query.isMentor !== undefined) filter.isMentor = query.isMentor === 'true';
    if (query.isActive !== undefined) filter.isActive = query.isActive === 'true';

    // Search by username - create text index for better performance
    if (query.search) {
      if (query.search.length > 2) { // Only search if query is at least 3 characters
        // Use text search if available, otherwise fallback to regex
        try {
          filter.$text = { $search: query.search };
        } catch (err) {
          // Fallback to regex if text search fails or isn't set up
          filter.discordUsername = { $regex: query.search, $options: 'i' };
        }
      } else {
        // Use regex for short queries
        filter.discordUsername = { $regex: query.search, $options: 'i' };
      }
    }

    // Pagination
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const skip = (page - 1) * limit;

    // Use aggregation for better performance and more flexibility
    const aggregationPipeline = [
      { $match: filter },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          discordId: 1,
          discordUsername: 1,
          discordAvatar: 1,
          email: 1,
          isMentor: 1,
          proficiencies: 1,
          bio: 1,
          socialLinks: 1,
          mentorApplication: 1,
          role: 1,
          isActive: 1,
          lastLogin: 1,
          createdAt: 1,
          updatedAt: 1
        }
      }
    ];

    // Execute query with aggregation pipeline
    const users = await User.aggregate(aggregationPipeline);

    // Get total count using countDocuments for better performance
    const total = await User.countDocuments(filter);

    return {
      users,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting all users: ${error.message}`);
    throw new Error('Failed to get users');
  }
};

/**
 * Get user by ID with optimized query
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - User document
 */
const getUserById = async (userId) => {
  // Use lean() for better performance when you don't need to modify the document
  const user = await User.findById(userId)
    .select('-__v')
    .lean();

  if (!user) {
    throw new AppError('User not found', 404);
  }

  return user;
};

/**
 * Update user profile with optimized query
 * @param {string} userId - User ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Updated user document
 */
const updateUserProfile = async (userId, updateData) => {
  try {
    // Fields that can be updated
    const allowedUpdates = [
      'bio',
      'socialLinks',
      'proficiencies'
    ];

    // Filter out fields that are not allowed to be updated
    const updates = {};
    Object.keys(updateData).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = updateData[key];
      }
    });

    // Use findOneAndUpdate for atomic updates
    // This is more efficient than findByIdAndUpdate
    const user = await User.findOneAndUpdate(
      { _id: userId },
      { $set: updates },
      {
        new: true, // Return the updated document
        runValidators: true, // Run schema validators
        projection: { __v: 0 } // Exclude __v field
      }
    );

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  } catch (error) {
    logger.error(`Error updating user profile: ${error.message}`);
    throw new Error('Failed to update user profile');
  }
};

/**
 * Apply for mentor status with optimized atomic update
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated user document
 */
const applyForMentor = async (userId) => {
  // First check if the user exists and hasn't already applied
  const user = await User.findOne({
    _id: userId,
    $or: [
      { 'mentorApplication.status': 'none' },
      { 'mentorApplication.status': 'rejected' }
    ]
  });

  if (!user) {
    // Check if user exists at all
    const userExists = await User.exists({ _id: userId });

    if (!userExists) {
      throw new AppError('User not found', 404);
    } else {
      throw new AppError('Mentor application already submitted', 400);
    }
  }

  // Use atomic update operation
  const updatedUser = await User.findOneAndUpdate(
    { _id: userId },
    {
      $set: {
        'mentorApplication.status': 'pending',
        'mentorApplication.submittedAt': new Date()
      }
    },
    {
      new: true, // Return the updated document
      runValidators: true // Run schema validators
    }
  );

  return updatedUser;
};

/**
 * Review mentor application with optimized atomic update
 * @param {string} userId - User ID
 * @param {string} status - Application status (approved/rejected)
 * @param {string} reviewerId - Reviewer ID
 * @returns {Promise<Object>} - Updated user document
 */
const reviewMentorApplication = async (userId, status, reviewerId) => {
  try {
    // First check if the user exists and has a pending application
    const user = await User.findOne({
      _id: userId,
      'mentorApplication.status': 'pending'
    });

    if (!user) {
      // Check if user exists at all
      const userExists = await User.exists({ _id: userId });

      if (!userExists) {
        throw new Error('User not found');
      } else {
        throw new Error('No pending mentor application found');
      }
    }

    // Prepare update operation based on status
    const updateOperation = {
      'mentorApplication.status': status,
      'mentorApplication.reviewedAt': new Date(),
      'mentorApplication.reviewedBy': reviewerId
    };

    // If approved, update user role and mentor status
    if (status === 'approved') {
      updateOperation.isMentor = true;
      updateOperation.role = 'mentor';
    }

    // Use atomic update operation
    const updatedUser = await User.findOneAndUpdate(
      { _id: userId },
      { $set: updateOperation },
      {
        new: true, // Return the updated document
        runValidators: true // Run schema validators
      }
    );

    return updatedUser;
  } catch (error) {
    logger.error(`Error reviewing mentor application: ${error.message}`);
    throw new Error(error.message || 'Failed to review mentor application');
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUserProfile,
  applyForMentor,
  reviewMentorApplication
};
