const MentorApplication = require('../models/MentorApplication');
const logger = require('../config/logger');

// POST /api/mentors/apply - Regular user submits mentor application
exports.applyForMentor = async (req, res) => {
  try {
    const { name, discord_id, proficiencies } = req.body;
    const userId = req.user._id;

    // Prevent duplicate applications
    const existing = await MentorApplication.findOne({ user: userId, status: 'pending' });
    if (existing) {
      return res.status(400).json({ success: false, message: 'You already have a pending application.' });
    }

    const application = new MentorApplication({
      user: userId,
      name,
      discord_id,
      proficiencies,
    });
    await application.save();
    logger.info(`Mentor application submitted by user ${userId}`);
    res.json({ success: true, message: 'Application submitted successfully.' });
  } catch (err) {
    logger.error('Error submitting mentor application:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// GET /api/mentors/applications - List all mentor applications (admin only)
exports.listApplications = async (req, res) => {
  try {
    const applications = await MentorApplication.find().populate('user', 'username email');
    res.json({ success: true, data: applications });
  } catch (err) {
    logger.error('Error fetching mentor applications:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// POST /api/mentors/applications/:id/approve - Approve a mentor application (admin only)
exports.approveApplication = async (req, res) => {
  try {
    const application = await MentorApplication.findById(req.params.id);
    if (!application) return res.status(404).json({ success: false, message: 'Application not found' });
    application.status = 'approved';
    await application.save();
    // Optionally, create Mentor entry here
    res.json({ success: true, message: 'Application approved.' });
  } catch (err) {
    logger.error('Error approving mentor application:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// POST /api/mentors/applications/:id/reject - Reject a mentor application (admin only)
exports.rejectApplication = async (req, res) => {
  try {
    const application = await MentorApplication.findById(req.params.id);
    if (!application) return res.status(404).json({ success: false, message: 'Application not found' });
    application.status = 'rejected';
    await application.save();
    res.json({ success: true, message: 'Application rejected.' });
  } catch (err) {
    logger.error('Error rejecting mentor application:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};
