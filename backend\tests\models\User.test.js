const User = require('../../src/models/User');

describe('User Model', () => {
  describe('User Creation', () => {
    test('should create a valid user', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.discordId).toBe(userData.discordId);
      expect(savedUser.discordUsername).toBe(userData.discordUsername);
      expect(savedUser.email).toBe(userData.email);
      expect(savedUser.role).toBe('user');
      expect(savedUser.isActive).toBe(true);
      expect(savedUser.createdAt).toBeDefined();
    });

    test('should require discordId', async () => {
      const userData = global.testUtils.createTestUser();
      delete userData.discordId;
      
      const user = new User(userData);
      await expect(user.save()).rejects.toThrow();
    });

    test('should require discordUsername', async () => {
      const userData = global.testUtils.createTestUser();
      delete userData.discordUsername;

      const user = new User(userData);
      await expect(user.save()).rejects.toThrow();
    });

    test('should not require email', async () => {
      const userData = global.testUtils.createTestUser();
      delete userData.email;

      const user = new User(userData);
      const savedUser = await user.save();
      expect(savedUser._id).toBeDefined();
    });

    test('should accept any email format', async () => {
      const userData = global.testUtils.createTestUser();
      userData.email = 'invalid-email';

      const user = new User(userData);
      const savedUser = await user.save();
      expect(savedUser.email).toBe('invalid-email');
    });

    test('should enforce unique discordId', async () => {
      const userData1 = global.testUtils.createTestUser();
      const userData2 = { ...userData1, discordUsername: 'different#5678', email: '<EMAIL>' };

      const user1 = new User(userData1);
      await user1.save();

      const user2 = new User(userData2);
      await expect(user2.save()).rejects.toThrow();
    });
  });

  describe('Mentor Functionality', () => {
    test('should create a mentor user', async () => {
      const mentorData = global.testUtils.createTestMentor();
      const mentor = new User(mentorData);
      const savedMentor = await mentor.save();

      expect(savedMentor.role).toBe('mentor');
      expect(savedMentor.isMentor).toBe(true);
      expect(savedMentor.bio).toBe(mentorData.bio);
    });

    test('should validate mentor application', async () => {
      const userData = global.testUtils.createTestUser();
      userData.mentorApplication = {
        status: 'pending',
        submittedAt: new Date()
      };

      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser.mentorApplication).toBeDefined();
      expect(savedUser.mentorApplication.status).toBe('pending');
      expect(savedUser.mentorApplication.submittedAt).toBeDefined();
    });
  });

  describe('User Methods', () => {
    test('should find users by proficiency category', async () => {
      const user1 = new User({
        ...global.testUtils.createTestUser(),
        discordId: '111111111111111111',
        proficiencies: [
          {
            name: 'Basic Streaming',
            category: 'Streaming Platforms',
            isSelected: true
          }
        ]
      });

      const user2 = new User({
        ...global.testUtils.createTestUser(),
        discordId: '222222222222222222',
        discordUsername: 'user2#5678',
        email: '<EMAIL>',
        proficiencies: [
          {
            name: 'Advanced Streaming',
            category: 'Streaming Platforms',
            isSelected: true
          }
        ]
      });

      await user1.save();
      await user2.save();

      const streamingUsers = await User.find({ 'proficiencies.category': 'Streaming Platforms' });
      expect(streamingUsers).toHaveLength(2);
    });

    test('should find mentors', async () => {
      const regularUser = new User(global.testUtils.createTestUser());
      const mentor = new User(global.testUtils.createTestMentor());

      await regularUser.save();
      await mentor.save();

      const mentors = await User.find({ role: 'mentor' });
      expect(mentors).toHaveLength(1);
      expect(mentors[0].discordId).toBe(mentor.discordId);
    });
  });

  describe('Proficiencies', () => {
    test('should allow empty proficiencies array', async () => {
      const userData = global.testUtils.createTestUser();
      userData.proficiencies = [];

      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser.proficiencies).toBeDefined();
      expect(savedUser.proficiencies).toHaveLength(0);
    });

    test('should validate proficiency categories', async () => {
      const userData = global.testUtils.createTestUser();
      userData.proficiencies = [
        {
          name: 'Test Skill',
          category: 'Invalid Category',
          isSelected: true
        }
      ];

      const user = new User(userData);
      await expect(user.save()).rejects.toThrow();
    });
  });
});
