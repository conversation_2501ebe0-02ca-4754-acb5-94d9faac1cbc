import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { isPlatformBrowser } from '@angular/common';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';
import { ContentService, Content } from './content.service';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class RecommendationService {
  private apiUrl = `/api/recommendations`;
  
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private contentService: ContentService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }
  
  /**
   * Get personalized content recommendations for the current user
   */
  getPersonalizedRecommendations(): Observable<Content[]> {
    // During SSR, return empty array to avoid API calls
    if (!isPlatformBrowser(this.platformId)) {
      return of([]);
    }

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      return this.getPopularContent();
    }
    
    // Get current user
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) {
          return this.getPopularContent();
        }
        
        // Get recommendations from API
        return this.http.get<any>(`${this.apiUrl}/content`).pipe(
          map(response => {
            if (response.success) {
              return response.data;
            } else {
              throw new Error(response.message || 'Failed to get recommendations');
            }
          }),
          catchError(error => {
            console.error('Error getting recommendations:', error);
            // Fallback to popular content if API fails
            return this.getPopularContent();
          })
        );
      })
    );
  }
  
  /**
   * Get popular content as fallback
   */
  private getPopularContent(): Observable<Content[]> {
    // During SSR, return empty array to avoid API calls
    if (!isPlatformBrowser(this.platformId)) {
      return of([]);
    }

    return this.contentService.getAllContent({ 
      sort: 'popular',
      isPublished: true,
      limit: 6
    }).pipe(
      map(result => result.content)
    );
  }
  
  /**
   * Get recommended mentors based on user interests
   */
  getRecommendedMentors(): Observable<User[]> {
    // During SSR, return empty array to avoid API calls
    if (!isPlatformBrowser(this.platformId)) {
      return of([]);
    }

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      return of([]);
    }
    
    return this.http.get<any>(`${this.apiUrl}/mentors`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        } else {
          throw new Error(response.message || 'Failed to get recommended mentors');
        }
      }),
      catchError(error => {
        console.error('Error getting recommended mentors:', error);
        return of([]);
      })
    );
  }
  
  /**
   * Get content similar to the given content
   * @param contentId Content ID
   */
  getSimilarContent(contentId: string): Observable<Content[]> {
    // During SSR, return empty array to avoid API calls
    if (!isPlatformBrowser(this.platformId)) {
      return of([]);
    }

    return this.http.get<any>(`${this.apiUrl}/content/${contentId}/similar`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        } else {
          throw new Error(response.message || 'Failed to get similar content');
        }
      }),
      catchError(error => {
        console.error('Error getting similar content:', error);
        // Fallback to content in the same category
        return this.contentService.getContentById(contentId).pipe(
          switchMap(content => {
            return this.contentService.getAllContent({
              category: content.category,
              isPublished: true,
              limit: 4
            }).pipe(
              map(result => {
                // Filter out the current content
                return result.content.filter(item => item._id !== contentId);
              })
            );
          }),
          catchError(() => of([]))
        );
      })
    );
  }
  
  /**
   * Track user content interaction for better recommendations
   * @param contentId Content ID
   * @param action Interaction type (view, like, comment)
   */
  trackContentInteraction(contentId: string, action: 'view' | 'like' | 'comment'): Observable<boolean> {
    // During SSR, don't track interactions
    if (!isPlatformBrowser(this.platformId)) {
      return of(false);
    }

    // Only track if user is authenticated
    if (!this.authService.isAuthenticated()) {
      return of(false);
    }
    
    return this.http.post<any>(`${this.apiUrl}/track`, { contentId, action }).pipe(
      map(response => response.success || false),
      catchError(() => of(false))
    );
  }
}
