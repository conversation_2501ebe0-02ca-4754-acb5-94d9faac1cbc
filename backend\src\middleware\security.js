const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const logger = require('../config/logger');

// Admin Discord IDs that have full system privileges
const ADMIN_DISCORD_IDS = [
  '323345923964928001', // captain.sexy - System Administrator
  // Add additional admin Discord IDs here
];

/**
 * Check if a Discord ID is a system administrator
 * @param {string} discordId - The Discord ID to check
 * @returns {boolean} - True if Discord ID is a system admin
 */
const isSystemAdmin = (discordId) => {
  return ADMIN_DISCORD_IDS.includes(discordId);
};

// Import rate limiting functionality
const {
  generalLimiter,
  authLimiter,
  contentCreationLimiter,
  uploadLimiter,
  chatLimiter,
  mentorApplicationLimiter,
  passwordResetLimiter,
  profileUpdateLimiter,
  monitoringLimiter,
  createUserSpecificLimiter,
  createTrustedRateLimiter
} = require('./rateLimiting');

/**
 * Helmet configuration for security headers
 */
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://discord.com", "https://discordapp.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for Discord OAuth
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * MongoDB injection protection
 */
const mongoSanitizeConfig = mongoSanitize({
  replaceWith: '_',
  onSanitize: ({ req, key }) => {
    logger.warn(`Potential NoSQL injection attempt detected from IP: ${req.ip}, key: ${key}`);
  }
});

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  // Sanitize URL parameters
  if (req.params && typeof req.params === 'object') {
    sanitizeObject(req.params);
  }

  next();
};

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj) {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'string') {
        // Remove potential script tags and dangerous characters
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  }
}

/**
 * Request logging middleware for security monitoring
 */
const securityLogger = (req, res, next) => {
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\$where/i,
    /\$ne/i,
    /\$gt/i,
    /\$lt/i,
    /\$regex/i,
    /<script/i,
    /javascript:/i,
    /eval\(/i,
    /union.*select/i,
    /drop.*table/i
  ];

  const requestData = JSON.stringify({
    body: req.body,
    query: req.query,
    params: req.params
  });

  const foundSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData));

  if (foundSuspicious) {
    logger.warn(`Suspicious request detected from IP: ${req.ip}`, {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      body: req.body,
      query: req.query,
      params: req.params
    });
  }

  next();
};

/**
 * File upload security middleware
 */
const fileUploadSecurity = (req, res, next) => {
  if (req.file) {
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mpeg',
      'audio/wav',
      'application/pdf'
    ];

    if (!allowedMimeTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: 'File type not allowed'
      });
    }

    // Check file size (handled by multer config, but double-check)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: 'File size too large'
      });
    }

    // Log file upload for security monitoring
    logger.info(`File uploaded by user ${req.user?.id || 'unknown'}`, {
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      ip: req.ip
    });
  }

  next();
};

module.exports = {
  generalLimiter,
  authLimiter,
  contentCreationLimiter,
  uploadLimiter,
  chatLimiter,
  mentorApplicationLimiter,
  passwordResetLimiter,
  profileUpdateLimiter,
  monitoringLimiter,
  createUserSpecificLimiter,
  createTrustedRateLimiter,
  isSystemAdmin,
  ADMIN_DISCORD_IDS,
  helmetConfig,
  mongoSanitizeConfig,
  sanitizeInput,
  securityLogger,
  fileUploadSecurity
};
