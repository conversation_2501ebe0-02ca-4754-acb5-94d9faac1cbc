const Content = require('../models/Content');
const User = require('../models/User');
const notificationService = require('./notificationService');
const logger = require('../config/logger');
const { isSystemAdmin } = require('../middleware/security');

/**
 * Get all content with optimized query performance
 * @param {Object} query - Query parameters for filtering
 * @returns {Promise<Array>} - Array of content with pagination metadata
 */
const getAllContent = async (query = {}) => {
  try {
    const filter = {};

    // Apply filters if provided (using lean queries for better performance)
    if (query.category) filter.category = query.category;
    if (query.isPublished !== undefined) filter.isPublished = query.isPublished === 'true';
    if (query.createdBy) filter.createdBy = query.createdBy;

    // Search by title, description, or tags
    // Using text index for better search performance instead of regex
    if (query.search) {
      if (query.search.length > 2) { // Only search if query is at least 3 characters
        filter.$text = { $search: query.search };
      } else {
        // Fallback to regex for short queries
        filter.$or = [
          { title: { $regex: query.search, $options: 'i' } },
          { description: { $regex: query.search, $options: 'i' } },
          { tags: { $regex: query.search, $options: 'i' } }
        ];
      }
    }

    // Filter by difficulty
    if (query.difficulty) {
      filter.difficulty = query.difficulty;
    }

    // Pagination
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const skip = (page - 1) * limit;

    // Sort options
    let sort = { createdAt: -1 }; // Default sort by newest
    let sortOptions = {};

    if (query.sort) {
      switch (query.sort) {
        case 'popular':
          sortOptions = { views: -1 };
          break;
        case 'likes':
          sortOptions = { likes: -1 };
          break;
        case 'oldest':
          sortOptions = { createdAt: 1 };
          break;
        default:
          sortOptions = { createdAt: -1 };
      }
    } else {
      sortOptions = { createdAt: -1 };
    }

    // Use aggregation pipeline for better performance
    const aggregationPipeline = [
      { $match: filter },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creatorInfo'
        }
      },
      { $unwind: '$creatorInfo' },
      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          category: 1,
          videoUrl: 1,
          embedUrl: 1,
          thumbnailUrl: 1,
          isPublished: 1,
          tags: 1,
          views: 1,
          likes: 1,
          difficulty: 1,
          createdAt: 1,
          updatedAt: 1,
          'createdBy._id': '$creatorInfo._id',
          'createdBy.discordUsername': '$creatorInfo.discordUsername',
          'createdBy.discordAvatar': '$creatorInfo.discordAvatar',
          'createdBy.isMentor': '$creatorInfo.isMentor'
        }
      }
    ];

    // Execute query with aggregation pipeline
    const content = await Content.aggregate(aggregationPipeline);

    // Get total count (using countDocuments for better performance)
    const total = await Content.countDocuments(filter);

    return {
      content,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting all content: ${error.message}`);
    throw new Error('Failed to get content');
  }
};

/**
 * Get content by ID with optimized query
 * @param {string} contentId - Content ID
 * @returns {Promise<Object>} - Content document
 */
const getContentById = async (contentId) => {
  try {
    // Use findOneAndUpdate to atomically increment the view count
    // This is more efficient than finding and then saving separately
    const content = await Content.findOneAndUpdate(
      { _id: contentId },
      { $inc: { views: 1 } },
      {
        new: true, // Return the updated document
        projection: { __v: 0 } // Exclude __v field
      }
    )
    .populate('createdBy', 'discordUsername discordAvatar isMentor')
    .populate('comments.user', 'discordUsername discordAvatar');

    if (!content) {
      throw new Error('Content not found');
    }

    return content;
  } catch (error) {
    logger.error(`Error getting content by ID: ${error.message}`);
    throw new Error('Failed to get content');
  }
};

/**
 * Create new content
 * @param {Object} contentData - Content data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created content document
 */
const createContent = async (contentData, userId) => {
  try {
    const content = new Content({
      ...contentData,
      createdBy: userId
    });

    await content.save();

    return content;
  } catch (error) {
    logger.error(`Error creating content: ${error.message}`);
    throw new Error('Failed to create content');
  }
};

/**
 * Update content
 * @param {string} contentId - Content ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated content document
 */
const updateContent = async (contentId, updateData, userId) => {
  try {
    const content = await Content.findById(contentId);

    if (!content) {
      throw new Error('Content not found');
    }

    // Check if user is the creator, an admin, or a system admin
    const user = await User.findById(userId);
    const isCreator = content.createdBy.toString() === userId;
    const isAdmin = user && (user.role === 'admin' || isSystemAdmin(user.discordId));
    
    if (!isCreator && !isAdmin) {
      throw new Error('Not authorized to update this content');
    }

    // Update content
    Object.keys(updateData).forEach(key => {
      content[key] = updateData[key];
    });

    await content.save();

    return content;
  } catch (error) {
    logger.error(`Error updating content: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Delete content
 * @param {string} contentId - Content ID
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} - Success status
 */
const deleteContent = async (contentId, userId) => {
  try {
    const content = await Content.findById(contentId);

    if (!content) {
      throw new Error('Content not found');
    }

    // Check if user is the creator, an admin, or a system admin
    const user = await User.findById(userId);
    const isCreator = content.createdBy.toString() === userId;
    const isAdmin = user && (user.role === 'admin' || isSystemAdmin(user.discordId));
    
    if (!isCreator && !isAdmin) {
      throw new Error('Not authorized to delete this content');
    }

    await content.deleteOne();

    return true;
  } catch (error) {
    logger.error(`Error deleting content: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Like or unlike content with optimized atomic operations
 * @param {string} contentId - Content ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated content document
 */
const toggleLike = async (contentId, userId) => {
  try {
    // First check if the user has already liked the content
    const content = await Content.findById(contentId)
      .populate('createdBy', '_id');

    if (!content) {
      throw new Error('Content not found');
    }

    // Check if user already liked the content
    const hasLiked = content.likedBy.includes(userId);

    // Use atomic update operations for better performance
    let updateOperation;

    if (!hasLiked) {
      // Add like
      updateOperation = {
        $addToSet: { likedBy: userId }, // Ensures no duplicates
        $inc: { likes: 1 }
      };

      // Create notification for content creator (if not the same user)
      if (content.createdBy._id.toString() !== userId) {
        try {
          await notificationService.createLikeNotification(
            contentId,
            userId,
            content.createdBy._id
          );
        } catch (notifError) {
          logger.error(`Error creating like notification: ${notifError.message}`);
          // Don't fail the like operation if notification fails
        }
      }
    } else {
      // Remove like
      updateOperation = {
        $pull: { likedBy: userId },
        $inc: { likes: -1 }
      };
    }

    // Update the document atomically
    const updatedContent = await Content.findByIdAndUpdate(
      contentId,
      updateOperation,
      { new: true } // Return the updated document
    );

    return updatedContent;
  } catch (error) {
    logger.error(`Error toggling like: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Add comment to content with optimized atomic operations
 * @param {string} contentId - Content ID
 * @param {string} userId - User ID
 * @param {string} text - Comment text
 * @returns {Promise<Object>} - Updated content document
 */
const addComment = async (contentId, userId, text) => {
  try {
    // Get content to check if it exists and get creator info
    const contentCheck = await Content.findById(contentId)
      .populate('createdBy', '_id');

    if (!contentCheck) {
      throw new Error('Content not found');
    }

    // Create a new comment object
    const newComment = {
      user: userId,
      text,
      createdAt: new Date()
    };

    // Use findOneAndUpdate to atomically add the comment
    // This is more efficient than finding, modifying, and saving
    const content = await Content.findOneAndUpdate(
      { _id: contentId },
      { $push: { comments: newComment } },
      {
        new: true, // Return the updated document
        runValidators: true // Run schema validators
      }
    );

    // Populate user data in the comments
    await content.populate('comments.user', 'discordUsername discordAvatar');

    // Create notification for content creator (if not the same user)
    if (contentCheck.createdBy._id.toString() !== userId) {
      try {
        // Create a comment notification
        const user = await User.findById(userId).select('discordUsername');

        if (user) {
          const notification = {
            recipient: contentCheck.createdBy._id,
            sender: userId,
            type: 'comment',
            content: `${user.discordUsername} commented on your content`,
            relatedContent: {
              contentType: 'content',
              contentId: contentId
            }
          };

          await notificationService.createNotification(notification);
        }
      } catch (notifError) {
        logger.error(`Error creating comment notification: ${notifError.message}`);
        // Don't fail the comment operation if notification fails
      }
    }

    return content;
  } catch (error) {
    logger.error(`Error adding comment: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Edit a comment on content (admin)
 * @param {string} contentId
 * @param {string} commentId
 * @param {string} newText
 * @returns {Promise<Object>} Updated comment
 */
const editComment = async (contentId, commentId, newText) => {
  // Find the content with the specified comment
  const content = await Content.findOne({ _id: contentId, 'comments._id': commentId });
  if (!content) {
    throw new Error('Comment not found');
  }

  // Update text and set editedAt timestamp
  await Content.updateOne(
    { _id: contentId, 'comments._id': commentId },
    { $set: { 'comments.$.text': newText, 'comments.$.editedAt': new Date() } }
  );

  // Return the updated comment
  const updated = await Content.findById(contentId)
    .select('comments')
    .populate('comments.user', 'discordUsername discordAvatar');
  return updated.comments.find(c => c._id.toString() === commentId);
};

/**
 * Delete a comment from content (admin)
 * @param {string} contentId
 * @param {string} commentId
 * @returns {Promise<Object>} Remaining comments
 */
const deleteComment = async (contentId, commentId) => {
  const content = await Content.findById(contentId);
  if (!content) {
    throw new Error('Content not found');
  }

  const updatedContent = await Content.findByIdAndUpdate(
    contentId,
    { $pull: { comments: { _id: commentId } } },
    { new: true }
  )
    .select('comments')
    .populate('comments.user', 'discordUsername discordAvatar');
  return { success: true, comments: updatedContent.comments };
};

module.exports = {
  getAllContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  toggleLike,
  addComment,
  editComment,
  deleteComment
};
