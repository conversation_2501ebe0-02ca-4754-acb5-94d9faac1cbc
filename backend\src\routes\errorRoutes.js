const express = require('express');
const { logClientError, getErrorStats } = require('../controllers/errorController');
const { protect, authorize } = require('../middleware/auth');
const { generalLimiter } = require('../middleware/security');

const router = express.Router();

// POST /api/errors - Log client-side errors (rate limited)
router.post('/', generalLimiter, logClientError);

// GET /api/errors/stats - Get error statistics (admin only)
router.get('/stats', protect, authorize('admin'), getErrorStats);

module.exports = router;
