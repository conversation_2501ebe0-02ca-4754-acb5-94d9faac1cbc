.notification-list-container {
  width: 350px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
  
  h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
  }
}

.notification-content {
  flex: 1;
  overflow-y: auto;
  max-height: 450px;
}

.loading-container, .error-container, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
  
  button {
    margin-top: 1rem;
  }
  
  mat-icon {
    font-size: 2rem;
    height: 2rem;
    width: 2rem;
    opacity: 0.7;
    line-height: 1;
    vertical-align: middle;
  }
}

.empty-state {
  padding: 3rem 1rem;
  
  mat-icon {
    color: #ccc;
  }
}

mat-nav-list {
  padding: 0;
  
  a.mat-mdc-list-item {
    height: auto;
    padding: 0.75rem 1rem;
    
    &.unread {
      background-color: #f0f7ff;
      
      .notification-text {
        font-weight: 500;
      }
    }
    
    &:hover {
      background-color: #f5f5f5;
      
      .delete-button {
        opacity: 1;
      }
    }
  }
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
  overflow: visible;
  
  mat-icon {
    color: white;
    vertical-align: middle;
    line-height: 1;
  }
  
  &.like {
    background-color: #f44336;
  }
  
  &.comment {
    background-color: #2196f3;
  }
  
  &.mention {
    background-color: #ff9800;
  }
  
  &.message {
    background-color: #4caf50;
  }
  
  &.mentor_application {
    background-color: #9c27b0;
  }
  
  &.system {
    background-color: #607d8b;
  }
}

.notification-content {
  flex: 1;
  min-width: 0;
  
  .notification-text {
    margin: 0 0 0.25rem;
    font-size: 0.875rem;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .notification-time {
    font-size: 0.75rem;
    color: #888;
  }
}

.delete-button {
  opacity: 0;
  transition: opacity 0.2s;
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  border-top: 1px solid #eee;
}
