<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>Welcome to HypeHive</mat-card-title>
      <mat-card-subtitle>Twitch Streaming Education Platform</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      @if (isFromMentorPage) {
        <div class="redirect-info">
          <mat-icon class="info-icon">info</mat-icon>
          <p class="info-message">
            You need to sign in to view mentor profiles. After logging in, you'll be redirected back to the mentor page.
          </p>
        </div>
      }

      <p class="login-description">
        Join our community of streamers and mentors to learn and grow your Twitch streaming skills.
      </p>

      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }
    </mat-card-content>

    <mat-card-actions>
      <button
        mat-raised-button
        color="primary"
        class="discord-button"
        (click)="loginWithDiscord()"
        [disabled]="isLoading">
        <mat-icon>login</mat-icon>
        <span>Login with Discord</span>
      </button>
    </mat-card-actions>

    @if (isLoading) {
      <mat-card-footer>
        <div class="loading-indicator">
          <p>Authenticating...</p>
        </div>
      </mat-card-footer>
    }
  </mat-card>
</div>
