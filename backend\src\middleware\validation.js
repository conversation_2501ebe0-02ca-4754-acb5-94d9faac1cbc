const { body, param, query, validationResult } = require('express-validator');
const { sanitizeBody } = require('express-validator');
const mongoSanitize = require('express-mongo-sanitize');

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    console.log('Validation errors:', errors.array());
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  }

  next();
};

/**
 * Common validation rules
 */
const commonValidations = {
  // MongoDB ObjectId validation
  mongoId: param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),

  // Discord ID validation
  discordId: body('discordId')
    .optional()
    .isString()
    .isLength({ min: 17, max: 19 })
    .matches(/^\d+$/)
    .withMessage('Discord ID must be a valid snowflake ID'),

  // Email validation
  email: body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),

  // Username validation
  username: body('username')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 32 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username must be 2-32 characters and contain only letters, numbers, underscores, and hyphens'),

  // Display name validation
  displayName: body('displayName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .escape()
    .withMessage('Display name must be 1-50 characters'),

  // Bio validation
  bio: body('bio')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .escape()
    .withMessage('Bio must not exceed 500 characters'),

  // Proficiency level validation
  proficiencyLevel: (field) => body(field)
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
    .withMessage('Proficiency level must be one of: beginner, intermediate, advanced, expert'),

  // Pagination validation
  page: query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt()
    .withMessage('Page must be a positive integer'),

  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt()
    .withMessage('Limit must be between 1 and 100'),

  // Search query validation
  searchQuery: query('q')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .escape()
    .withMessage('Search query must be 1-100 characters')
};

/**
 * Auth validation rules
 */
const authValidation = {
  discordCallback: [
    body('code')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Discord authorization code is required'),
    handleValidationErrors
  ]
};

/**
 * User validation rules
 */
const userValidation = {
  updateProfile: [
    commonValidations.bio,
    body('proficiencies')
      .optional()
      .custom((value) => {
        if (!value) return true;
        if (!Array.isArray(value)) return false;
        return value.every(prof =>
          prof &&
          typeof prof === 'object' &&
          typeof prof.name === 'string' &&
          typeof prof.category === 'string' &&
          typeof prof.isSelected === 'boolean' &&
          [
            'Account Setup',
            'Bots',
            'Networking',
            'Emotes',
            'Streaming Platforms',
            'Affiliate Roles',
            'Console Help',
            'PC Help'
          ].includes(prof.category)
        );
      })
      .withMessage('Invalid proficiencies format'),
    body('socialLinks.twitch')
      .optional()
      .custom((value) => {
        if (!value || value.trim() === '') return true;
        return /^https?:\/\/.+/.test(value) || /^[a-zA-Z0-9_]+$/.test(value);
      })
      .withMessage('Invalid Twitch URL or username'),
    body('socialLinks.twitter')
      .optional()
      .custom((value) => {
        if (!value || value.trim() === '') return true;
        return /^https?:\/\/.+/.test(value) || /^@?[a-zA-Z0-9_]+$/.test(value);
      })
      .withMessage('Invalid Twitter URL or username'),
    body('socialLinks.youtube')
      .optional()
      .custom((value) => {
        if (!value || value.trim() === '') return true;
        return /^https?:\/\/.+/.test(value);
      })
      .withMessage('Invalid YouTube URL'),
    body('socialLinks.instagram')
      .optional()
      .custom((value) => {
        if (!value || value.trim() === '') return true;
        return /^https?:\/\/.+/.test(value) || /^[a-zA-Z0-9_.]+$/.test(value);
      })
      .withMessage('Invalid Instagram URL or username'),
    handleValidationErrors
  ],

  mentorApplication: [
    body('reason')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 50, max: 1000 })
      .escape()
      .withMessage('Reason must be 50-1000 characters'),
    body('experience')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 20, max: 1000 })
      .escape()
      .withMessage('Experience must be 20-1000 characters'),
    body('expertise')
      .isArray({ min: 1 })
      .withMessage('At least one area of expertise is required'),
    body('expertise.*')
      .isIn(['streaming', 'contentCreation', 'communityBuilding', 'marketing', 'technical'])
      .withMessage('Invalid expertise area'),
    body('availability')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 5, max: 200 })
      .escape()
      .withMessage('Availability must be 5-200 characters'),
    handleValidationErrors
  ],

  getUserById: [
    commonValidations.mongoId,
    handleValidationErrors
  ],

  searchUsers: [
    commonValidations.searchQuery,
    commonValidations.page,
    commonValidations.limit,
    query('proficiency')
      .optional()
      .isIn(['streaming', 'contentCreation', 'communityBuilding'])
      .withMessage('Invalid proficiency filter'),
    query('level')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid proficiency level filter'),
    handleValidationErrors
  ]
};

/**
 * Content validation rules
 */
const contentValidation = {
  createContent: [
    body('title')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 3, max: 100 })
      .escape()
      .withMessage('Title must be 3-100 characters'),
    body('description')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 10, max: 1000 })
      .escape()
      .withMessage('Description must be 10-1000 characters'),
    body('category')
      .notEmpty()
      .isIn(['Account Setup', 'Bots', 'Networking', 'Emotes', 'Streaming Platforms', 'Affiliate Roles', 'Console Help', 'PC Help'])
      .withMessage('Invalid category'),
    body('difficulty')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('videoUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Video URL must be a valid string'),
    body('embedUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Embed URL must be a valid string'),
    body('thumbnailUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Thumbnail URL must be a valid string'),
    body('isPublished')
      .optional()
      .isBoolean()
      .withMessage('isPublished must be a boolean'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed'),
    body('tags.*')
      .isString()
      .trim()
      .isLength({ min: 2, max: 30 })
      .matches(/^[a-zA-Z0-9-_\s]+$/)
      .withMessage('Tags must be 2-30 characters and contain only letters, numbers, hyphens, underscores, and spaces'),
    handleValidationErrors
  ],

  updateContent: [
    commonValidations.mongoId,
    body('title')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 3, max: 100 })
      .escape()
      .withMessage('Title must be 3-100 characters'),
    body('description')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 10, max: 1000 })
      .escape()
      .withMessage('Description must be 10-1000 characters'),
    body('category')
      .optional()
      .isIn(['Account Setup', 'Bots', 'Networking', 'Emotes', 'Streaming Platforms', 'Affiliate Roles', 'Console Help', 'PC Help'])
      .withMessage('Invalid category'),
    body('difficulty')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('videoUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Video URL must be a valid string'),
    body('embedUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Embed URL must be a valid string'),
    body('thumbnailUrl')
      .optional()
      .isString()
      .trim()
      .withMessage('Thumbnail URL must be a valid string'),
    body('isPublished')
      .optional()
      .isBoolean()
      .withMessage('isPublished must be a boolean'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed'),
    body('tags.*')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 2, max: 30 })
      .matches(/^[a-zA-Z0-9-_\s]+$/)
      .withMessage('Tags must be 2-30 characters and contain only letters, numbers, hyphens, underscores, and spaces'),
    handleValidationErrors
  ],

  getContentById: [
    commonValidations.mongoId,
    handleValidationErrors
  ],

  addComment: [
    commonValidations.mongoId,
    body('text')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 1, max: 1000 })
      .escape()
      .withMessage('Comment must be 1-1000 characters'),
    handleValidationErrors
  ],

  /**
   * Validation rules for comment editing and deletion
   */
  commentParams: [
    param('contentId')
      .isMongoId()
      .withMessage('Invalid content ID'),
    param('commentId')
      .isMongoId()
      .withMessage('Invalid comment ID'),
    handleValidationErrors
  ],

  editComment: [
    param('contentId').isMongoId().withMessage('Invalid content ID'),
    param('commentId').isMongoId().withMessage('Invalid comment ID'),
    body('text').notEmpty().isString().trim().isLength({ min: 1, max: 1000 }).escape().withMessage('Comment must be 1-1000 characters'),
    handleValidationErrors
  ],

  deleteComment: [
    param('contentId').isMongoId().withMessage('Invalid content ID'),
    param('commentId').isMongoId().withMessage('Invalid comment ID'),
    handleValidationErrors
  ]
};

module.exports = {
  handleValidationErrors,
  commonValidations,
  authValidation,
  userValidation,
  contentValidation
};
