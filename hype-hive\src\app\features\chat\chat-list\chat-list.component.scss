.chat-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.chat-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.connection-status {
  display: flex;
  align-items: center;
  background-color: #fff3cd;
  color: #856404;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;

  mat-icon {
    margin-right: 0.5rem;
  }
}

.auth-required {
  display: flex;
  align-items: center;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;

  mat-icon {
    margin-right: 0.5rem;
  }
}

.chat-list-content {
  flex: 1;
  overflow-y: auto;
}

.loading-container, .error-container, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
  
  button {
    margin-top: 1rem;
  }
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
  }
}

.empty-state {
  mat-icon {
    color: #ccc;
  }
  
  h3 {
    margin: 0.5rem 0;
    color: #333;
  }
}

.chat-item {
  display: flex;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  min-width: 0;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  
  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #e0e0e0;
    
    .chat-avatar .avatar-img {
      border-color: #3f51b5;
    }
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.chat-avatar {
  margin-right: 1rem;
  flex-shrink: 0;
  position: relative;
  
  .avatar-img {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e0e0e0;
    transition: border-color 0.2s ease;
    
    &:hover {
      border-color: #3f51b5;
    }
  }
  
  .discord-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #5865F2; // Discord brand color
    border: 2px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    
    mat-icon {
      font-size: 12px;
      width: 12px;
      height: 12px;
      color: white;
    }
  }
}

.chat-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
  min-width: 0;
  gap: 0.5rem;
  
  .chat-name {
    margin: 0;
    font-weight: 500;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
    max-width: calc(100% - 70px); // Reserve space for timestamp
  }
  
  .chat-time {
    font-size: 0.75rem;
    color: #888;
    white-space: nowrap;
    flex-shrink: 0;
    margin-left: auto;
    font-weight: 400;
  }
}

.chat-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
  gap: 0.5rem;
  
  .message-preview {
    margin: 0;
    font-size: 0.875rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
    max-width: calc(100% - 50px); // Reserve space for unread badge
    line-height: 1.3;
    
    &.unread {
      font-weight: 600;
      color: #333;
    }
  }
  
  .unread-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    background-color: #3f51b5;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
    padding: 0 6px;
    flex-shrink: 0;
    animation: pulse 2s infinite;
    
    .unread-count {
      font-size: 0.7rem;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(63, 81, 181, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(63, 81, 181, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(63, 81, 181, 0);
  }
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

// Responsive design for mobile
@media (max-width: 768px) {
  .chat-list-container {
    padding: 0.5rem;
  }
  
  .chat-item {
    padding: 0.75rem;
    margin-bottom: 0.375rem;
    
    .chat-avatar .avatar-img {
      width: 48px;
      height: 48px;
    }
    
    .discord-indicator {
      width: 16px;
      height: 16px;
      
      mat-icon {
        font-size: 10px;
        width: 10px;
        height: 10px;
      }
    }
  }
  
  .chat-header {
    .chat-name {
      font-size: 0.9rem;
      max-width: calc(100% - 60px);
    }
    
    .chat-time {
      font-size: 0.7rem;
    }
  }
  
  .chat-preview {
    .message-preview {
      font-size: 0.8rem;
      max-width: calc(100% - 40px);
    }
  }
}

@media (max-width: 480px) {
  .chat-list-header {
    h1 {
      font-size: 1.3rem;
    }
  }
  
  .chat-item {
    padding: 0.625rem;
    margin-bottom: 0.25rem;
    
    .chat-avatar .avatar-img {
      width: 44px;
      height: 44px;
    }
  }
}
