.recommended-content-container {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    color: #333;
    font-size: 1.8rem;
    margin: 0;
  }
  
  .view-all {
    color: #6441a5;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.content-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

.content-thumbnail {
  height: 160px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    color: #9e9e9e;
  }
  
  .content-category {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
  }
}

mat-card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.content-title {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: #333;
}

.content-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
  flex-grow: 1;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.content-author {
  display: flex;
  align-items: center;
  
  .author-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    margin-right: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #6441a5;
    
    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      color: white;
    }
  }
  
  span {
    font-size: 0.9rem;
    color: #555;
  }
}

.content-stats {
  display: flex;
  gap: 0.75rem;
  
  .views, .likes {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: #777;
    
    mat-icon {
      font-size: 1rem;
      height: 1rem;
      width: 1rem;
      margin-right: 0.25rem;
    }
  }
}

.content-difficulty {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: capitalize;
  
  &.difficulty-beginner {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
  
  &.difficulty-intermediate {
    background-color: #fff8e1;
    color: #ff8f00;
  }
  
  &.difficulty-advanced {
    background-color: #ffebee;
    color: #c62828;
  }
}

.loading-container, .error-container, .no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
  }
  
  p {
    color: #666;
    margin-bottom: 1rem;
  }
}

.error-container mat-icon {
  color: #f44336;
}

.no-content mat-icon {
  color: #9e9e9e;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}
