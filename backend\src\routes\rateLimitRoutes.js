const express = require('express');
const {
  getRateLimitStats,
  clearRateLimitData,
  getTrustedIPs,
  getSuspiciousIPs
} = require('../controllers/rateLimitController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All rate limit management routes require admin access
router.use(protect, authorize('admin'));

// GET /api/admin/rate-limit/stats - Get rate limiting statistics
router.get('/stats', getRateLimitStats);

// POST /api/admin/rate-limit/clear - Clear rate limiting data
router.post('/clear', clearRateLimitData);

// GET /api/admin/rate-limit/trusted-ips - Get trusted IPs
router.get('/trusted-ips', getTrustedIPs);

// GET /api/admin/rate-limit/suspicious-ips - Get suspicious/blocked IPs
router.get('/suspicious-ips', getSuspiciousIPs);

module.exports = router;
