import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { Router, RouterModule } from '@angular/router';

import { AuthService } from '../../../core/services/auth.service';
import { User, InstructionCategory } from '../../../core/models/user.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    RouterModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent implements OnInit {
  user: User | null = null;
  isLoading = true;
  errorMessage = '';
  instructionCategories = Object.values(InstructionCategory);

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.currentUser$.subscribe({
      next: (user) => {
        this.user = user;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load profile. Please try again.';
        this.isLoading = false;
        console.error('Profile loading error:', error);
      }
    });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/']);
  }

  /**
   * Check if the user has proficiencies in a specific category
   */
  hasProficienciesInCategory(category: InstructionCategory): boolean {
    if (!this.user || !this.user.proficiencies) return false;
    return this.user.proficiencies.some(p => p.category === category && p.isSelected);
  }

  /**
   * Get proficiencies for a specific category
   */
  getProficienciesForCategory(category: InstructionCategory): string[] {
    if (!this.user || !this.user.proficiencies) return [];
    return this.user.proficiencies
      .filter(p => p.category === category && p.isSelected)
      .map(p => p.name);
  }

  hasSelectedProficiencies(): boolean {
    return !!this.user && Array.isArray(this.user.proficiencies) && this.user.proficiencies.length > 0 && this.user.proficiencies.some(p => p && p.isSelected);
  }

  /**
   * Check if user can apply to be a mentor
   */
  canApplyForMentor(): boolean {
    if (!this.user) return false;
    if (this.user.isMentor) return false;

    const applicationStatus = this.user.mentorApplication?.status || 'none';
    return applicationStatus === 'none' || applicationStatus === 'rejected';
  }

  /**
   * Get mentor application status display text
   */
  getMentorApplicationStatusText(): string {
    if (!this.user?.mentorApplication) return '';

    switch (this.user.mentorApplication.status) {
      case 'pending':
        return 'Application Pending Review';
      case 'approved':
        return 'Application Approved';
      case 'rejected':
        return 'Application Rejected';
      default:
        return '';
    }
  }

  /**
   * Get mentor application status color
   */
  getMentorApplicationStatusColor(): string {
    if (!this.user?.mentorApplication) return '';

    switch (this.user.mentorApplication.status) {
      case 'pending':
        return 'warn';
      case 'approved':
        return 'primary';
      case 'rejected':
        return 'warn';
      default:
        return '';
    }
  }

  /**
   * Navigate to mentor application form
   */
  applyForMentor(): void {
    this.router.navigate(['/mentors/apply']);
  }
}
