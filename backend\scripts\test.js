#!/usr/bin/env node

/**
 * Test runner script for HypeHive backend
 * Provides different test execution modes and utilities
 */

const { spawn } = require('child_process');
const path = require('path');

// Test execution modes
const TEST_MODES = {
  unit: 'Run unit tests only',
  integration: 'Run integration tests only',
  all: 'Run all tests',
  watch: 'Run tests in watch mode',
  coverage: 'Run tests with coverage report',
  ci: 'Run tests in CI mode (no watch, with coverage)'
};

function showHelp() {
  console.log('\n🧪 HypeHive Backend Test Runner\n');
  console.log('Usage: npm run test [mode] [options]\n');
  console.log('Available modes:');
  Object.entries(TEST_MODES).forEach(([mode, description]) => {
    console.log(`  ${mode.padEnd(12)} - ${description}`);
  });
  console.log('\nExamples:');
  console.log('  npm run test unit');
  console.log('  npm run test integration');
  console.log('  npm run test coverage');
  console.log('  npm run test watch');
  console.log('  npm run test ci\n');
}

function runJest(args) {
  // Use the Windows-compatible Jest binary if on Windows
  const isWindows = process.platform === 'win32';
  const jestPath = isWindows
    ? path.join(__dirname, '../node_modules/.bin/jest.cmd')
    : path.join(__dirname, '../node_modules/.bin/jest');

  const jestProcess = spawn(isWindows ? 'cmd' : 'node',
    isWindows ? ['/c', jestPath, ...args] : [jestPath, ...args],
    {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    }
  );

  jestProcess.on('close', (code) => {
    process.exit(code);
  });

  jestProcess.on('error', (error) => {
    console.error('Failed to start Jest:', error);
    process.exit(1);
  });
}

function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'all';

  if (mode === 'help' || mode === '--help' || mode === '-h') {
    showHelp();
    return;
  }

  let jestArgs = [];

  switch (mode) {
    case 'unit':
      jestArgs = ['--testPathPattern=tests/(models|services|utils)', '--verbose'];
      break;

    case 'integration':
      jestArgs = ['--testPathPattern=tests/integration', '--verbose', '--runInBand'];
      break;

    case 'watch':
      jestArgs = ['--watch', '--verbose'];
      break;

    case 'coverage':
      jestArgs = ['--coverage', '--verbose'];
      break;

    case 'ci':
      jestArgs = ['--coverage', '--ci', '--watchAll=false', '--verbose'];
      break;

    case 'all':
    default:
      jestArgs = ['--verbose'];
      break;
  }

  // Add any additional arguments passed to the script
  if (args.length > 1) {
    jestArgs.push(...args.slice(1));
  }

  console.log(`\n🧪 Running tests in '${mode}' mode...\n`);
  runJest(jestArgs);
}

if (require.main === module) {
  main();
}

module.exports = { runJest, TEST_MODES };
