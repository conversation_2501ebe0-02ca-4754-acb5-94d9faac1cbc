.content-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;

    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 500;
      color: #333;
    }

    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.filters-card {
  margin-bottom: 30px;

  .filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.content-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .content-thumbnail {
    height: 180px;
    background-size: cover;
    background-position: center;
    background-color: #f5f5f5;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .thumbnail-overlay {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.1);

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #666;
      }
    }

    .difficulty-badge {
      position: absolute;
      top: 12px;
      right: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;

      &.difficulty-beginner {
        background-color: #4caf50;
        color: white;
      }

      &.difficulty-intermediate {
        background-color: #ff9800;
        color: white;
      }

      &.difficulty-advanced {
        background-color: #f44336;
        color: white;
      }
    }
  }

  mat-card-content {
    padding: 16px;

    .content-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
      color: #333;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .content-description {
      margin: 0 0 16px 0;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }

    .content-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .category-chip {
        background-color: #e3f2fd;
        color: #1976d2;
        font-size: 12px;
        height: 24px;
      }

      .content-stats {
        display: flex;
        gap: 12px;

        .stat {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #666;

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }
      }
    }

    .content-author {
      display: flex;
      align-items: center;
      gap: 8px;

      .author-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
      }

      .author-name {
        font-size: 14px;
        color: #666;
        flex: 1;
      }

      .mentor-badge {
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: #4caf50;
      }
    }
  }
}

.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h2 {
    margin: 0 0 8px 0;
    color: #666;
  }

  p {
    margin: 0 0 24px 0;
    color: #999;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .content-list-container {
    padding: 16px;

    .header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      h1 {
        font-size: 24px;
        text-align: center;
      }
    }
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}