.mentor-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.back-button {
  margin-bottom: 1.5rem;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
  }
  
  p {
    color: #666;
    margin-bottom: 1rem;
  }
}

.error-container mat-icon {
  color: #f44336;
}

.profile-header {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  
  .profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #6441a5;
    color: white;
    flex-shrink: 0;
    
    mat-icon {
      font-size: 4rem;
      height: 4rem;
      width: 4rem;
    }
  }
  
  .profile-info {
    display: flex;
    flex-direction: column;
    
    h1 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 2.5rem;
    }
    
    .mentor-badge {
      background-color: #6441a5;
      color: white;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 0.9rem;
      display: inline-block;
      margin-bottom: 1rem;
    }
    
    .social-links {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
      
      a {
        color: #6441a5;
      }
    }
    
    .contact-button {
      align-self: flex-start;
    }
  }
}

.tab-content {
  padding: 2rem 0;
}

.bio-section, .proficiencies-section {
  margin-bottom: 2rem;
  
  h2 {
    color: #6441a5;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
  
  p {
    color: #555;
    line-height: 1.6;
  }
}

.category-section {
  margin-bottom: 1.5rem;
  
  h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
  }
  
  .proficiency-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

.no-proficiencies {
  font-style: italic;
  color: #999;
}

.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem;
  text-align: center;
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
    color: #9e9e9e;
  }
  
  p {
    color: #666;
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.content-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .content-thumbnail {
    height: 160px;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    
    mat-icon {
      font-size: 3rem;
      height: 3rem;
      width: 3rem;
      color: #9e9e9e;
    }
  }
  
  .content-info {
    padding: 1rem;
    
    h3 {
      margin-bottom: 0.5rem;
      color: #333;
    }
    
    p {
      color: #666;
      margin-bottom: 1rem;
      line-height: 1.4;
    }
    
    .content-meta {
      display: flex;
      justify-content: space-between;
      color: #888;
      font-size: 0.9rem;
      
      .views {
        display: flex;
        align-items: center;
        
        mat-icon {
          font-size: 1rem;
          height: 1rem;
          width: 1rem;
          margin-right: 0.25rem;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .profile-info {
      align-items: center;
      
      .contact-button {
        align-self: center;
      }
    }
  }
}
