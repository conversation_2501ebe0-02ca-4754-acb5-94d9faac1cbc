import { Injectable, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { isPlatformBrowser } from '@angular/common';
import { Router, NavigationEnd } from '@angular/router';
import { environment } from '../../../environments/environment';
import { filter } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class MonitoringService implements ErrorHandler {
  private readonly apiUrl = `${environment.apiUrl}/monitoring`;
  private isEnabled = environment.monitoring?.errorTrackingEnabled || false;
  private analyticsEnabled = environment.monitoring?.analyticsEnabled || false;
  private performanceMetrics = new BehaviorSubject<PerformanceMetric[]>([]);

  // Real-time metrics
  private socketLatency = new BehaviorSubject<number>(0);
  private messageDeliveryTime = new BehaviorSubject<number>(0);
  private socketErrorRate = new BehaviorSubject<number>(0);
  private activeConnections = new BehaviorSubject<number>(0);

  constructor(
    private http: HttpClient,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.setupRouteTracking();
  }

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  /**
   * Handle errors and send them to the monitoring service
   * @param error Error to handle
   */
  handleError(error: any): void {
    // Log error to console
    console.error('Error caught by monitoring service:', error);

    // If monitoring is disabled or not in browser, don't send to server
    if (!this.isEnabled || !this.isBrowser()) {
      return;
    }

    // Prepare error data
    const errorData = {
      message: error.message || 'Unknown error',
      stack: error.stack || '',
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    // Send error to server
    this.http.post(`${this.apiUrl}/errors`, errorData)
      .subscribe({
        error: (err) => console.error('Failed to send error to monitoring service:', err)
      });
  }

  /**
   * Track page views
   */
  private setupRouteTracking(): void {
    if (!this.analyticsEnabled || !this.isBrowser()) {
      return;
    }

    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      const pageViewData = {
        url: event.urlAfterRedirects,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      };

      this.http.post(`${this.apiUrl}/pageviews`, pageViewData)
        .subscribe({
          error: (err) => console.error('Failed to send page view to monitoring service:', err)
        });
    });
  }

  /**
   * Track custom events
   * @param eventName Event name
   * @param eventData Event data
   */
  trackEvent(eventName: string, eventData: any = {}): void {
    if (!this.analyticsEnabled || !this.isBrowser()) {
      return;
    }

    const data = {
      name: eventName,
      data: eventData,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.http.post(`${this.apiUrl}/events`, data)
      .subscribe({
        error: (err) => console.error('Failed to send event to monitoring service:', err)
      });

    // Track real-time metrics for specific events
    this.trackRealTimeMetrics(eventName, eventData);
  }

  /**
   * Track errors specifically for monitoring
   * @param errorType Error type
   * @param error Error object
   */
  trackError(errorType: string, error: any): void {
    console.error(`${errorType}:`, error);

    if (!this.isEnabled || !this.isBrowser()) {
      return;
    }

    // Prepare error data
    const errorData = {
      type: errorType,
      message: error.message || 'Unknown error',
      stack: error.stack || '',
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    // Send error to server
    this.http.post(`${this.apiUrl}/errors`, errorData)
      .subscribe({
        error: (err) => console.error('Failed to send error to monitoring service:', err)
      });

    // Update error rate for socket errors
    if (errorType.includes('socket')) {
      this.socketErrorRate.next(this.socketErrorRate.value + 1);
    }
  }

  /**
   * Track real-time metrics based on events
   * @param eventName Event name
   * @param eventData Event data
   */
  private trackRealTimeMetrics(eventName: string, eventData: any): void {
    switch (eventName) {
      case 'socket_connected':
        this.activeConnections.next(this.activeConnections.value + 1);
        break;

      case 'socket_disconnected':
        this.activeConnections.next(Math.max(0, this.activeConnections.value - 1));
        break;

      case 'socket_latency':
        if (eventData.latency) {
          this.socketLatency.next(eventData.latency);
          this.addPerformanceMetric('socket_latency', eventData.latency);
        }
        break;

      case 'message_delivery_time':
        if (eventData.deliveryTime) {
          this.messageDeliveryTime.next(eventData.deliveryTime);
          this.addPerformanceMetric('message_delivery_time', eventData.deliveryTime);
        }
        break;
    }
  }

  /**
   * Add a performance metric to the metrics collection
   * @param name Metric name
   * @param value Metric value
   */
  private addPerformanceMetric(name: string, value: number): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: new Date().toISOString()
    };

    const currentMetrics = this.performanceMetrics.value;

    // Keep only the last 100 metrics
    const updatedMetrics = [metric, ...currentMetrics].slice(0, 100);

    this.performanceMetrics.next(updatedMetrics);
  }

  /**
   * Get socket latency
   * @returns Observable of socket latency
   */
  getSocketLatency(): Observable<number> {
    return this.socketLatency.asObservable();
  }

  /**
   * Get message delivery time
   * @returns Observable of message delivery time
   */
  getMessageDeliveryTime(): Observable<number> {
    return this.messageDeliveryTime.asObservable();
  }

  /**
   * Get socket error rate
   * @returns Observable of socket error rate
   */
  getSocketErrorRate(): Observable<number> {
    return this.socketErrorRate.asObservable();
  }

  /**
   * Get active connections
   * @returns Observable of active connections
   */
  getActiveConnections(): Observable<number> {
    return this.activeConnections.asObservable();
  }

  /**
   * Get performance metrics
   * @returns Observable of performance metrics
   */
  getPerformanceMetrics(): Observable<PerformanceMetric[]> {
    return this.performanceMetrics.asObservable();
  }
}
