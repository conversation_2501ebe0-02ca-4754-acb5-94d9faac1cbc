# System Admin Configuration

This document describes the system administrator configuration for HypeHive.

## System Administrator

**Discord User:** captain.sexy  
**Discord ID:** 323345923964928001  
**Privileges:** Full system administrator access

## Admin Privileges

System administrators have the following privileges:

### 1. **Rate Limiting Bypass**
- All rate limiting is bypassed for system administrators
- No request throttling on any endpoints
- Unlimited API access

### 2. **Role-Based Access Control Bypass**
- Access to all admin-only endpoints
- Automatic promotion to admin role on login
- Bypass all authorization checks

### 3. **Content Management**
- Can edit/delete any user's content
- Full moderation capabilities
- Override all content restrictions

### 4. **User Management**
- Can edit any user's profile
- Access to all user data
- User role management

### 5. **System Monitoring**
- Access to rate limiting statistics
- Error monitoring and logs
- System health dashboards

## Implementation Details

### Security Middleware
The system admin configuration is implemented in `src/middleware/security.js`:

```javascript
// Admin Discord IDs that have full system privileges
const ADMIN_DISCORD_IDS = [
  '323345923964928001', // captain.sexy - System Administrator
];
```

### Authentication Middleware
Auto-promotion to admin role is handled in `src/middleware/auth.js`:

```javascript
// Auto-promote system administrators
if (isSystemAdmin(user.discordId) && user.role !== 'admin') {
  await User.findByIdAndUpdate(user._id, { 
    role: 'admin',
    isMentor: true
  });
  user.role = 'admin';
  user.isMentor = true;
}
```

### Authorization Bypass
System admins bypass all role-based restrictions:

```javascript
// System admins have access to everything
if (isSystemAdmin(req.user.discordId)) {
  return next();
}
```

## Setup Instructions

### 1. Initial Admin Setup
Run the admin setup script to create/update admin users in the database:

```bash
npm run setup:admin
```

### 2. Manual Database Setup
If needed, you can manually create an admin user:

```javascript
const user = await User.findOneAndUpdate(
  { discordId: '323345923964928001' },
  {
    discordId: '323345923964928001',
    discordUsername: 'captain.sexy',
    role: 'admin',
    isMentor: true,
    isActive: true,
    'mentorApplication.status': 'approved'
  },
  { upsert: true, new: true }
);
```

## Security Considerations

1. **Discord ID Verification**: The system relies on Discord OAuth for authentication
2. **Database Consistency**: Admin status is stored in the database but overridden by Discord ID
3. **Audit Logging**: All admin actions are logged for security monitoring
4. **Rate Limiting**: Admins bypass rate limits but access is still logged

## Adding New Admins

To add a new system administrator:

1. Add their Discord ID to the `ADMIN_DISCORD_IDS` array in `src/middleware/security.js`
2. Run `npm run setup:admin` to update the database
3. The user will be automatically promoted on their next login

## Removing Admin Access

To remove admin access:

1. Remove the Discord ID from `ADMIN_DISCORD_IDS` array
2. Update the user's role in the database manually
3. Restart the server to apply changes

## Monitoring Admin Activity

Admin actions are logged with the following format:

```
System admin access: captain.sexy (323345923964928001)
{
  method: 'POST',
  url: '/api/admin/rate-limit/clear',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0...'
}
```

Check the application logs for admin activity monitoring.

