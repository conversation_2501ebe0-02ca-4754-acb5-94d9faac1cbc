<div class="content-list-container">
  <div class="header">
    <h1>Educational Content</h1>
    <button
      mat-raised-button
      color="primary"
      routerLink="/content/upload"
      *ngIf="canCreateContent">
      <mat-icon>add</mat-icon>
      Create Content
    </button>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search content</mat-label>
          <input matInput [(ngModel)]="searchTerm" (input)="onSearch()" placeholder="Search by title or description">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="category-field">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="selectedCategory" (selectionChange)="onCategoryChange()">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="difficulty-field">
          <mat-label>Difficulty</mat-label>
          <mat-select [(ngModel)]="selectedDifficulty" (selectionChange)="onDifficultyChange()">
            <mat-option value="">All Levels</mat-option>
            <mat-option value="beginner">Beginner</mat-option>
            <mat-option value="intermediate">Intermediate</mat-option>
            <mat-option value="advanced">Advanced</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading content...</p>
  </div>

  <!-- Content Grid -->
  <div class="content-grid" *ngIf="!isLoading && content.length > 0">
    <mat-card class="content-card" *ngFor="let item of content" [routerLink]="['/content', item._id]">
      <div class="content-thumbnail" [style.background-image]="item.thumbnailUrl ? 'url(' + item.thumbnailUrl + ')' : ''">
        <div class="thumbnail-overlay" *ngIf="!item.thumbnailUrl">
          <mat-icon>video_library</mat-icon>
        </div>
        <div class="difficulty-badge" [class]="'difficulty-' + item.difficulty">
          {{ item.difficulty }}
        </div>
      </div>

      <mat-card-content>
        <h3 class="content-title">{{ item.title }}</h3>
        <p class="content-description">{{ item.description | slice:0:120 }}{{ item.description.length > 120 ? '...' : '' }}</p>

        <div class="content-meta">
          <mat-chip class="category-chip">{{ item.category }}</mat-chip>
          <div class="content-stats">
            <span class="stat">
              <mat-icon>visibility</mat-icon>
              {{ item.views }}
            </span>
            <span class="stat">
              <mat-icon>thumb_up</mat-icon>
              {{ item.likes }}
            </span>
          </div>
        </div>

        <div class="content-author">
          <img
            [src]="item.createdBy?.discordAvatar || '/assets/default-avatar.png'"
            [alt]="item.createdBy?.discordUsername"
            class="author-avatar">
          <span class="author-name">{{ item.createdBy?.discordUsername }}</span>
          <mat-icon class="mentor-badge" *ngIf="item.createdBy?.isMentor">verified</mat-icon>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- No Content -->
  <div class="no-content" *ngIf="!isLoading && content.length === 0">
    <mat-icon>video_library</mat-icon>
    <h2>No content found</h2>
    <p *ngIf="hasActiveFilters">Try adjusting your search filters</p>
    <p *ngIf="!hasActiveFilters">No educational content has been published yet.</p>
    <button
      mat-raised-button
      color="primary"
      routerLink="/content/upload"
      *ngIf="canCreateContent && !hasActiveFilters">
      Create the first content
    </button>
  </div>

  <!-- Pagination -->
  <mat-paginator
    *ngIf="!isLoading && content.length > 0"
    [length]="totalItems"
    [pageSize]="pageSize"
    [pageSizeOptions]="[12, 24, 48]"
    [pageIndex]="currentPage"
    (page)="onPageChange($event)"
    showFirstLastButtons>
  </mat-paginator>
</div>
