/**
 * Build cleanup script for HypeHive
 * 
 * This script cleans up build artifacts and prepares for a fresh build
 */

const fs = require('fs-extra');
const path = require('path');
const rimraf = require('rimraf');

const paths = {
  dist: path.join(__dirname, '../dist'),
  nodeModules: path.join(__dirname, '../node_modules/.cache'),
  coverage: path.join(__dirname, '../coverage'),
  logs: path.join(__dirname, '../logs'),
  temp: path.join(__dirname, '../temp')
};

async function cleanBuildArtifacts() {
  console.log('🧹 Starting build cleanup...');
  
  const cleanupTasks = [
    { name: 'Distribution folder', path: paths.dist },
    { name: 'Node modules cache', path: paths.nodeModules },
    { name: 'Coverage reports', path: paths.coverage },
    { name: 'Temporary files', path: paths.temp }
  ];

  for (const task of cleanupTasks) {
    try {
      if (await fs.pathExists(task.path)) {
        await fs.remove(task.path);
        console.log(`✅ Cleaned ${task.name}`);
      } else {
        console.log(`⏭️  Skipped ${task.name} (doesn't exist)`);
      }
    } catch (error) {
      console.error(`❌ Error cleaning ${task.name}:`, error.message);
    }
  }

  // Create fresh dist directory
  try {
    await fs.ensureDir(paths.dist);
    console.log('📁 Created fresh dist directory');
  } catch (error) {
    console.error('❌ Error creating dist directory:', error.message);
  }

  console.log('🎉 Build cleanup completed!');
}

async function clearAngularCache() {
  console.log('🗑️  Clearing Angular cache...');
  
  try {
    const angularCachePath = path.join(__dirname, '../.angular');
    if (await fs.pathExists(angularCachePath)) {
      await fs.remove(angularCachePath);
      console.log('✅ Angular cache cleared');
    } else {
      console.log('⏭️  Angular cache not found');
    }
  } catch (error) {
    console.error('❌ Error clearing Angular cache:', error.message);
  }
}

async function createVersionFile() {
  console.log('📋 Creating version file...');
  
  try {
    const packageJson = await fs.readJson(path.join(__dirname, '../package.json'));
    const versionInfo = {
      version: packageJson.version,
      buildDate: new Date().toISOString(),
      buildNumber: process.env.BUILD_NUMBER || 'local',
      gitCommit: process.env.GIT_COMMIT || 'unknown',
      environment: process.env.NODE_ENV || 'production'
    };

    await fs.writeJson(path.join(__dirname, '../src/assets/version.json'), versionInfo, { spaces: 2 });
    console.log('✅ Version file created');
  } catch (error) {
    console.error('❌ Error creating version file:', error.message);
  }
}

async function main() {
  try {
    await cleanBuildArtifacts();
    await clearAngularCache();
    await createVersionFile();
    console.log('🚀 Ready for build!');
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { cleanBuildArtifacts, clearAngularCache, createVersionFile };
