module.exports = {
  clientId: process.env.DISCORD_CLIENT_ID,
  clientSecret: process.env.DISCORD_CLIENT_SECRET,
  redirectUri: process.env.DISCORD_REDIRECT_URI,
  apiEndpoint: process.env.DISCORD_API_ENDPOINT,
  tokenURL: `${process.env.DISCORD_API_ENDPOINT}/oauth2/token`,
  userURL: `${process.env.DISCORD_API_ENDPOINT}/users/@me`,
  scope: 'identify email',
  // Bot credentials and admin role config
  botToken: process.env.DISCORD_BOT_TOKEN,
  adminGuildId: process.env.DISCORD_ADMIN_GUILD_ID,
  adminRoleId: process.env.DISCORD_ADMIN_ROLE_ID
};
