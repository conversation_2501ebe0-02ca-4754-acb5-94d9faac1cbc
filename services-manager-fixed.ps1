#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Advanced HypeHive Services Manager with comprehensive logging and monitoring
.DESCRIPTION
    Manages all HypeHive services with advanced features:
    - Concurrent service startup
    - Health monitoring
    - Automatic restart on failure
    - Centralized logging
    - Graceful shutdown
.PARAMETER Action
    Action to perform: start, stop, restart, status, logs
.PARAMETER Mode
    Service mode: dev, prod
.PARAMETER Services
    Specific services to manage: backend, frontend, discord, all
.PARAMETER Follow
    Follow log output in real-time
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet('start', 'stop', 'restart', 'status', 'logs')]
    [string]$Action = 'start',
    
    [Parameter(Mandatory=$false)]
    [ValidateSet('dev', 'prod')]
    [string]$Mode = 'dev',
    
    [Parameter(Mandatory=$false)]
    [ValidateSet('backend', 'frontend', 'discord', 'all')]
    [string[]]$Services = @('all'),
    
    [Parameter(Mandatory=$false)]
    [switch]$Follow
)

# Configuration
$SCRIPT_DIR = $PSScriptRoot
$PID_FILE = Join-Path $SCRIPT_DIR "services.pid"
$LOG_DIR = Join-Path $SCRIPT_DIR "logs"

# Service definitions
$ServiceConfig = @{
    'backend' = @{
        'name' = 'HypeHive Backend'
        'directory' = Join-Path $SCRIPT_DIR "backend"
        'command' = @{
            'dev' = 'npm run dev'
            'prod' = 'npm start'
        }
        'port' = 3000
        'logFile' = Join-Path $LOG_DIR "backend.log"
        'healthEndpoint' = 'http://localhost:3000/health'
    }
    'frontend' = @{
        'name' = 'HypeHive Frontend SSR'
        'directory' = Join-Path $SCRIPT_DIR "hype-hive"
        'command' = @{
            'dev' = 'npm run start:local'
            'prod' = 'npm run serve:ssr:hype-hive'
        }
        'port' = 4000
        'logFile' = Join-Path $LOG_DIR "frontend.log"
        'healthEndpoint' = 'http://localhost:4000'
    }
    'discord' = @{
        'name' = 'HypeHive Discord Bot'
        'directory' = Join-Path $SCRIPT_DIR "discord-bot"
        'command' = @{
            'dev' = 'python start.py'
            'prod' = 'python start.py'
        }
        'port' = $null
        'logFile' = Join-Path $LOG_DIR "discord-bot.log"
        'healthEndpoint' = $null
    }
}

# Global variables
$RunningServices = @{}
$MASTER_LOG = Join-Path $LOG_DIR "services-master.log"

# Create directories
if (-not (Test-Path $LOG_DIR)) {
    New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
}

function Write-ServiceLog {
    param(
        [string]$Message,
        [string]$Level = 'INFO',
        [string]$Service = 'MASTER'
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] [$Service] $Message"
    
    # Write to master log
    Add-Content -Path $MASTER_LOG -Value $logEntry
    
    # Color coding for console output
    $color = switch ($Level) {
        'INFO' { 'Green' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'DEBUG' { 'Cyan' }
        default { 'White' }
    }
    
    Write-Host $logEntry -ForegroundColor $color
}

function Test-ServiceHealth {
    param(
        [string]$ServiceName
    )
    
    $config = $ServiceConfig[$ServiceName]
    
    # Check if process is running
    $process = $RunningServices[$ServiceName]
    if (-not $process -or $process.HasExited) {
        return $false
    }
    
    # Check health endpoint if available
    if ($config.healthEndpoint) {
        try {
            $response = Invoke-WebRequest -Uri $config.healthEndpoint -TimeoutSec 5 -UseBasicParsing
            return $response.StatusCode -eq 200
        } catch {
            return $false
        }
    }
    
    return $true
}

function Start-ServiceProcess {
    param(
        [string]$ServiceName
    )
    
    $config = $ServiceConfig[$ServiceName]
    Write-ServiceLog "Starting $($config.name)..." -Service $ServiceName
    
    # Check if already running
    if (Test-ServiceHealth $ServiceName) {
        Write-ServiceLog "$($config.name) is already running" -Level 'WARN' -Service $ServiceName
        return $true
    }
    
    try {
        Push-Location $config.directory
        
        # Clear previous log
        if (Test-Path $config.logFile) {
            Clear-Content $config.logFile
        }
        
        # Start the service using Start-Process for better control
        $command = $config.command[$Mode]
        $argumentList = @()
        
        # Split command into executable and arguments
        if ($command -match '^(npm|node|python)\s+(.+)$') {
            $executable = $matches[1]
            $arguments = $matches[2]
            $argumentList = $arguments -split '\s+'
        } else {
            $executable = 'cmd'
            $argumentList = @('/c', $command)
        }
        
        # Start the process
        $process = Start-Process -FilePath $executable -ArgumentList $argumentList -WorkingDirectory $config.directory -PassThru -NoNewWindow -RedirectStandardOutput $config.logFile -RedirectStandardError $config.logFile
        
        if ($process) {
            $RunningServices[$ServiceName] = $process
            Write-ServiceLog "$($config.name) started successfully (PID: $($process.Id))" -Service $ServiceName
            
            # Wait a moment for service to initialize
            Start-Sleep -Seconds 2
            return $true
        } else {
            Write-ServiceLog "Failed to start $($config.name) - process not created" -Level 'ERROR' -Service $ServiceName
            return $false
        }
    } catch {
        Write-ServiceLog "Failed to start $($config.name): $_" -Level 'ERROR' -Service $ServiceName
        return $false
    } finally {
        Pop-Location
    }
}

function Stop-ServiceProcess {
    param(
        [string]$ServiceName
    )
    
    $config = $ServiceConfig[$ServiceName]
    $process = $RunningServices[$ServiceName]
    
    if ($process -and -not $process.HasExited) {
        Write-ServiceLog "Stopping $($config.name)..." -Service $ServiceName
        try {
            $process.Kill()
            $process.WaitForExit(5000)
            Write-ServiceLog "$($config.name) stopped successfully" -Service $ServiceName
        } catch {
            Write-ServiceLog "Failed to stop $($config.name): $_" -Level 'ERROR' -Service $ServiceName
        }
        $RunningServices.Remove($ServiceName)
    } else {
        Write-ServiceLog "$($config.name) is not running" -Level 'WARN' -Service $ServiceName
    }
}

function Get-ServiceStatus {
    param(
        [string]$ServiceName
    )
    
    $config = $ServiceConfig[$ServiceName]
    $isHealthy = Test-ServiceHealth $ServiceName
    $process = $RunningServices[$ServiceName]
    
    $status = @{
        'name' = $config.name
        'running' = $isHealthy
        'pid' = if ($process -and -not $process.HasExited) { $process.Id } else { $null }
        'port' = $config.port
        'logFile' = $config.logFile
    }
    
    return $status
}

function Show-ServiceStatus {
    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "HypeHive Services Status" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    foreach ($serviceName in $ServiceConfig.Keys) {
        $status = Get-ServiceStatus $serviceName
        $statusText = if ($status.running) { "RUNNING" } else { "STOPPED" }
        $statusColor = if ($status.running) { "Green" } else { "Red" }
        
        Write-Host "`n$($status.name):" -ForegroundColor White
        Write-Host "  Status: $statusText" -ForegroundColor $statusColor
        if ($status.pid) {
            Write-Host "  PID: $($status.pid)" -ForegroundColor Gray
        }
        if ($status.port) {
            Write-Host "  Port: $($status.port)" -ForegroundColor Gray
        }
        Write-Host "  Log: $($status.logFile)" -ForegroundColor Gray
    }
    
    Write-Host "`n========================================" -ForegroundColor Cyan
}

function Show-ServiceLogs {
    param(
        [string]$ServiceName = 'all'
    )
    
    if ($ServiceName -eq 'all') {
        Write-Host "Master Log:" -ForegroundColor Cyan
        if (Test-Path $MASTER_LOG) {
            Get-Content $MASTER_LOG -Tail 50
        }
        
        foreach ($service in $ServiceConfig.Keys) {
            $logFile = $ServiceConfig[$service].logFile
            if (Test-Path $logFile) {
                Write-Host "`n$($ServiceConfig[$service].name) Log:" -ForegroundColor Cyan
                Get-Content $logFile -Tail 20
            }
        }
    } else {
        $logFile = $ServiceConfig[$ServiceName].logFile
        if (Test-Path $logFile) {
            if ($Follow) {
                Get-Content $logFile -Wait -Tail 50
            } else {
                Get-Content $logFile -Tail 50
            }
        } else {
            Write-Host "Log file not found: $logFile" -ForegroundColor Red
        }
    }
}

function Start-ServiceMonitoring {
    Write-ServiceLog "Starting service monitoring..." -Level 'INFO'
    
    while ($true) {
        Start-Sleep -Seconds 30
        
        foreach ($serviceName in $ServiceConfig.Keys) {
            if ($RunningServices.ContainsKey($serviceName)) {
                if (-not (Test-ServiceHealth $serviceName)) {
                    Write-ServiceLog "Service $serviceName is unhealthy. Attempting restart..." -Level 'WARN' -Service $serviceName
                    Stop-ServiceProcess $serviceName
                    Start-Sleep -Seconds 5
                    Start-ServiceProcess $serviceName
                }
            }
        }
    }
}

function Save-ServicePIDs {
    $serviceData = @{}
    foreach ($serviceName in $RunningServices.Keys) {
        $process = $RunningServices[$serviceName]
        if ($process -and -not $process.HasExited) {
            $serviceData[$serviceName] = $process.Id
        }
    }
    
    $serviceData | ConvertTo-Json | Set-Content $PID_FILE
}

function Restore-ServicePIDs {
    if (Test-Path $PID_FILE) {
        try {
            $serviceData = Get-Content $PID_FILE | ConvertFrom-Json
            foreach ($serviceName in $serviceData.PSObject.Properties.Name) {
                $processId = $serviceData.$serviceName
                try {
                    $process = Get-Process -Id $processId -ErrorAction Stop
                    $RunningServices[$serviceName] = $process
                    Write-ServiceLog "Restored existing service $serviceName (PID: $processId)" -Service $serviceName
                } catch {
                    Write-ServiceLog "Service $serviceName (PID: $processId) is no longer running" -Level 'WARN' -Service $serviceName
                }
            }
        } catch {
            Write-ServiceLog "Failed to restore service PIDs: $_" -Level 'ERROR'
        }
    }
}

function Clear-AllServices {
    Write-ServiceLog "Stopping all services..." -Level 'INFO'
    
    foreach ($serviceName in $ServiceConfig.Keys) {
        Stop-ServiceProcess $serviceName
    }
    
    if (Test-Path $PID_FILE) {
        Remove-Item $PID_FILE -Force
    }
}

# Main execution logic
function Invoke-Main {
    # Load existing services
    Restore-ServicePIDs
    
    # Handle Ctrl+C
    $null = Register-ObjectEvent -InputObject ([System.Console]) -EventName CancelKeyPress -Action {
        Write-ServiceLog "Received shutdown signal..." -Level 'INFO'
        Clear-AllServices
        [System.Environment]::Exit(0)
    }
    
    switch ($Action) {
        'start' {
            Write-ServiceLog "Starting HypeHive services in $Mode mode..." -Level 'INFO'
            
            $servicesToStart = if ($Services -contains 'all') { $ServiceConfig.Keys } else { $Services }
            
            foreach ($serviceName in $servicesToStart) {
                if ($ServiceConfig.ContainsKey($serviceName)) {
                    Start-ServiceProcess $serviceName
                } else {
                    Write-ServiceLog "Unknown service: $serviceName" -Level 'ERROR'
                }
            }
            
            Save-ServicePIDs
            Show-ServiceStatus
            
            Write-Host "`nServices started. Press Ctrl+C to stop all services." -ForegroundColor Green
            Write-Host "Use '.\services-manager.ps1 -Action status' to check service status." -ForegroundColor Gray
            Write-Host "Use '.\services-manager.ps1 -Action logs' to view logs." -ForegroundColor Gray
            
            # Start monitoring in background
            Start-ServiceMonitoring
        }
        'stop' {
            $servicesToStop = if ($Services -contains 'all') { $ServiceConfig.Keys } else { $Services }
            
            foreach ($serviceName in $servicesToStop) {
                if ($ServiceConfig.ContainsKey($serviceName)) {
                    Stop-ServiceProcess $serviceName
                } else {
                    Write-ServiceLog "Unknown service: $serviceName" -Level 'ERROR'
                }
            }
            
            Save-ServicePIDs
            Show-ServiceStatus
        }
        'restart' {
            $servicesToRestart = if ($Services -contains 'all') { $ServiceConfig.Keys } else { $Services }
            
            foreach ($serviceName in $servicesToRestart) {
                if ($ServiceConfig.ContainsKey($serviceName)) {
                    Stop-ServiceProcess $serviceName
                    Start-Sleep -Seconds 2
                    Start-ServiceProcess $serviceName
                } else {
                    Write-ServiceLog "Unknown service: $serviceName" -Level 'ERROR'
                }
            }
            
            Save-ServicePIDs
            Show-ServiceStatus
        }
        'status' {
            Show-ServiceStatus
        }
        'logs' {
            $serviceToShow = if ($Services -contains 'all') { 'all' } else { $Services[0] }
            Show-ServiceLogs $serviceToShow
        }
    }
}

# Execute main function
try {
    Invoke-Main
} catch {
    Write-ServiceLog "Script execution failed: $_" -Level 'ERROR'
    Clear-AllServices
    exit 1
}
