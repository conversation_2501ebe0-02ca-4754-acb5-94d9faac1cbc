# Users API

The Users API provides endpoints for managing user profiles, retrieving user information, and handling mentor applications.

## Endpoints

### Get All Users

Retrieves a list of all users. This endpoint is restricted to administrators.

```
GET /api/users
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Query Parameters

| Parameter | Type    | Required | Description                                      |
|-----------|---------|----------|--------------------------------------------------|
| page      | integer | No       | Page number (default: 1)                         |
| limit     | integer | No       | Number of items per page (default: 10)           |
| role      | string  | No       | Filter by role (user, mentor, admin)             |
| isMentor  | boolean | No       | Filter by mentor status (true, false)            |
| isActive  | boolean | No       | Filter by active status (true, false)            |
| search    | string  | No       | Search by Discord username                       |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "discordId": "123456789012345678",
      "discordUsername": "username",
      "discordAvatar": "avatar_hash",
      "email": "<EMAIL>",
      "isMentor": false,
      "role": "user",
      "proficiencies": [
        {
          "name": "Streaming Setup",
          "category": "Account Setup",
          "isSelected": true
        }
      ],
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
    // More users...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

#### Status Codes

| Status Code | Description                                                  |
|-------------|--------------------------------------------------------------|
| 200         | Success                                                      |
| 401         | Unauthorized - Invalid or missing token                      |
| 403         | Forbidden - User does not have admin privileges              |
| 500         | Server Error - Failed to process the request                 |

#### Example Request

```bash
curl -X GET "http://localhost:3000/api/users?page=1&limit=10&role=mentor" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Mentors

Retrieves a list of all mentors.

```
GET /api/users/mentors
```

#### Query Parameters

| Parameter | Type    | Required | Description                                      |
|-----------|---------|----------|--------------------------------------------------|
| page      | integer | No       | Page number (default: 1)                         |
| limit     | integer | No       | Number of items per page (default: 10)           |
| search    | string  | No       | Search by Discord username                       |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "discordId": "123456789012345678",
      "discordUsername": "username",
      "discordAvatar": "avatar_hash",
      "isMentor": true,
      "proficiencies": [
        {
          "name": "Streaming Setup",
          "category": "Account Setup",
          "isSelected": true
        }
      ],
      "bio": "Experienced Twitch streamer with 5+ years of experience",
      "socialLinks": {
        "twitch": "https://twitch.tv/username",
        "twitter": "https://twitter.com/username"
      }
    }
    // More mentors...
  ],
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "pages": 5
  }
}
```

### Get User by ID

Retrieves a specific user by their ID. Users can only access their own profile unless they are administrators.

```
GET /api/users/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "discordId": "123456789012345678",
    "discordUsername": "username",
    "discordAvatar": "avatar_hash",
    "email": "<EMAIL>",
    "isMentor": false,
    "role": "user",
    "proficiencies": [
      {
        "name": "Streaming Setup",
        "category": "Account Setup",
        "isSelected": true
      }
    ],
    "bio": "Aspiring Twitch streamer",
    "socialLinks": {
      "twitch": "https://twitch.tv/username"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Update User Profile

Updates a user's profile. Users can only update their own profile unless they are administrators.

```
PUT /api/users/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "bio": "Updated bio information",
  "proficiencies": [
    {
      "name": "Streaming Setup",
      "category": "Account Setup",
      "isSelected": true
    },
    {
      "name": "Nightbot Configuration",
      "category": "Bots",
      "isSelected": true
    }
  ],
  "socialLinks": {
    "twitch": "https://twitch.tv/updated_username",
    "twitter": "https://twitter.com/updated_username",
    "youtube": "https://youtube.com/updated_username",
    "instagram": "https://instagram.com/updated_username"
  }
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "discordId": "123456789012345678",
    "discordUsername": "username",
    "discordAvatar": "avatar_hash",
    "email": "<EMAIL>",
    "isMentor": false,
    "role": "user",
    "proficiencies": [
      {
        "name": "Streaming Setup",
        "category": "Account Setup",
        "isSelected": true
      },
      {
        "name": "Nightbot Configuration",
        "category": "Bots",
        "isSelected": true
      }
    ],
    "bio": "Updated bio information",
    "socialLinks": {
      "twitch": "https://twitch.tv/updated_username",
      "twitter": "https://twitter.com/updated_username",
      "youtube": "https://youtube.com/updated_username",
      "instagram": "https://instagram.com/updated_username"
    },
    "updatedAt": "2023-01-02T00:00:00.000Z"
  },
  "message": "Profile updated successfully"
}
```

### Apply for Mentor Status

Submits an application for mentor status.

```
POST /api/users/mentor/apply
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "mentorApplication": {
      "status": "pending",
      "submittedAt": "2023-01-02T00:00:00.000Z"
    }
  },
  "message": "Mentor application submitted successfully"
}
```

### Review Mentor Application

Reviews a mentor application. This endpoint is restricted to administrators.

```
PUT /api/users/:id/mentor/review
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "status": "approved" // or "rejected"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "isMentor": true,
    "role": "mentor",
    "mentorApplication": {
      "status": "approved",
      "submittedAt": "2023-01-02T00:00:00.000Z",
      "reviewedAt": "2023-01-03T00:00:00.000Z",
      "reviewedBy": "60d21b4667d0d8992e610c86"
    }
  },
  "message": "Mentor application approved"
}
```
