const multer = require('multer');
const uploadService = require('../services/uploadService');
const logger = require('../config/logger');

// Configure multer storage
const storage = multer.memoryStorage();

// Configure multer upload
const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760 // 10MB default
  },
  fileFilter: (req, file, cb) => {
    // Accept images and videos only
    if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image and video files are allowed'));
    }
  }
});

/**
 * Upload file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const uploadFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }
    
    const fileUrl = await uploadService.saveFile(req.file, req.user._id.toString());
    
    return res.status(201).json({
      success: true,
      data: {
        url: fileUrl,
        filename: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      },
      message: 'File uploaded successfully'
    });
  } catch (error) {
    logger.error(`Upload file error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to upload file',
      error: error.message
    });
  }
};

/**
 * Delete file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteFile = async (req, res) => {
  try {
    const { fileUrl } = req.body;
    
    if (!fileUrl) {
      return res.status(400).json({
        success: false,
        message: 'File URL is required'
      });
    }
    
    // Check if file belongs to user
    if (!fileUrl.includes(`/uploads/${req.user._id.toString()}/`)) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this file'
      });
    }
    
    await uploadService.deleteFile(fileUrl);
    
    return res.status(200).json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    logger.error(`Delete file error: ${error.message}`);
    
    if (error.message === 'File not found') {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to delete file',
      error: error.message
    });
  }
};

module.exports = {
  upload,
  uploadFile,
  deleteFile
};
