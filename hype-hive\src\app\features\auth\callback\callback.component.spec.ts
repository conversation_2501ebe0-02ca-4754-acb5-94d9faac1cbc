import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';
import { CallbackComponent } from './callback.component';

describe('CallbackComponent', () => {
  let component: CallbackComponent;
  let fixture: ComponentFixture<CallbackComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let routerSpy: jasmine.SpyObj<Router>;
  let activatedRouteSpy: jasmine.SpyObj<ActivatedRoute>;

  const mockUser = {
    id: '1',
    discordId: '123456789',
    username: 'testuser',
    email: '<EMAIL>',
    displayName: 'Test User',
    avatar: 'avatar_url',
    isMentor: false,
    proficiencies: {
      streaming: 'beginner',
      contentCreation: 'intermediate',
      communityBuilding: 'advanced'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(async () => {
    const authSpy = jasmine.createSpyObj('AuthService', ['handleDiscordCallback']);
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);
    const activatedRouteSpyObj = jasmine.createSpyObj('ActivatedRoute', [], {
      queryParams: of({ code: 'test_code' })
    });

    await TestBed.configureTestingModule({
      imports: [CallbackComponent],
      providers: [
        { provide: AuthService, useValue: authSpy },
        { provide: Router, useValue: routerSpyObj },
        { provide: ActivatedRoute, useValue: activatedRouteSpyObj }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CallbackComponent);
    component = fixture.componentInstance;
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    activatedRouteSpy = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle successful Discord callback', () => {
    const mockResponse = {
      token: 'jwt_token',
      user: mockUser
    };

    authServiceSpy.handleDiscordCallback.and.returnValue(of(mockResponse));

    component.ngOnInit();

    expect(authServiceSpy.handleDiscordCallback).toHaveBeenCalledWith('test_code');
    expect(component.loading).toBe(false);
    expect(component.error).toBeNull();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle Discord callback error', () => {
    const errorResponse = { error: 'Invalid authorization code' };
    authServiceSpy.handleDiscordCallback.and.returnValue(throwError(() => errorResponse));

    component.ngOnInit();

    expect(authServiceSpy.handleDiscordCallback).toHaveBeenCalledWith('test_code');
    expect(component.loading).toBe(false);
    expect(component.error).toBe('Authentication failed. Please try again.');
    expect(routerSpy.navigate).not.toHaveBeenCalled();
  });

  it('should handle missing authorization code', () => {
    // Mock route with no code parameter
    activatedRouteSpy.queryParams = of({});

    component.ngOnInit();

    expect(authServiceSpy.handleDiscordCallback).not.toHaveBeenCalled();
    expect(component.loading).toBe(false);
    expect(component.error).toBe('No authorization code received.');
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
  });

  it('should show loading state initially', () => {
    expect(component.loading).toBe(true);
    expect(component.error).toBeNull();
  });

  it('should display loading message when loading', () => {
    component.loading = true;
    fixture.detectChanges();

    const loadingElement = fixture.nativeElement.querySelector('[data-testid="loading-message"]');
    expect(loadingElement).toBeTruthy();
    expect(loadingElement.textContent).toContain('Authenticating...');
  });

  it('should display error message when there is an error', () => {
    component.loading = false;
    component.error = 'Test error message';
    fixture.detectChanges();

    const errorElement = fixture.nativeElement.querySelector('[data-testid="error-message"]');
    expect(errorElement).toBeTruthy();
    expect(errorElement.textContent).toContain('Test error message');
  });

  it('should provide retry functionality on error', () => {
    spyOn(component, 'retryAuthentication');
    component.loading = false;
    component.error = 'Test error';
    fixture.detectChanges();

    const retryButton = fixture.nativeElement.querySelector('[data-testid="retry-button"]');
    expect(retryButton).toBeTruthy();

    retryButton.click();
    expect(component.retryAuthentication).toHaveBeenCalled();
  });

  it('should navigate to login on retry', () => {
    component.retryAuthentication();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
  });
});
