const request = require('supertest');
const express = require('express');
const adminRoutes = require('./adminRoutes');
const { isSystemAdmin } = require('../middleware/security');

// Mocks
jest.mock('../middleware/auth', () => ({
    protect: (req, res, next) => {
        req.user = req.mockUser;
        next();
    },
    authorize: () => (req, res, next) => next()
}));
npm test
jest.mock('../middleware/security', () => ({
    isSystemAdmin: jest.fn()
}));
jest.mock('../config/logger', () => ({
    info: jest.fn()
}));


describe('Admin Routes - /system-test', () => {
    let app;

    beforeEach(() => {
        app = express();
        app.use(express.json());
        app.use('/admin', adminRoutes);
    });

    it('should allow access for system admins', async () => {
        isSystemAdmin.mockReturnValue(true);
        const mockUser = {
            discordId: 'sysadmin123',
            discordUsername: 'SysAdmin',
            role: 'admin'
        };

        const res = await request(app)
            .get('/admin/system-test')
            .set('Accept', 'application/json')
            .set('Authorization', 'Bearer token') // Simulate auth header
            .use((req) => { req.mockUser = mockUser; });

        expect(res.statusCode).toBe(200);
        expect(res.body.success).toBe(true);
        expect(res.body.message).toMatch(/System admin privileges confirmed/);
        expect(res.body.data.adminUser.discordId).toBe('sysadmin123');
    });

    it('should deny access for non-system admins', async () => {
        isSystemAdmin.mockReturnValue(false);
        const mockUser = {
            discordId: 'user456',
            discordUsername: 'RegularUser',
            role: 'admin'
        };

        const res = await request(app)
            .get('/admin/system-test')
            .set('Accept', 'application/json')
            .set('Authorization', 'Bearer token')
            .use((req) => { req.mockUser = mockUser; });

        expect(res.statusCode).toBe(403);
        expect(res.body.success).toBe(false);
        expect(res.body.message).toMatch(/restricted to system administrators/i);
    });
});