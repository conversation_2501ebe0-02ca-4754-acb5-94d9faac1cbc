# Server-Side OAuth Flow

This document describes the simplified server-side OAuth flow implemented for Discord authentication.

## Overview

The OAuth flow has been simplified to handle the entire authentication process on the server side, eliminating the need for client-side code exchange and reducing security risks.

## Flow Diagram

```
1. User clicks "Login with Discord" → Frontend redirects to Discord OAuth
2. Discord redirects to server callback → Server exchanges code for tokens
3. Server creates/updates user → Server generates JWT
4. Server sets HTTP-only cookie → Server redirects user back to frontend
5. Frontend checks auth status → User is logged in
```

## Implementation Details

### Backend Changes

1. **New Server Callback Route**: `GET /api/auth/discord/callback`
   - Handles Discord OAuth callback directly
   - Exchanges authorization code for access token
   - Creates or updates user in database
   - Generates JWT token
   - Sets secure HTTP-only cookie
   - Redirects user back to frontend

2. **Updated Auth Middleware**: 
   - Now supports both Bearer token and cookie authentication
   - Checks `Authorization` header first, then falls back to `auth_token` cookie

3. **New Logout Endpoint**: `POST /api/auth/logout`
   - Clears the HTTP-only authentication cookie
   - Provides clean logout functionality

4. **CORS Configuration**:
   - Updated to include `credentials: true` for cookie support
   - Allows cookies to be sent with cross-origin requests

### Frontend Changes

1. **Updated OAuth URL**: 
   - Now points to server callback: `http://localhost:3000/api/auth/discord/callback`
   - Supports state parameter for return URL handling

2. **Simplified Login Flow**:
   - Removed client-side code exchange logic
   - Login component now handles auth success/error from query parameters
   - Automatically fetches user profile after successful authentication

3. **HTTP Interceptor Updates**:
   - All requests now include `withCredentials: true`
   - Supports both token-based and cookie-based authentication

4. **Auth Service Updates**:
   - `getUserProfile()` method supports both auth methods
   - `logout()` method calls server endpoint to clear cookies

## Configuration

### Environment Variables

Update your `.env` file:

```bash
# Discord OAuth Configuration
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback
FRONTEND_URL=http://localhost:4200
```

### Discord Application Settings

Update your Discord application's OAuth2 redirect URI to:
- Development: `http://localhost:3000/api/auth/discord/callback`
- Production: `https://your-domain.com/api/auth/discord/callback`

## Security Benefits

1. **HTTP-Only Cookies**: Prevents XSS attacks from accessing auth tokens
2. **Server-Side Token Exchange**: Client never handles sensitive OAuth tokens
3. **Secure Cookie Settings**: 
   - `httpOnly: true` - Not accessible via JavaScript
   - `secure: true` (production) - Only sent over HTTPS
   - `sameSite: 'strict'` (production) - CSRF protection

## Backward Compatibility

The system maintains backward compatibility:
- Legacy API endpoint `POST /api/auth/discord` still works
- Token-based authentication still supported
- Existing clients can continue using Bearer tokens

## Testing

1. Start the backend server: `npm run dev`
2. Start the frontend: `ng serve`
3. Navigate to `http://localhost:4200/auth/login`
4. Click "Login with Discord"
5. Complete OAuth flow on Discord
6. Should be redirected back and logged in automatically

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure `FRONTEND_URL` is set correctly in backend `.env`
2. **Cookie Not Set**: Check that `withCredentials: true` is set in HTTP requests
3. **Redirect URI Mismatch**: Verify Discord app settings match your callback URL
4. **Authentication Fails**: Check server logs for detailed error messages

### Debug Steps

1. Check browser Network tab for failed requests
2. Verify cookies are being set in Application tab
3. Check server logs for authentication errors
4. Ensure environment variables are loaded correctly
