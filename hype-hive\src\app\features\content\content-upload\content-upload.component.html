<div class="content-upload-container">
  <div class="header">
    <button mat-icon-button (click)="cancel()" aria-label="Back">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <h1>Create New Content</h1>
  </div>

  <form [formGroup]="contentForm" (ngSubmit)="onSubmit()" class="content-form">
    <div class="form-row">
      <!-- Basic Information -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>Basic Information</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <!-- Title -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Title</mat-label>
            <input matInput formControlName="title" placeholder="Enter content title">
            <mat-error *ngIf="contentForm.get('title')?.hasError('required')">
              Title is required
            </mat-error>
            <mat-error *ngIf="contentForm.get('title')?.hasError('minlength')">
              Title must be at least 3 characters
            </mat-error>
            <mat-error *ngIf="contentForm.get('title')?.hasError('maxlength')">
              Title cannot exceed 100 characters
            </mat-error>
          </mat-form-field>

          <!-- Description -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description</mat-label>
            <textarea 
              matInput 
              formControlName="description" 
              placeholder="Describe your content"
              rows="4">
            </textarea>
            <mat-error *ngIf="contentForm.get('description')?.hasError('required')">
              Description is required
            </mat-error>
            <mat-error *ngIf="contentForm.get('description')?.hasError('minlength')">
              Description must be at least 10 characters
            </mat-error>
            <mat-error *ngIf="contentForm.get('description')?.hasError('maxlength')">
              Description cannot exceed 1000 characters
            </mat-error>
          </mat-form-field>

          <!-- Category and Difficulty -->
          <div class="form-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Category</mat-label>
              <mat-select formControlName="category">
                <mat-option *ngFor="let category of categories" [value]="category">
                  {{ category }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="contentForm.get('category')?.hasError('required')">
                Category is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Difficulty</mat-label>
              <mat-select formControlName="difficulty">
                <mat-option *ngFor="let difficulty of difficulties" [value]="difficulty.value">
                  {{ difficulty.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Embed URL -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Embed URL (Optional)</mat-label>
            <input matInput formControlName="embedUrl" placeholder="YouTube embed URL or other video embed">
            <mat-hint>For YouTube videos, use the embed URL format</mat-hint>
          </mat-form-field>
        </mat-card-content>
      </mat-card>

      <!-- File Uploads -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>Media Files</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <!-- Video Upload -->
          <div class="file-upload-section">
            <h3>Video File</h3>
            <div class="file-upload-area" *ngIf="!selectedVideoFile">
              <input 
                type="file" 
                #videoFileInput 
                (change)="onVideoFileSelected($event)"
                accept="video/*"
                style="display: none">
              <button 
                mat-stroked-button 
                type="button" 
                (click)="videoFileInput.click()"
                class="upload-button">
                <mat-icon>video_library</mat-icon>
                Choose Video File
              </button>
              <p class="upload-hint">Supported formats: MP4, WebM, MOV (Max: 100MB)</p>
            </div>

            <!-- Video Preview -->
            <div class="file-preview" *ngIf="selectedVideoFile">
              <div class="preview-header">
                <span class="file-name">{{ selectedVideoFile.name }}</span>
                <button mat-icon-button (click)="removeVideoFile()" type="button">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
              <video *ngIf="videoPreviewUrl" [src]="videoPreviewUrl" controls class="video-preview">
                Your browser does not support the video tag.
              </video>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Thumbnail Upload -->
          <div class="file-upload-section">
            <h3>Thumbnail Image</h3>
            <div class="file-upload-area" *ngIf="!selectedThumbnailFile">
              <input 
                type="file" 
                #thumbnailFileInput 
                (change)="onThumbnailFileSelected($event)"
                accept="image/*"
                style="display: none">
              <button 
                mat-stroked-button 
                type="button" 
                (click)="thumbnailFileInput.click()"
                class="upload-button">
                <mat-icon>image</mat-icon>
                Choose Thumbnail
              </button>
              <p class="upload-hint">Supported formats: JPG, PNG, GIF, WebP (Max: 5MB)</p>
            </div>

            <!-- Thumbnail Preview -->
            <div class="file-preview" *ngIf="selectedThumbnailFile">
              <div class="preview-header">
                <span class="file-name">{{ selectedThumbnailFile.name }}</span>
                <button mat-icon-button (click)="removeThumbnailFile()" type="button">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
              <img *ngIf="thumbnailPreviewUrl" [src]="thumbnailPreviewUrl" alt="Thumbnail preview" class="thumbnail-preview">
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Tags and Publishing -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Tags & Publishing</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <!-- Tags -->
        <div class="tags-section">
          <h3>Tags</h3>
          <div class="tag-input-container">
            <mat-form-field appearance="outline" class="tag-input">
              <mat-label>Add tags</mat-label>
              <input 
                matInput 
                [(ngModel)]="tagInput" 
                (keypress)="onTagKeyPress($event)"
                placeholder="Type and press Enter"
                [ngModelOptions]="{standalone: true}">
            </mat-form-field>
            <button 
              mat-button 
              type="button" 
              (click)="addTag()"
              [disabled]="!tagInput.trim()">
              Add
            </button>
          </div>
          
          <div class="tags-list" *ngIf="tags.length > 0">
            <mat-chip-set>
              <mat-chip *ngFor="let tag of tags" (removed)="removeTag(tag)">
                {{ tag }}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip>
            </mat-chip-set>
          </div>
          <p class="tags-hint">Add up to 10 tags to help users find your content</p>
        </div>

        <!-- Publishing Options -->
        <div class="publishing-section">
          <mat-slide-toggle formControlName="isPublished">
            Publish immediately
          </mat-slide-toggle>
          <p class="publishing-hint">
            If unchecked, content will be saved as draft and can be published later
          </p>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Upload Progress -->
    <mat-card class="progress-section" *ngIf="isUploading">
      <mat-card-content>
        <div class="upload-progress">
          <h3>Uploading...</h3>
          <mat-progress-bar mode="determinate" [value]="uploadProgress"></mat-progress-bar>
          <p>{{ uploadProgress }}% complete</p>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Form Actions -->
    <div class="form-actions">
      <button 
        mat-button 
        type="button" 
        (click)="cancel()"
        [disabled]="isLoading">
        Cancel
      </button>
      <button 
        mat-raised-button 
        color="primary" 
        type="submit"
        [disabled]="isLoading || contentForm.invalid">
        <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
        <span *ngIf="!isLoading">Create Content</span>
        <span *ngIf="isLoading">Creating...</span>
      </button>
    </div>
  </form>
</div>
