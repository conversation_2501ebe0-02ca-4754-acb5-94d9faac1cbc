const logger = require('../config/logger');

/**
 * Rate limiting statistics and management
 */
class RateLimitManager {
  constructor() {
    this.rateLimitHits = new Map(); // Track rate limit hits per IP
    this.suspiciousIPs = new Map(); // Track potentially malicious IPs
  }

  /**
   * Record a rate limit hit for an IP
   * @param {string} ip - The IP address
   * @param {string} endpoint - The endpoint that was rate limited
   */
  recordRateLimitHit(ip, endpoint = 'unknown') {
    const key = `${ip}:${endpoint}`;
    const current = this.rateLimitHits.get(key) || { count: 0, lastHit: new Date() };
    
    current.count++;
    current.lastHit = new Date();
    
    this.rateLimitHits.set(key, current);

    // Mark as suspicious if hitting multiple endpoints or high frequency
    const ipHits = Array.from(this.rateLimitHits.entries())
      .filter(([k]) => k.startsWith(`${ip}:`))
      .reduce((sum, [, data]) => sum + data.count, 0);

    if (ipHits > 50) { // Threshold for suspicious activity
      this.markSuspicious(ip, `High rate limit hits: ${ipHits}`);
    }

    logger.warn(`Rate limit hit recorded for IP: ${ip} on endpoint: ${endpoint}`, {
      ip,
      endpoint,
      hitCount: current.count,
      totalIPHits: ipHits
    });
  }

  /**
   * Mark an IP as suspicious
   * @param {string} ip - The IP address
   * @param {string} reason - Reason for marking as suspicious
   */
  markSuspicious(ip, reason) {
    const suspiciousData = this.suspiciousIPs.get(ip) || {
      firstSeen: new Date(),
      reasons: [],
      hitCount: 0
    };

    suspiciousData.reasons.push({ reason, timestamp: new Date() });
    suspiciousData.hitCount++;
    suspiciousData.lastSeen = new Date();

    this.suspiciousIPs.set(ip, suspiciousData);

    logger.error(`IP marked as suspicious: ${ip}`, {
      ip,
      reason,
      totalReasons: suspiciousData.reasons.length,
      hitCount: suspiciousData.hitCount
    });
  }

  /**
   * Get rate limiting statistics
   * @returns {Object} - Statistics object
   */
  getStatistics() {
    const now = new Date();
    const oneHour = 60 * 60 * 1000;

    // Get recent hits (last hour)
    const recentHits = Array.from(this.rateLimitHits.entries())
      .filter(([, data]) => (now - data.lastHit) < oneHour)
      .sort(([, a], [, b]) => b.count - a.count);

    return {
      totalRateLimitHits: this.rateLimitHits.size,
      recentHits: recentHits.slice(0, 20), // Top 20 recent hits
      suspiciousIPs: Array.from(this.suspiciousIPs.entries())
        .map(([ip, data]) => ({ ip, ...data }))
        .sort((a, b) => b.hitCount - a.hitCount),
      summary: {
        totalSuspiciousIPs: this.suspiciousIPs.size,
        recentHitsCount: recentHits.length,
        timestamp: now
      }
    };
  }

  /**
   * Clean old entries (older than 24 hours)
   */
  cleanup() {
    const now = new Date();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    // Clean rate limit hits
    for (const [key, data] of this.rateLimitHits.entries()) {
      if (data.lastHit < oneDayAgo) {
        this.rateLimitHits.delete(key);
      }
    }

    // Clean suspicious IPs (keep for longer - 7 days)
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.lastSeen < oneWeekAgo) {
        this.suspiciousIPs.delete(ip);
      }
    }

    logger.info('Rate limit data cleanup completed', {
      remainingHits: this.rateLimitHits.size,
      remainingSuspicious: this.suspiciousIPs.size
    });
  }
}

// Create singleton instance
const rateLimitManager = new RateLimitManager();

// Cleanup old entries every hour
setInterval(() => {
  rateLimitManager.cleanup();
}, 60 * 60 * 1000);

module.exports = rateLimitManager;
