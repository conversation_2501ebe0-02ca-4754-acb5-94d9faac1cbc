const request = require('supertest');
const express = require('express');
const { AppError, globalErrorHandler, catchAsync, notFound } = require('../../src/middleware/errorHandler');

describe('Error Handler Middleware', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Test routes
    app.get('/test-app-error', (req, res, next) => {
      next(new AppError('Test error message', 400));
    });
    
    app.get('/test-catch-async', catchAsync(async (req, res, next) => {
      throw new Error('Async error');
    }));
    
    app.get('/test-success', (req, res) => {
      res.status(200).json({ success: true, message: 'Success' });
    });
    
    // 404 handler
    app.use(notFound);
    
    // Global error handler
    app.use(globalErrorHandler);
  });

  describe('AppError', () => {
    it('should create an operational error with correct properties', () => {
      const error = new AppError('Test message', 400);
      
      expect(error.message).toBe('Test message');
      expect(error.statusCode).toBe(400);
      expect(error.status).toBe('fail');
      expect(error.isOperational).toBe(true);
    });

    it('should create a server error with correct status', () => {
      const error = new AppError('Server error', 500);
      
      expect(error.status).toBe('error');
    });
  });

  describe('Global Error Handler', () => {
    it('should handle AppError correctly', async () => {
      const response = await request(app)
        .get('/test-app-error')
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        message: 'Test error message'
      });
    });

    it('should handle async errors with catchAsync', async () => {
      const response = await request(app)
        .get('/test-catch-async')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBeDefined();
    });

    it('should handle 404 errors', async () => {
      const response = await request(app)
        .get('/non-existent-route')
        .expect(404);

      expect(response.body).toEqual({
        success: false,
        message: "Can't find /non-existent-route on this server!"
      });
    });

    it('should allow successful requests to pass through', async () => {
      const response = await request(app)
        .get('/test-success')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Success'
      });
    });
  });

  describe('catchAsync', () => {
    it('should catch async errors and pass to error handler', async () => {
      const asyncFunction = catchAsync(async (req, res, next) => {
        throw new Error('Async test error');
      });

      const mockReq = {};
      const mockRes = {};
      const mockNext = jest.fn();

      await asyncFunction(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toBe('Async test error');
    });
  });
});
