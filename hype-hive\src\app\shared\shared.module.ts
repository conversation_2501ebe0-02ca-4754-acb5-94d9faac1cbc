import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { NotificationBadgeComponent } from './notifications/notification-badge/notification-badge.component';
import { NotificationListComponent } from './notifications/notification-list/notification-list.component';

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    NotificationBadgeComponent,
    NotificationListComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    MatBadgeModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    MatProgressSpinnerModule
  ],
  exports: [
    HeaderComponent,
    FooterComponent,
    NotificationBadgeComponent,
    NotificationListComponent
  ]
})
export class SharedModule { }
