const chatService = require('../services/chatService');
const logger = require('../config/logger');

/**
 * Get all chats for the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserChats = async (req, res) => {
  try {
    const result = await chatService.getUserChats(req.user._id, req.query);
    
    return res.status(200).json({
      success: true,
      data: result.chats,
      pagination: result.pagination
    });
  } catch (error) {
    logger.error(`Get user chats error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get chats',
      error: error.message
    });
  }
};

/**
 * Get chat by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getChatById = async (req, res) => {
  try {
    const chat = await chatService.getChatById(req.params.id, req.user._id);
    
    return res.status(200).json({
      success: true,
      data: chat
    });
  } catch (error) {
    logger.error(`Get chat by ID error: ${error.message}`);
    
    if (error.message === 'Chat not found or you do not have access') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to get chat',
      error: error.message
    });
  }
};

/**
 * Create a new chat
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createChat = async (req, res) => {
  try {
    const { participants, isGroupChat, groupName } = req.body;
    
    if (!participants || !Array.isArray(participants) || participants.length < 1) {
      return res.status(400).json({
        success: false,
        message: 'At least one participant is required'
      });
    }
    
    const chatData = {
      isGroupChat: isGroupChat || false,
      groupName: groupName
    };
    
    const chat = await chatService.createChat(participants, req.user._id, chatData);
    
    return res.status(201).json({
      success: true,
      data: chat,
      message: 'Chat created successfully'
    });
  } catch (error) {
    logger.error(`Create chat error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to create chat',
      error: error.message
    });
  }
};

/**
 * Add message to chat
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addMessage = async (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Message content is required'
      });
    }
    
    const result = await chatService.addMessage(req.params.id, req.user._id, content);
    
    return res.status(201).json({
      success: true,
      data: result.newMessage,
      message: 'Message sent successfully'
    });
  } catch (error) {
    logger.error(`Add message error: ${error.message}`);
    
    if (error.message === 'Chat not found or you are not a participant') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to send message',
      error: error.message
    });
  }
};

/**
 * Mark messages as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const markMessagesAsRead = async (req, res) => {
  try {
    await chatService.markMessagesAsRead(req.params.id, req.user._id);
    
    return res.status(200).json({
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    logger.error(`Mark messages as read error: ${error.message}`);
    
    if (error.message === 'Chat not found or you are not a participant') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
};

module.exports = {
  getUserChats,
  getChatById,
  createChat,
  addMessage,
  markMessagesAsRead
};
