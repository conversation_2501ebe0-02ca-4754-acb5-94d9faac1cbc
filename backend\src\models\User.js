const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const ProficiencySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Account Setup',
      'Bots',
      'Networking',
      'Emotes',
      'Streaming Platforms',
      'Affiliate Roles',
      'Console Help',
      'PC Help'
    ]
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const UserSchema = new mongoose.Schema({
  discordId: {
    type: String,
    required: true,
    unique: true
  },
  discordUsername: {
    type: String,
    required: true
  },
  discordAvatar: {
    type: String
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  isMentor: {
    type: Boolean,
    default: false
  },
  proficiencies: [ProficiencySchema],
  bio: {
    type: String,
    trim: true
  },
  socialLinks: {
    twitch: String,
    twitter: String,
    youtube: String,
    instagram: String
  },
  mentorApplication: {
    status: {
      type: String,
      enum: ['none', 'pending', 'approved', 'rejected'],
      default: 'none'
    },
    submittedAt: Date,
    reviewedAt: Date,
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  role: {
    type: String,
    enum: ['user', 'mentor', 'admin'],
    default: 'user'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Method to check if user is admin
UserSchema.methods.isAdmin = function() {
  return this.role === 'admin';
};

// Method to check if user is mentor
UserSchema.methods.isMentorUser = function() {
  return this.role === 'mentor' || this.role === 'admin';
};

// Add text index for search functionality
UserSchema.index({ discordUsername: 'text', bio: 'text' });

// Add compound indexes for common queries
UserSchema.index({ isMentor: 1, isActive: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ 'mentorApplication.status': 1 });

const User = mongoose.model('User', UserSchema);

module.exports = User;
