.content-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }
  }

  .filters-card {
    margin-bottom: 24px;

    .filters {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr auto;
      gap: 16px;
      align-items: end;

      .filter-actions {
        display: flex;
        gap: 8px;

        button {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }

  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 16px 0;
      color: #666;
    }
  }

  .content-table-card {
    .content-table {
      width: 100%;

      .content-cell {
        display: flex;
        align-items: center;
        gap: 12px;

        .thumbnail {
          width: 60px;
          height: 40px;
          border-radius: 4px;
          object-fit: cover;
        }

        .content-info {
          flex: 1;

          .title {
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.2;
          }

          .description {
            font-size: 0.875rem;
            color: #666;
            line-height: 1.3;
          }
        }
      }

      .creator-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .stats-cell {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .stat {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 0.875rem;

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
            color: #666;
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 4px;
      }

      mat-chip {
        font-size: 0.75rem;
        min-height: 24px;
      }

      mat-slide-toggle {
        .mdc-form-field {
          font-size: 0.875rem;
        }
      }

      th {
        font-weight: 600;
        color: #333;
      }

      td {
        padding: 12px 8px;
      }
    }

    mat-paginator {
      border-top: 1px solid #e0e0e0;
      margin-top: 16px;
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .content-management {
    .filters-card .filters {
      grid-template-columns: 1fr;
      gap: 12px;

      .filter-actions {
        justify-content: center;
      }
    }

    .content-table-card .content-table {
      font-size: 0.875rem;

      .content-cell {
        gap: 8px;

        .thumbnail {
          width: 48px;
          height: 32px;
        }
      }

      .creator-cell .avatar {
        width: 24px;
        height: 24px;
      }
    }
  }
}

@media (max-width: 768px) {
  .content-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      button {
        align-self: flex-end;
      }
    }

    .content-table-card {
      .content-table {
        .mat-column-creator,
        .mat-column-stats {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .content-management {
    .content-table-card {
      .content-table {
        .mat-column-category,
        .mat-column-difficulty {
          display: none;
        }

        .content-cell {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }

        .action-buttons {
          flex-direction: column;
        }
      }
    }
  }
}