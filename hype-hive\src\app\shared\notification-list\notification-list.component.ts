import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-notification-list',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatDividerModule],
  template: `
    <div class="notification-list">
      <div class="notification-header">
        <h3>Notifications</h3>
      </div>
      
      <div class="notification-items" *ngIf="notifications.length > 0; else noNotifications">
        <div class="notification-item" *ngFor="let notification of notifications">
          <div class="notification-icon">
            <mat-icon [class]="'icon-' + notification.type">{{ getNotificationIcon(notification.type) }}</mat-icon>
          </div>
          <div class="notification-content">
            <p class="notification-message">{{ notification.message }}</p>
            <span class="notification-time">{{ notification.time }}</span>
          </div>
        </div>
      </div>
      
      <ng-template #noNotifications>
        <div class="no-notifications">
          <mat-icon>notifications_none</mat-icon>
          <p>No new notifications</p>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .notification-list {
      width: 300px;
      max-height: 400px;
      background: var(--bg-primary);
      border-radius: 12px;
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--border-light);
      overflow: hidden;
    }

    .notification-header {
      padding: 1rem;
      background: var(--gradient-blue-purple);
      color: var(--text-inverse);
    }

    .notification-header h3 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .notification-items {
      max-height: 300px;
      overflow-y: auto;
    }

    .notification-item {
      display: flex;
      padding: 1rem;
      border-bottom: 1px solid var(--border-light);
      transition: background-color 0.2s ease;
    }

    .notification-item:hover {
      background: var(--bg-secondary);
    }

    .notification-item:last-child {
      border-bottom: none;
    }

    .notification-icon {
      margin-right: 0.75rem;
      flex-shrink: 0;
    }

    .notification-icon mat-icon {
      width: 24px;
      height: 24px;
      font-size: 24px;
    }

    .icon-message {
      color: var(--primary-blue);
    }

    .icon-mention {
      color: var(--orange);
    }

    .icon-system {
      color: var(--purple);
    }

    .notification-content {
      flex: 1;
    }

    .notification-message {
      margin: 0 0 0.25rem 0;
      font-size: 0.9rem;
      color: var(--text-primary);
      line-height: 1.4;
    }

    .notification-time {
      font-size: 0.75rem;
      color: var(--text-muted);
    }

    .no-notifications {
      padding: 2rem;
      text-align: center;
      color: var(--text-muted);
    }

    .no-notifications mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 0.5rem;
      opacity: 0.5;
    }

    .no-notifications p {
      margin: 0;
      font-size: 0.9rem;
    }
  `]
})
export class NotificationListComponent {
  notifications = [
    // Mock data - this would come from a service
    {
      id: 1,
      type: 'message',
      message: 'You have a new message from John Doe',
      time: '2 minutes ago'
    },
    {
      id: 2,
      type: 'mention',
      message: 'You were mentioned in #general',
      time: '1 hour ago'
    }
  ];

  getNotificationIcon(type: string): string {
    switch (type) {
      case 'message':
        return 'message';
      case 'mention':
        return 'alternate_email';
      case 'system':
        return 'info';
      default:
        return 'notifications';
    }
  }
}
