const Chat = require('../models/Chat');
const User = require('../models/User');
const mongoose = require('mongoose');
const logger = require('../config/logger');

/**
 * Get all chats for a user
 * @param {string} userId - User ID
 * @param {Object} query - Query parameters
 * @returns {Promise<Array>} - Array of chats
 */
const getUserChats = async (userId, query = {}) => {
  try {
    // Pagination
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 20;
    const skip = (page - 1) * limit;

    // Use aggregation for better performance
    const aggregationPipeline = [
      {
        $match: {
          participants: { $in: [new mongoose.Types.ObjectId(userId)] }
        }
      },
      { $sort: { updatedAt: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'users',
          localField: 'participants',
          foreignField: '_id',
          as: 'participantInfo'
        }
      },
      {
        $project: {
          _id: 1,
          participants: 1,
          lastMessage: 1,
          isGroupChat: 1,
          groupName: 1,
          groupAdmin: 1,
          createdAt: 1,
          updatedAt: 1,
          'participantInfo._id': 1,
          'participantInfo.discordUsername': 1,
          'participantInfo.discordAvatar': 1,
          'participantInfo.isMentor': 1
        }
      }
    ];

    const chats = await Chat.aggregate(aggregationPipeline);

    // Get total count
    const total = await Chat.countDocuments({
      participants: { $in: [new mongoose.Types.ObjectId(userId)] }
    });

    return {
      chats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting user chats: ${error.message}`);
    throw new Error('Failed to get chats');
  }
};

/**
 * Get chat by ID
 * @param {string} chatId - Chat ID
 * @param {string} userId - User ID (for authorization)
 * @returns {Promise<Object>} - Chat document
 */
const getChatById = async (chatId, userId) => {
  try {
    const chat = await Chat.findOne({
      _id: chatId,
      participants: { $in: [userId] }
    })
    .populate('participants', 'discordUsername discordAvatar isMentor')
    .populate('messages.sender', 'discordUsername discordAvatar')
    .populate('lastMessage.sender', 'discordUsername discordAvatar')
    .populate('groupAdmin', 'discordUsername discordAvatar');

    if (!chat) {
      throw new Error('Chat not found or you do not have access');
    }

    return chat;
  } catch (error) {
    logger.error(`Error getting chat by ID: ${error.message}`);
    throw new Error(error.message || 'Failed to get chat');
  }
};

/**
 * Create a new chat
 * @param {Array} participantIds - Array of participant user IDs
 * @param {string} creatorId - Creator user ID
 * @param {Object} chatData - Chat data (for group chats)
 * @returns {Promise<Object>} - Created chat document
 */
const createChat = async (participantIds, creatorId, chatData = {}) => {
  try {
    // Validate participants
    if (!participantIds || participantIds.length < 1) {
      throw new Error('At least one participant is required');
    }

    // Ensure creator is included in participants
    if (!participantIds.includes(creatorId)) {
      participantIds.push(creatorId);
    }

    // Check if it's a group chat
    const isGroupChat = participantIds.length > 2 || chatData.isGroupChat;

    // For direct messages, check if a chat already exists
    if (!isGroupChat) {
      const existingChat = await Chat.findOne({
        isGroupChat: false,
        participants: { $all: participantIds, $size: 2 }
      });

      if (existingChat) {
        return existingChat;
      }
    }

    // Create new chat
    const chatObj = {
      participants: participantIds,
      isGroupChat: isGroupChat,
      groupAdmin: isGroupChat ? creatorId : undefined,
      groupName: isGroupChat ? chatData.groupName : undefined
    };

    const chat = new Chat(chatObj);
    await chat.save();

    // Populate participant info
    await chat.populate('participants', 'discordUsername discordAvatar isMentor');

    return chat;
  } catch (error) {
    logger.error(`Error creating chat: ${error.message}`);
    throw new Error(error.message || 'Failed to create chat');
  }
};

/**
 * Add message to chat
 * @param {string} chatId - Chat ID
 * @param {string} senderId - Sender user ID
 * @param {string} content - Message content
 * @returns {Promise<Object>} - Updated chat document with new message
 */
const addMessage = async (chatId, senderId, content) => {
  try {
    // Check if chat exists and user is a participant
    const chat = await Chat.findOne({
      _id: chatId,
      participants: { $in: [senderId] }
    });

    if (!chat) {
      throw new Error('Chat not found or you are not a participant');
    }

    // Create new message
    const newMessage = {
      sender: senderId,
      content,
      readBy: [senderId],
      createdAt: new Date()
    };

    // Update chat with new message and last message info
    const updatedChat = await Chat.findByIdAndUpdate(
      chatId,
      {
        $push: { messages: newMessage },
        $set: {
          lastMessage: {
            content,
            sender: senderId,
            createdAt: new Date()
          },
          updatedAt: new Date()
        }
      },
      { new: true }
    )
    .populate('messages.sender', 'discordUsername discordAvatar')
    .populate('lastMessage.sender', 'discordUsername discordAvatar');

    return {
      chat: updatedChat,
      newMessage: updatedChat.messages[updatedChat.messages.length - 1]
    };
  } catch (error) {
    logger.error(`Error adding message: ${error.message}`);
    throw new Error(error.message || 'Failed to add message');
  }
};

/**
 * Mark messages as read
 * @param {string} chatId - Chat ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated chat document
 */
const markMessagesAsRead = async (chatId, userId) => {
  try {
    const chat = await Chat.findOneAndUpdate(
      {
        _id: chatId,
        participants: { $in: [userId] }
      },
      {
        $addToSet: { 'messages.$[].readBy': userId }
      },
      { new: true }
    );

    if (!chat) {
      throw new Error('Chat not found or you are not a participant');
    }

    return chat;
  } catch (error) {
    logger.error(`Error marking messages as read: ${error.message}`);
    throw new Error(error.message || 'Failed to mark messages as read');
  }
};

module.exports = {
  getUserChats,
  getChatById,
  createChat,
  addMessage,
  markMessagesAsRead
};
