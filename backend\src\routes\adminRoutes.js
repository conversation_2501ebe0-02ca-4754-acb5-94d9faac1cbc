const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { isSystemAdmin } = require('../middleware/security');
const User = require('../models/User');
const logger = require('../config/logger');
const fs = require('fs');
const path = require('path');
const validation = require('../middleware/validation');
const contentController = require('../controllers/contentController');
const adminController = require('../controllers/adminController');

const router = express.Router();

// List all content for admin
router.get(
  '/content',
  protect,
  authorize('admin'),
  catchAsync(contentController.getAllContent)
);

/**
 * Admin dashboard endpoint - shows system status
 * Only accessible by admins and system admins
 */
router.get('/dashboard', protect, authorize('admin'), catchAsync(async (req, res) => {
  const isSystemAdminUser = isSystemAdmin(req.user.discordId);
  
  // Get system statistics
  const userStats = await User.aggregate([
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const totalUsers = await User.countDocuments({ isActive: true });
  const totalMentors = await User.countDocuments({ isMentor: true, isActive: true });
  const pendingApplications = await User.countDocuments({ 
    'mentorApplication.status': 'pending' 
  });
  
  res.json({
    success: true,
    data: {
      adminInfo: {
        username: req.user.discordUsername,
        discordId: req.user.discordId,
        role: req.user.role,
        isSystemAdmin: isSystemAdminUser,
        lastLogin: req.user.lastLogin
      },
      systemStats: {
        totalUsers,
        totalMentors,
        pendingApplications,
        usersByRole: userStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {})
      },
      privileges: {
        rateLimitBypass: true,
        fullContentAccess: true,
        userManagement: true,
        systemMonitoring: true,
        mentorManagement: true
      }
    }
  });
}));

/**
 * System admin test endpoint - only accessible by system admins
 * Used to verify system admin privileges are working
 */
router.get('/system-test', protect, catchAsync(async (req, res) => {
  // Only system admins can access this endpoint
  if (!isSystemAdmin(req.user.discordId)) {
    return res.status(403).json({
      success: false,
      message: 'This endpoint is restricted to system administrators only'
    });
  }
  
  logger.info(`System admin test accessed by: ${req.user.discordUsername} (${req.user.discordId})`);
  
  res.json({
    success: true,
    message: '🎉 System admin privileges confirmed!',
    data: {
      adminUser: {
        username: req.user.discordUsername,
        discordId: req.user.discordId,
        role: req.user.role
      },
      systemAccess: {
        rateLimitBypass: true,
        adminPanelAccess: true,
        userManagement: true,
        contentModeration: true,
        systemConfiguration: true
      },
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * Promote user to admin - system admin only
 */
router.post('/promote/:discordId', protect, catchAsync(async (req, res) => {
  if (!isSystemAdmin(req.user.discordId)) {
    return res.status(403).json({
      success: false,
      message: 'Only system administrators can promote users'
    });
  }
  
  const { discordId } = req.params;
  const user = await User.findOneAndUpdate(
    { discordId },
    { 
      role: 'admin',
      isMentor: true,
      'mentorApplication.status': 'approved',
      'mentorApplication.reviewedAt': new Date(),
      'mentorApplication.reviewedBy': req.user._id
    },
    { new: true }
  );
  
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }
  
  logger.info(`User promoted to admin by system admin`, {
    promotedUser: { discordId: user.discordId, username: user.discordUsername },
    systemAdmin: { discordId: req.user.discordId, username: req.user.discordUsername }
  });
  
  res.json({
    success: true,
    message: `User ${user.discordUsername} promoted to admin`,
    data: {
      user: {
        discordId: user.discordId,
        username: user.discordUsername,
        role: user.role,
        isMentor: user.isMentor
      }
    }
  });
}));

// Comment management for content (admin only)
router.put(
  '/content/:contentId/comment/:commentId',
  protect,
  authorize('admin'),
  validation.contentValidation.editComment,
  catchAsync(async (req, res) => contentController.editComment(req, res))
);

router.delete(
  '/content/:contentId/comment/:commentId',
  protect,
  authorize('admin'),
  validation.contentValidation.deleteComment,
  catchAsync(async (req, res) => contentController.deleteComment(req, res))
);

/**
 * Get Cloudflare tunnel logs
 */
router.get('/tunnel-logs', protect, authorize('admin'), catchAsync(async (req, res) => {
  // Construct path to root-level bot_startup.log (one level above backend directory)
  const logPath = path.resolve(__dirname, '..', '..', '..', 'bot_startup.log');
  const logs = await fs.promises.readFile(logPath, 'utf8');
  res.json({ success: true, data: logs });
}));

// Dashboard statistics for admin
router.get('/stats', protect, authorize('admin'), catchAsync(adminController.getStats));

// Pending mentor applications for admin
router.get('/mentor-applications', protect, authorize('admin'), catchAsync(adminController.getPendingMentorApplications));

// Update user role
router.put('/users/:id/role', protect, authorize('admin'), catchAsync(adminController.updateUserRole));


// Export router
module.exports = router;
