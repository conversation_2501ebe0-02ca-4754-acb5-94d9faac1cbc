import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { LoginComponent } from './login.component';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let routerSpy: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const authSpy = jasmine.createSpyObj('AuthService', ['redirectToDiscordAuth', 'isAuthenticated']);
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [LoginComponent],
      providers: [
        { provide: AuthService, useValue: authSpy },
        { provide: Router, useValue: routerSpyObj }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should redirect to Discord auth when login button is clicked', () => {
    component.loginWithDiscord();
    expect(authServiceSpy.redirectToDiscordAuth).toHaveBeenCalled();
  });

  it('should redirect authenticated users', () => {
    authServiceSpy.isAuthenticated.and.returnValue(true);

    component.ngOnInit();

    expect(routerSpy.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should not redirect unauthenticated users', () => {
    authServiceSpy.isAuthenticated.and.returnValue(false);

    component.ngOnInit();

    expect(routerSpy.navigate).not.toHaveBeenCalled();
  });

  it('should display login button for unauthenticated users', () => {
    authServiceSpy.isAuthenticated.and.returnValue(false);
    fixture.detectChanges();

    const loginButton = fixture.nativeElement.querySelector('[data-testid="discord-login-btn"]');
    expect(loginButton).toBeTruthy();
    expect(loginButton.textContent).toContain('Login with Discord');
  });

  it('should call loginWithDiscord when login button is clicked', () => {
    spyOn(component, 'loginWithDiscord');
    authServiceSpy.isAuthenticated.and.returnValue(false);
    fixture.detectChanges();

    const loginButton = fixture.nativeElement.querySelector('[data-testid="discord-login-btn"]');
    loginButton.click();

    expect(component.loginWithDiscord).toHaveBeenCalled();
  });
});
