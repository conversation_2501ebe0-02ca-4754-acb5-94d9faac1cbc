import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject } from 'rxjs';

export type Theme = 'light' | 'dark' | 'auto';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private currentTheme = new BehaviorSubject<Theme>('auto');
  public theme$ = this.currentTheme.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.initializeTheme();
    if (isPlatformBrowser(this.platformId)) {
      this.setupSystemThemeListener();
    }
  }

  private initializeTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      // Load theme from localStorage or default to 'auto'
      const savedTheme = localStorage.getItem('theme') as Theme || 'auto';
      this.setTheme(savedTheme);
    } else {
      // On server, just set default theme without localStorage access
      this.currentTheme.next('auto');
      this.applyTheme('auto');
    }
  }

  private setupSystemThemeListener(): void {
    // Listen for system theme changes when in auto mode
    if (isPlatformBrowser(this.platformId) && typeof window !== 'undefined') {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (this.currentTheme.value === 'auto') {
          this.applyTheme('auto');
        }
      });
    }
  }

  setTheme(theme: Theme): void {
    this.currentTheme.next(theme);
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('theme', theme);
    }
    this.applyTheme(theme);
  }

  getCurrentTheme(): Theme {
    return this.currentTheme.value;
  }

  private applyTheme(theme: Theme): void {
    if (isPlatformBrowser(this.platformId) && typeof document !== 'undefined') {
      const body = document.body;
      
      // Remove existing theme classes
      body.classList.remove('light-theme', 'dark-theme');
      
      let effectiveTheme: 'light' | 'dark';
      
      if (theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        effectiveTheme = prefersDark ? 'dark' : 'light';
      } else {
        effectiveTheme = theme;
      }
      
      body.classList.add(`${effectiveTheme}-theme`);
      
      // Also set a data attribute for easier CSS targeting
      body.setAttribute('data-theme', effectiveTheme);
    }
  }

  isDarkMode(): boolean {
    if (isPlatformBrowser(this.platformId) && typeof document !== 'undefined') {
      const body = document.body;
      return body.classList.contains('dark-theme') || 
             body.getAttribute('data-theme') === 'dark';
    }
    return false;
  }

  isLightMode(): boolean {
    if (isPlatformBrowser(this.platformId) && typeof document !== 'undefined') {
      const body = document.body;
      return body.classList.contains('light-theme') || 
             body.getAttribute('data-theme') === 'light';
    }
    return true; // Default to light mode on server
  }
}
