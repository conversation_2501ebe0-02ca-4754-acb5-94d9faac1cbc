# Routing Fix Notes

If the updated server routes configuration doesn't fully fix the navigation issues, consider these additional troubleshooting steps:

## 1. Check LocationStrategy

Make sure the app is using `PathLocationStrategy` (default) instead of `HashLocationStrategy`. With SSR, you should use the default path-based strategy.

## 2. Add Route Tracing

Add route tracing to debug navigation:

```typescript
// In app.config.ts
import { provideRouter, withDebugTracing } from '@angular/router';

export const appConfig: ApplicationConfig = {
  providers: [
    // ...other providers
    provideRouter(routes, withDebugTracing()), // Add debug tracing
    // ...other providers
  ]
};
```

## 3. Check Base Href

Ensure the base href in index.html is set correctly:

```html
<base href="/">
```

## 4. Add Navigation Error Handling

Add navigation error handling to log navigation issues:

```typescript
// In a component that handles navigation (e.g., app.component.ts)
import { Router, NavigationError } from '@angular/router';

constructor(private router: Router) {
  this.router.events.subscribe(event => {
    if (event instanceof NavigationError) {
      console.error('Navigation Error', event.error);
    }
  });
}
```

## 5. Check Guards and Resolvers

If you have route guards or resolvers, make sure they are properly handling both client and server environments.

## 6. Check for Universal-specific Issues

Some browser-specific APIs aren't available during server-side rendering. Ensure all code that uses browser APIs is properly guarded:

```typescript
import { isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID, Inject } from '@angular/core';

constructor(@Inject(PLATFORM_ID) private platformId: Object) {
  if (isPlatformBrowser(this.platformId)) {
    // Browser-only code
  }
}
```
