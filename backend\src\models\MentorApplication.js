const mongoose = require('mongoose');

const MentorApplicationSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  name: { type: String, required: true },
  discord_id: { type: String, required: true },
  proficiencies: [{ type: String, enum: ['streaming', 'contentCreation', 'communityBuilding', 'marketing', 'technical'] }],
  status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('MentorApplication', MentorApplicationSchema);
