<div class="mentor-detail-container">
  <button mat-button color="primary" class="back-button" (click)="goBack()">
    <mat-icon>arrow_back</mat-icon> Back to Mentors
  </button>
  
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading mentor profile...</p>
    </div>
  } @else if (errorMessage) {
    <div class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Back to Mentors</button>
    </div>
  } @else if (mentor) {
    <div class="mentor-profile">
      <div class="profile-header">
        <div class="profile-avatar" [style.background-image]="mentor.discordAvatar ? 'url(' + mentor.discordAvatar + ')' : ''">
          @if (!mentor.discordAvatar) {
            <mat-icon>person</mat-icon>
          }
        </div>
        <div class="profile-info">
          <h1>{{ mentor.discordUsername }}</h1>
          <div class="mentor-badge">Mentor</div>
          
          @if (mentor.socialLinks) {
            <div class="social-links">
              @if (mentor.socialLinks.twitch) {
                <a [href]="mentor.socialLinks.twitch" target="_blank" mat-icon-button matTooltip="Twitch">
                  <mat-icon>videogame_asset</mat-icon>
                </a>
              }
              @if (mentor.socialLinks.twitter) {
                <a [href]="mentor.socialLinks.twitter" target="_blank" mat-icon-button matTooltip="Twitter">
                  <mat-icon>chat</mat-icon>
                </a>
              }
              @if (mentor.socialLinks.youtube) {
                <a [href]="mentor.socialLinks.youtube" target="_blank" mat-icon-button matTooltip="YouTube">
                  <mat-icon>video_library</mat-icon>
                </a>
              }
              @if (mentor.socialLinks.instagram) {
                <a [href]="mentor.socialLinks.instagram" target="_blank" mat-icon-button matTooltip="Instagram">
                  <mat-icon>photo_camera</mat-icon>
                </a>
              }
            </div>
          }
          
          <button mat-raised-button color="primary" class="contact-button" (click)="contactMentor()">
            <mat-icon>message</mat-icon> Contact Mentor
          </button>
        </div>
      </div>
      
      <mat-divider></mat-divider>
      
      <mat-tab-group animationDuration="0ms">
        <mat-tab label="About">
          <div class="tab-content">
            @if (mentor.bio) {
              <div class="bio-section">
                <h2>Bio</h2>
                <p>{{ mentor.bio }}</p>
              </div>
            }
            
            <div class="proficiencies-section">
              <h2>Proficiencies</h2>
              
              @for (category of getSelectedCategories(); track category) {
                <div class="category-section">
                  <h3>{{ category }}</h3>
                  <div class="proficiency-chips">
                    @for (proficiency of getProficienciesForCategory(category); track proficiency) {
                      <mat-chip color="primary" selected>{{ proficiency }}</mat-chip>
                    }
                  </div>
                </div>
              }
              
              @if (getSelectedCategories().length === 0) {
                <p class="no-proficiencies">No proficiencies selected yet.</p>
              }
            </div>
          </div>
        </mat-tab>
        
        <mat-tab label="Content">
          <div class="tab-content">
            @if (isLoadingContent) {
              <div class="loading-container">
                <mat-spinner diameter="30"></mat-spinner>
                <p>Loading content...</p>
              </div>
            } @else if (mentorContent.length === 0) {
              <div class="no-content">
                <mat-icon>video_library</mat-icon>
                <p>This mentor hasn't published any content yet.</p>
              </div>
            } @else {
              <div class="content-grid">
                @for (content of mentorContent; track content._id) {
                  <mat-card class="content-card" [routerLink]="['/content', content._id]">
                    <div class="content-thumbnail" [style.background-image]="content.thumbnailUrl ? 'url(' + content.thumbnailUrl + ')' : ''">
                      @if (!content.thumbnailUrl) {
                        <mat-icon>video_library</mat-icon>
                      }
                    </div>
                    <div class="content-info">
                      <h3>{{ content.title }}</h3>
                      <p>{{ content.description.substring(0, 100) }}{{ content.description.length > 100 ? '...' : '' }}</p>
                      <div class="content-meta">
                        <span class="category">{{ content.category }}</span>
                        <span class="views"><mat-icon>visibility</mat-icon> {{ content.views }}</span>
                      </div>
                    </div>
                  </mat-card>
                }
              </div>
            }
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  }
</div>
