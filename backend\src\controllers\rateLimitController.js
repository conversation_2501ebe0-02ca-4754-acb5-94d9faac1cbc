const logger = require('../config/logger');

/**
 * Get rate limiting statistics (disabled)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRateLimitStats = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        message: 'Rate limiting is disabled',
        enabled: false,
        summary: {
          totalHits: 0,
          uniqueIPs: 0,
          suspiciousIPs: 0,
          rateLimitActive: false
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching rate limit statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch rate limiting statistics'
    });
  }
};

/**
 * Clear rate limiting data (no-op since rate limiting is disabled)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const clearRateLimitData = async (req, res) => {
  try {
    logger.info(`Rate limit data clear requested by admin user: ${req.user?.id} (no-op - rate limiting disabled)`);
    
    res.json({
      success: true,
      message: 'Rate limiting is disabled - no data to clear'
    });
  } catch (error) {
    logger.error('Error clearing rate limit data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear rate limiting data'
    });
  }
};

/**
 * Get current trusted IPs (returns empty since rate limiting is disabled)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTrustedIPs = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        message: 'Rate limiting is disabled - no trusted IPs configured',
        trustedIPs: [],
        environmentIPs: [],
        hardcodedIPs: [],
        rateLimitingEnabled: false
      }
    });
  } catch (error) {
    logger.error('Error fetching trusted IPs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trusted IPs'
    });
  }
};

/**
 * Get blocked/suspicious IPs (returns empty since rate limiting is disabled)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSuspiciousIPs = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        message: 'Rate limiting is disabled - no suspicious IPs tracked',
        suspiciousIPs: [],
        recentBlocked: [],
        summary: {
          totalSuspicious: 0,
          rateLimitingEnabled: false
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching suspicious IPs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch suspicious IPs'
    });
  }
};

module.exports = {
  getRateLimitStats,
  clearRateLimitData,
  getTrustedIPs,
  getSuspiciousIPs
};
