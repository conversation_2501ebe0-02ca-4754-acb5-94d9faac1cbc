/**
 * Cache clearing script for HypeHive
 * 
 * This script helps clear various caches and provides cache management utilities
 */

const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

class CacheManager {
  constructor() {
    this.cacheDir = path.join(__dirname, '../.cache');
    this.manifestPath = path.join(this.cacheDir, 'cache-manifest.json');
  }

  async ensureCacheDir() {
    await fs.ensureDir(this.cacheDir);
  }

  async generateCacheManifest() {
    console.log('📝 Generating cache manifest...');
    
    try {
      await this.ensureCacheDir();
      
      const distPath = path.join(__dirname, '../dist');
      if (!await fs.pathExists(distPath)) {
        console.log('⏭️  Dist folder not found, skipping manifest generation');
        return;
      }

      const files = await this.getFileList(distPath);
      const manifest = {
        version: require('../package.json').version,
        buildDate: new Date().toISOString(),
        files: {}
      };

      for (const file of files) {
        const filePath = path.join(distPath, file);
        const stats = await fs.stat(filePath);
        const content = await fs.readFile(filePath);
        const hash = crypto.createHash('md5').update(content).digest('hex');
        
        manifest.files[file] = {
          hash,
          size: stats.size,
          lastModified: stats.mtime.toISOString()
        };
      }

      await fs.writeJson(this.manifestPath, manifest, { spaces: 2 });
      console.log('✅ Cache manifest generated');
    } catch (error) {
      console.error('❌ Error generating cache manifest:', error.message);
    }
  }

  async getFileList(dir, baseDir = dir) {
    const files = [];
    const items = await fs.readdir(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        const subFiles = await this.getFileList(itemPath, baseDir);
        files.push(...subFiles);
      } else {
        files.push(path.relative(baseDir, itemPath));
      }
    }
    
    return files;
  }

  async clearCache() {
    console.log('🗑️  Clearing application cache...');
    
    const cachePaths = [
      path.join(__dirname, '../.cache'),
      path.join(__dirname, '../node_modules/.cache'),
      path.join(__dirname, '../.angular'),
      path.join(__dirname, '../.nx'),
      path.join(__dirname, '../dist/.cache')
    ];

    for (const cachePath of cachePaths) {
      try {
        if (await fs.pathExists(cachePath)) {
          await fs.remove(cachePath);
          console.log(`✅ Cleared cache: ${path.basename(cachePath)}`);
        } else {
          console.log(`⏭️  Cache not found: ${path.basename(cachePath)}`);
        }
      } catch (error) {
        console.error(`❌ Error clearing cache ${cachePath}:`, error.message);
      }
    }
  }

  async createCacheHeaders() {
    console.log('📋 Creating cache headers configuration...');
    
    try {
      const cacheConfig = {
        // Static assets with long cache (1 year)
        staticAssets: {
          pattern: /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/,
          maxAge: 31536000, // 1 year
          immutable: true
        },
        // HTML files with short cache (5 minutes)
        htmlFiles: {
          pattern: /\.html$/,
          maxAge: 300, // 5 minutes
          mustRevalidate: true
        },
        // API responses with moderate cache (1 hour)
        apiResponses: {
          pattern: /\/api\//,
          maxAge: 3600, // 1 hour
          mustRevalidate: true
        },
        // Dynamic content with no cache
        dynamicContent: {
          pattern: /\/(chat|notifications|real-time)/,
          maxAge: 0,
          noCache: true
        }
      };

      await this.ensureCacheDir();
      await fs.writeJson(path.join(this.cacheDir, 'cache-config.json'), cacheConfig, { spaces: 2 });
      console.log('✅ Cache headers configuration created');
    } catch (error) {
      console.error('❌ Error creating cache headers:', error.message);
    }
  }

  async validateCache() {
    console.log('🔍 Validating cache integrity...');
    
    try {
      if (!await fs.pathExists(this.manifestPath)) {
        console.log('⏭️  No cache manifest found');
        return false;
      }

      const manifest = await fs.readJson(this.manifestPath);
      const distPath = path.join(__dirname, '../dist');
      
      if (!await fs.pathExists(distPath)) {
        console.log('❌ Dist folder not found, cache invalid');
        return false;
      }

      let isValid = true;
      for (const [file, info] of Object.entries(manifest.files)) {
        const filePath = path.join(distPath, file);
        
        if (!await fs.pathExists(filePath)) {
          console.log(`❌ Missing file: ${file}`);
          isValid = false;
          continue;
        }

        const content = await fs.readFile(filePath);
        const hash = crypto.createHash('md5').update(content).digest('hex');
        
        if (hash !== info.hash) {
          console.log(`❌ Hash mismatch for: ${file}`);
          isValid = false;
        }
      }

      console.log(isValid ? '✅ Cache is valid' : '❌ Cache is invalid');
      return isValid;
    } catch (error) {
      console.error('❌ Error validating cache:', error.message);
      return false;
    }
  }
}

async function main() {
  const cacheManager = new CacheManager();
  
  const command = process.argv[2] || 'clear';
  
  switch (command) {
    case 'clear':
      await cacheManager.clearCache();
      break;
    case 'generate':
      await cacheManager.generateCacheManifest();
      break;
    case 'validate':
      await cacheManager.validateCache();
      break;
    case 'headers':
      await cacheManager.createCacheHeaders();
      break;
    case 'all':
      await cacheManager.clearCache();
      await cacheManager.createCacheHeaders();
      break;
    default:
      console.log('Usage: node cache-clear.js [clear|generate|validate|headers|all]');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = CacheManager;
