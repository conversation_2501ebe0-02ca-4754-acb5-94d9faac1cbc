# Upload API

The Upload API provides endpoints for uploading files such as images and videos to the HypeHive platform.

## Endpoints

### Upload File

Uploads a file to the server. This endpoint supports multipart/form-data uploads.

```
POST /api/upload
```

#### Headers

| Header        | Value                        | Required |
|---------------|------------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN        | Yes      |
| Content-Type  | multipart/form-data          | Yes      |

#### Form Data

| Parameter | Type | Required | Description                                      |
|-----------|------|----------|--------------------------------------------------|
| file      | File | Yes      | The file to upload                               |
| type      | string | Yes    | Type of file (thumbnail, video)                  |

#### Response

```json
{
  "success": true,
  "data": {
    "url": "/uploads/thumbnails/filename.jpg",
    "filename": "filename.jpg",
    "mimetype": "image/jpeg",
    "size": 123456
  },
  "message": "File uploaded successfully"
}
```

#### Status Codes

| Status Code | Description                                                  |
|-------------|--------------------------------------------------------------|
| 201         | Created - File uploaded successfully                         |
| 400         | Bad Request - Invalid file type or missing file              |
| 401         | Unauthorized - Invalid or missing token                      |
| 413         | Payload Too Large - File exceeds size limit                  |
| 500         | Server Error - Failed to process the request                 |

#### Example Request

```bash
curl -X POST http://localhost:3000/api/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/file.jpg" \
  -F "type=thumbnail"
```

### Upload Multiple Files

Uploads multiple files to the server. This endpoint supports multipart/form-data uploads.

```
POST /api/upload/multiple
```

#### Headers

| Header        | Value                        | Required |
|---------------|------------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN        | Yes      |
| Content-Type  | multipart/form-data          | Yes      |

#### Form Data

| Parameter | Type | Required | Description                                      |
|-----------|------|----------|--------------------------------------------------|
| files     | File[] | Yes    | The files to upload                              |
| type      | string | Yes    | Type of files (thumbnail, video)                 |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "url": "/uploads/thumbnails/filename1.jpg",
      "filename": "filename1.jpg",
      "mimetype": "image/jpeg",
      "size": 123456
    },
    {
      "url": "/uploads/thumbnails/filename2.jpg",
      "filename": "filename2.jpg",
      "mimetype": "image/jpeg",
      "size": 234567
    }
  ],
  "message": "Files uploaded successfully"
}
```

## File Types and Limitations

### Supported File Types

#### Images
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

#### Videos
- MP4 (.mp4)
- WebM (.webm)
- MOV (.mov)

### File Size Limits

- Images: 5MB
- Videos: 100MB

## File Storage

Uploaded files are stored in the following directories:

- Thumbnails: `/uploads/thumbnails/`
- Videos: `/uploads/videos/`

## File URLs

Files can be accessed using the following URL pattern:

```
http://localhost:3000/uploads/{type}/{filename}
```

For example:

```
http://localhost:3000/uploads/thumbnails/filename.jpg
```

## Error Handling

Common upload errors:

- **File Too Large**: The uploaded file exceeds the size limit
- **Invalid File Type**: The uploaded file type is not supported
- **Missing File**: No file was provided in the request
- **Storage Error**: Failed to store the file on the server

## Security Considerations

- All uploaded files are scanned for viruses and malware
- File names are sanitized to prevent path traversal attacks
- File types are validated by examining the file content, not just the extension
- Authentication is required to prevent unauthorized uploads
