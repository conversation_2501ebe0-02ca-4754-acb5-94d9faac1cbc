import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-callback',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule],
  template: `
    <div class="callback-container">
      <h2>Authenticating...</h2>
      <mat-spinner></mat-spinner>
      @if (errorMessage) {
        <div class="error-message">
          {{ errorMessage }}
        </div>
      }
    </div>
  `,
  styles: [`
    .callback-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      text-align: center;
    }
    
    h2 {
      margin-bottom: 2rem;
      color: #6441a5;
    }
    
    .error-message {
      margin-top: 2rem;
      padding: 1rem;
      background-color: #ffebee;
      color: #c62828;
      border-radius: 4px;
      max-width: 400px;
    }
  `]
})
export class CallbackComponent implements OnInit {
  errorMessage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const code = params['code'];
      const error = params['error'];
      
      if (error) {
        this.errorMessage = `Authentication error: ${error}`;
        setTimeout(() => this.router.navigate(['/auth/login']), 3000);
        return;
      }
      
      if (!code) {
        this.errorMessage = 'No authorization code received';
        setTimeout(() => this.router.navigate(['/auth/login']), 3000);
        return;
      }
      
      this.authService.handleAuthCallback(code).subscribe({
        next: () => {
          this.router.navigate(['/profile']);
        },
        error: (err) => {
          this.errorMessage = 'Failed to authenticate with Discord. Please try again.';
          console.error('Auth callback error:', err);
          setTimeout(() => this.router.navigate(['/auth/login']), 3000);
        }
      });
    });
  }
}
