const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../config/logger');
const { AppError, catchAsync } = require('./errorHandler');
const { isSystemAdmin } = require('./security');

/**
 * Middleware to protect routes that require authentication
 */
const protect = catchAsync(async (req, res, next) => {
  let token;

  // Check if token exists in Authorization header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check if token exists in cookies (for server-side auth)
  else if (req.cookies && req.cookies.auth_token) {
    token = req.cookies.auth_token;
  }

  // Check if token exists
  if (!token) {
    return next(new AppError('Not authorized to access this route', 401));
  }

  // Verify token
  const decoded = jwt.verify(token, process.env.JWT_SECRET);

  // Get user from database
  const user = await User.findById(decoded.id);

  // Check if user exists
  if (!user) {
    return next(new AppError('The user belonging to this token does no longer exist', 401));
  }

  // Check if user is active
  if (!user.isActive) {
    return next(new AppError('User account is deactivated', 401));
  }

  // Auto-promote system administrators
  if (isSystemAdmin(user.discordId) && user.role !== 'admin') {
    logger.info(`Auto-promoting system admin user: ${user.discordId} (${user.discordUsername}) to admin role`);
    await User.findByIdAndUpdate(user._id, { 
      role: 'admin',
      isMentor: true // System admins are also mentors
    });
    user.role = 'admin';
    user.isMentor = true;
  }

  // Attach user to request object
  req.user = user;
  next();
});

/**
 * Middleware to restrict access to specific roles
 * @param {...String} roles - Roles allowed to access the route
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    // System admins have access to everything
    if (isSystemAdmin(req.user.discordId)) {
      logger.debug(`System admin access granted for: ${req.user.discordId} (${req.user.discordUsername})`);
      return next();
    }
    
    // Check if user role is included in the allowed roles
    if (!roles.includes(req.user.role)) {
      return next(new AppError(`User role ${req.user.role} is not authorized to access this route`, 403));
    }
    next();
  };
};

module.exports = {
  protect,
  authorize
};
