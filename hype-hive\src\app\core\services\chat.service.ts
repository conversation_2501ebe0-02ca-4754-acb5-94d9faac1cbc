import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, map, tap, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { 
  Chat, 
  ChatCreationData, 
  ChatMessage, 
  ChatPaginationResult, 
  NewMessageEvent, 
  TypingIndicator 
} from '../models/chat.model';
import { SocketService } from './socket.service';
import { MonitoringService } from './monitoring.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private apiUrl = `/api/chat`;
  private currentChatId = new BehaviorSubject<string | null>(null);
  private activeChats = new BehaviorSubject<Chat[]>([]);
  private typingUsers = new BehaviorSubject<Map<string, Set<string>>>(new Map());

  constructor(
    private http: HttpClient,
    private socketService: SocketService,
    private monitoringService: MonitoringService
  ) {
    // Initialize socket listeners
    this.initializeSocketListeners();
  }

  /**
   * Initialize socket listeners for real-time updates
   */
  private initializeSocketListeners(): void {
    // Listen for new messages
    this.socketService.onNewMessage().subscribe((data: NewMessageEvent) => {
      this.handleNewMessage(data);
    });

    // Listen for typing indicators
    this.socketService.onTyping().subscribe((data: TypingIndicator) => {
      this.handleTypingIndicator(data);
    });

    // Listen for stop typing indicators
    this.socketService.onStopTyping().subscribe((data: TypingIndicator) => {
      this.handleStopTypingIndicator(data);
    });
  }

  /**
   * Handle new message event
   * @param data New message event data
   */
  private handleNewMessage(data: NewMessageEvent): void {
    const { chatId, message } = data;
    
    // Update active chats with new message
    const currentChats = this.activeChats.value;
    const updatedChats = currentChats.map(chat => {
      if (chat._id === chatId) {
        // Add message to chat
        const updatedMessages = [...chat.messages, message];
        
        // Update last message
        const lastMessage = {
          content: message.content,
          sender: message.sender,
          createdAt: message.createdAt
        };
        
        return {
          ...chat,
          messages: updatedMessages,
          lastMessage,
          updatedAt: message.createdAt
        };
      }
      return chat;
    });
    
    this.activeChats.next(updatedChats);
    
    // Track message received
    this.monitoringService.trackEvent('chat_message_received', { chatId });
  }

  /**
   * Handle typing indicator
   * @param data Typing indicator data
   */
  private handleTypingIndicator(data: TypingIndicator): void {
    const { chatId, user } = data;
    
    // Get current typing users
    const typingMap = new Map(this.typingUsers.value);
    
    // Get or create set for this chat
    let chatTypingUsers = typingMap.get(chatId);
    if (!chatTypingUsers) {
      chatTypingUsers = new Set<string>();
      typingMap.set(chatId, chatTypingUsers);
    }
    
    // Add user to typing set
    chatTypingUsers.add(user.discordUsername);
    
    // Update typing users
    this.typingUsers.next(typingMap);
  }

  /**
   * Handle stop typing indicator
   * @param data Typing indicator data
   */
  private handleStopTypingIndicator(data: TypingIndicator): void {
    const { chatId, user } = data;
    
    // Get current typing users
    const typingMap = new Map(this.typingUsers.value);
    
    // Get set for this chat
    const chatTypingUsers = typingMap.get(chatId);
    if (chatTypingUsers) {
      // Remove user from typing set
      chatTypingUsers.delete(user.discordUsername);
      
      // Update typing users
      this.typingUsers.next(typingMap);
    }
  }

  /**
   * Get user chats
   * @param page Page number
   * @param limit Items per page
   * @returns Observable of chat pagination result
   */
  public getUserChats(page = 1, limit = 20): Observable<ChatPaginationResult> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    
    return this.http.get<any>(this.apiUrl, { params }).pipe(
      map(response => {
        if (response.success) {
          // Update active chats
          this.activeChats.next(response.data);
          
          return {
            chats: response.data,
            pagination: response.pagination
          };
        }
        throw new Error(response.message || 'Failed to get chats');
      }),
      catchError(error => {
        this.monitoringService.trackError('get_chats_error', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get chat by ID
   * @param chatId Chat ID
   * @returns Observable of chat
   */
  public getChatById(chatId: string): Observable<Chat> {
    return this.http.get<any>(`${this.apiUrl}/${chatId}`).pipe(
      map(response => {
        if (response.success) {
          // Set current chat ID
          this.currentChatId.next(chatId);
          
          // Join chat room for real-time updates
          this.socketService.joinChat(chatId);
          
          return response.data;
        }
        throw new Error(response.message || 'Failed to get chat');
      }),
      catchError(error => {
        this.monitoringService.trackError('get_chat_error', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new chat
   * @param chatData Chat creation data
   * @returns Observable of created chat
   */
  public createChat(chatData: ChatCreationData): Observable<Chat> {
    return this.http.post<any>(this.apiUrl, chatData).pipe(
      map(response => {
        if (response.success) {
          // Add to active chats
          const currentChats = this.activeChats.value;
          this.activeChats.next([...currentChats, response.data]);
          
          // Set as current chat
          this.currentChatId.next(response.data._id);
          
          // Join chat room
          this.socketService.joinChat(response.data._id);
          
          return response.data;
        }
        throw new Error(response.message || 'Failed to create chat');
      }),
      catchError(error => {
        this.monitoringService.trackError('create_chat_error', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Send a message
   * @param chatId Chat ID
   * @param content Message content
   * @returns Observable of sent message
   */
  public sendMessage(chatId: string, content: string): Observable<ChatMessage> {
    // Send via socket for real-time delivery
    this.socketService.sendMessage(chatId, content);
    
    // Also send via HTTP for persistence
    return this.http.post<any>(`${this.apiUrl}/${chatId}/message`, { content }).pipe(
      map(response => {
        if (response.success) {
          // Track message sent
          this.monitoringService.trackEvent('chat_message_sent', { chatId });
          
          return response.data;
        }
        throw new Error(response.message || 'Failed to send message');
      }),
      catchError(error => {
        this.monitoringService.trackError('send_message_error', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Mark messages as read
   * @param chatId Chat ID
   * @returns Observable of success status
   */
  public markMessagesAsRead(chatId: string): Observable<boolean> {
    return this.http.put<any>(`${this.apiUrl}/${chatId}/read`, {}).pipe(
      map(response => response.success || false),
      catchError(() => {
        return throwError(() => new Error('Failed to mark messages as read'));
      })
    );
  }

  /**
   * Send typing indicator
   * @param chatId Chat ID
   */
  public sendTyping(chatId: string): void {
    this.socketService.sendTyping(chatId);
  }

  /**
   * Send stop typing indicator
   * @param chatId Chat ID
   */
  public sendStopTyping(chatId: string): void {
    this.socketService.sendStopTyping(chatId);
  }

  /**
   * Get current chat ID
   * @returns Observable of current chat ID
   */
  public getCurrentChatId(): Observable<string | null> {
    return this.currentChatId.asObservable();
  }

  /**
   * Get active chats
   * @returns Observable of active chats
   */
  public getActiveChats(): Observable<Chat[]> {
    return this.activeChats.asObservable();
  }

  /**
   * Get typing users for a chat
   * @param chatId Chat ID
   * @returns Observable of typing usernames
   */
  public getTypingUsers(chatId: string): Observable<string[]> {
    return this.typingUsers.pipe(
      map(typingMap => {
        const chatTypingUsers = typingMap.get(chatId);
        return chatTypingUsers ? Array.from(chatTypingUsers) : [];
      })
    );
  }

  /**
   * Leave current chat
   */
  public leaveCurrentChat(): void {
    const chatId = this.currentChatId.value;
    if (chatId) {
      this.socketService.leaveChat(chatId);
      this.currentChatId.next(null);
    }
  }
}
