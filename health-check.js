#!/usr/bin/env node

/**
 * Health check and troubleshooting script for HypeHive tunnel issues
 */

const http = require('http');
const https = require('https');
const { spawn } = require('child_process');

const config = {
  localPorts: {
    frontend: 4200,
    backend: 3000
  },
  tunnelDomain: 'hypehive.linksynk.info',
  timeouts: {
    http: 5000,
    tunnel: 10000
  }
};

console.log('🔍 HypeHive Health Check & Troubleshooting Tool');
console.log('================================================\n');

// Helper function to check if a port is accessible
function checkPort(host, port, timeout = 5000) {
  return new Promise((resolve) => {
    const requestModule = host.startsWith('https') ? https : http;
    const url = `${host.startsWith('http') ? host : `http://${host}`}:${port}`;
    
    const req = requestModule.get(url, (res) => {
      resolve({
        success: true,
        status: res.statusCode,
        url: url
      });
    });
    
    req.on('error', (err) => {
      resolve({
        success: false,
        error: err.message,
        url: url
      });
    });
    
    req.setTimeout(timeout, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout',
        url: url
      });
    });
  });
}

// Check tunnel connectivity
function checkTunnel(domain, timeout = 10000) {
  return new Promise((resolve) => {
    const url = `https://${domain}`;
    
    https.get(url, (res) => {
      resolve({
        success: true,
        status: res.statusCode,
        url: url
      });
    }).on('error', (err) => {
      resolve({
        success: false,
        error: err.message,
        url: url
      });
    }).setTimeout(timeout, function() {
      this.destroy();
      resolve({
        success: false,
        error: 'Tunnel timeout',
        url: url
      });
    });
  });
}

// Check if processes are running
async function checkProcesses() {
  console.log('📋 Checking running processes...');
  
  const processes = [
    { name: 'Angular Dev Server', pattern: 'ng serve', port: 4200 },
    { name: 'Node.js Backend', pattern: 'node.*app.js|nodemon', port: 3000 },
    { name: 'Cloudflared Tunnel', pattern: 'cloudflared' }
  ];
  
  for (const proc of processes) {
    try {
      const result = await new Promise((resolve) => {
        const ps = spawn('powershell', ['-Command', `Get-Process | Where-Object {$_.ProcessName -match "${proc.pattern}" -or $_.CommandLine -match "${proc.pattern}"} | Select-Object ProcessName, Id`], {
          stdio: 'pipe'
        });
        
        let output = '';
        ps.stdout.on('data', (data) => output += data.toString());
        ps.on('close', () => resolve(output.trim()));
        ps.on('error', () => resolve(''));
      });
      
      if (result && result.length > 0) {
        console.log(`✅ ${proc.name}: Running`);
      } else {
        console.log(`❌ ${proc.name}: Not found`);
        if (proc.port) {
          console.log(`   Expected on port ${proc.port}`);
        }
      }
    } catch (error) {
      console.log(`❓ ${proc.name}: Unable to check (${error.message})`);
    }
  }
  console.log();
}

// Main health check function
async function runHealthCheck() {
  try {
    // Check local services
    console.log('🌐 Checking local services...');
    
    const frontendCheck = await checkPort('localhost', config.localPorts.frontend);
    console.log(`Frontend (${config.localPorts.frontend}): ${frontendCheck.success ? '✅ Running' : '❌ ' + frontendCheck.error}`);
    
    const backendCheck = await checkPort('localhost', config.localPorts.backend);
    console.log(`Backend (${config.localPorts.backend}): ${backendCheck.success ? '✅ Running' : '❌ ' + backendCheck.error}`);
    
    console.log();
    
    // Check tunnel
    console.log('🚇 Checking tunnel connectivity...');
    const tunnelCheck = await checkTunnel(config.tunnelDomain);
    console.log(`Tunnel (${config.tunnelDomain}): ${tunnelCheck.success ? '✅ Connected' : '❌ ' + tunnelCheck.error}`);
    
    if (tunnelCheck.success && tunnelCheck.status) {
      console.log(`   Status Code: ${tunnelCheck.status}`);
    }
    
    console.log();
    
    // Check processes
    await checkProcesses();
    
    // Provide recommendations
    console.log('💡 Recommendations:');
    
    if (!frontendCheck.success) {
      console.log('   • Start the Angular dev server: cd hype-hive && npm start');
    }
    
    if (!backendCheck.success) {
      console.log('   • Start the backend server: cd backend && npm start');
    }
    
    if (!tunnelCheck.success) {
      console.log('   • Check cloudflared tunnel status');
      console.log('   • Verify tunnel configuration');
      console.log('   • Consider using local development mode');
    }
    
    if (frontendCheck.success && backendCheck.success && !tunnelCheck.success) {
      console.log('   • Local services are running, tunnel may be down');
      console.log('   • Switch to local development configuration');
    }
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

// Run the health check
runHealthCheck();
