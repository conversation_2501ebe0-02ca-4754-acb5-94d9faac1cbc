import { User } from './user.model';

/**
 * Notification types
 */
export type NotificationType = 'like' | 'comment' | 'mention' | 'message' | 'mentor_application' | 'system';

/**
 * Related content types
 */
export type ContentType = 'content' | 'chat' | 'user' | 'system';

/**
 * Related content
 */
export interface RelatedContent {
  contentType: ContentType;
  contentId: string;
}

/**
 * Notification model
 */
export interface Notification {
  _id: string;
  recipient: User | string;
  sender?: User | string;
  type: NotificationType;
  content: string;
  isRead: boolean;
  relatedContent: RelatedContent;
  createdAt: Date;
}

/**
 * Notification pagination result
 */
export interface NotificationPaginationResult {
  notifications: Notification[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}
