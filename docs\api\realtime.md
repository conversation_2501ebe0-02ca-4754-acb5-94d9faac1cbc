# Real-time Features API

The HypeHive platform provides robust real-time functionality using Socket.io to enable instant messaging, notifications, typing indicators, and presence detection. This document provides a comprehensive guide to all real-time features available in the platform.

## Socket.io Connection

All real-time features use Socket.io for communication. To connect to the Socket.io server, you need to authenticate with your JWT token.

### Connection Setup

```javascript
import { io } from 'socket.io-client';

const socket = io('http://localhost:3000', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  },
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000
});
```

### Connection Events

| Event              | Data                | Description                                      |
|--------------------|---------------------|--------------------------------------------------|
| connect            | -                   | Fired when the connection is established         |
| disconnect         | reason              | Fired when the connection is closed              |
| reconnect_attempt  | attempt             | Fired when trying to reconnect                   |
| reconnect          | attempt             | Fired when successfully reconnected              |
| reconnect_failed   | -                   | Fired when all reconnection attempts have failed |
| error              | error               | Fired when an error occurs                       |

### Example: Handling Connection Events

```javascript
// Connection established
socket.on('connect', () => {
  console.log('Connected to Socket.io server');
});

// Connection closed
socket.on('disconnect', (reason) => {
  console.log(`Disconnected: ${reason}`);
});

// Reconnection attempt
socket.on('reconnect_attempt', (attempt) => {
  console.log(`Reconnection attempt: ${attempt}`);
});

// Successfully reconnected
socket.on('reconnect', (attempt) => {
  console.log(`Reconnected after ${attempt} attempts`);
});

// All reconnection attempts failed
socket.on('reconnect_failed', () => {
  console.log('Failed to reconnect');
});

// Error occurred
socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

## Chat Features

The platform provides real-time chat functionality, allowing users to send and receive messages instantly.

### Chat Events

#### Client Events (Emitted by the client)

| Event         | Data                                | Description                                      |
|---------------|-------------------------------------|--------------------------------------------------|
| join_chat     | chatId                              | Join a chat room                                 |
| leave_chat    | chatId                              | Leave a chat room                                |
| send_message  | { chatId, content }                 | Send a message to a chat                         |
| typing        | chatId                              | Indicate that the user is typing                 |
| stop_typing   | chatId                              | Indicate that the user has stopped typing        |

#### Server Events (Emitted by the server)

| Event         | Data                                | Description                                      |
|---------------|-------------------------------------|--------------------------------------------------|
| new_message   | { chatId, message }                 | New message received                             |
| typing        | { chatId, user }                    | User is typing                                   |
| stop_typing   | { chatId, user }                    | User has stopped typing                          |
| error         | { message }                         | Error message                                    |

### Example: Chat Implementation

```javascript
// Join a chat room
function joinChat(chatId) {
  socket.emit('join_chat', chatId);
}

// Leave a chat room
function leaveChat(chatId) {
  socket.emit('leave_chat', chatId);
}

// Send a message
function sendMessage(chatId, content) {
  socket.emit('send_message', { chatId, content });
}

// Indicate typing
function sendTypingIndicator(chatId) {
  socket.emit('typing', chatId);
}

// Indicate stopped typing
function sendStopTypingIndicator(chatId) {
  socket.emit('stop_typing', chatId);
}

// Listen for new messages
socket.on('new_message', (data) => {
  const { chatId, message } = data;
  console.log(`New message in chat ${chatId}:`, message);
  // Update UI with new message
});

// Listen for typing indicators
socket.on('typing', (data) => {
  const { chatId, user } = data;
  console.log(`${user.discordUsername} is typing in chat ${chatId}`);
  // Show typing indicator in UI
});

// Listen for stop typing indicators
socket.on('stop_typing', (data) => {
  const { chatId, user } = data;
  console.log(`${user.discordUsername} stopped typing in chat ${chatId}`);
  // Hide typing indicator in UI
});
```

## Notification Features

The platform provides real-time notifications for various events such as likes, comments, mentions, and messages.

### Notification Events

#### Server Events (Emitted by the server)

| Event            | Data                                | Description                                      |
|------------------|-------------------------------------|--------------------------------------------------|
| new_notification | notification                        | New notification received                         |

### Example: Notification Implementation

```javascript
// Listen for new notifications
socket.on('new_notification', (notification) => {
  console.log('New notification:', notification);
  // Update UI to show new notification
  
  // Example: Display notification based on type
  switch (notification.type) {
    case 'like':
      showLikeNotification(notification);
      break;
    case 'comment':
      showCommentNotification(notification);
      break;
    case 'mention':
      showMentionNotification(notification);
      break;
    case 'message':
      showMessageNotification(notification);
      break;
    case 'mentor_application':
      showMentorApplicationNotification(notification);
      break;
    case 'system':
      showSystemNotification(notification);
      break;
  }
});
```

## Presence Detection

The platform provides presence detection to show which users are online.

### Presence Events

#### Server Events (Emitted by the server)

| Event            | Data                                | Description                                      |
|------------------|-------------------------------------|--------------------------------------------------|
| user_online      | userId                              | User came online                                 |
| user_offline     | userId                              | User went offline                                |

### Example: Presence Implementation

```javascript
// Listen for user online events
socket.on('user_online', (userId) => {
  console.log(`User ${userId} is now online`);
  // Update UI to show user as online
});

// Listen for user offline events
socket.on('user_offline', (userId) => {
  console.log(`User ${userId} is now offline`);
  // Update UI to show user as offline
});
```

## Performance Monitoring

The platform includes performance monitoring for real-time features to ensure optimal performance.

### Monitoring Metrics

- Socket connection latency
- Message delivery time
- Error rates
- Active connections

### Example: Monitoring Implementation

```javascript
// Track socket latency
function trackSocketLatency() {
  const startTime = Date.now();
  
  socket.emit('ping', () => {
    const latency = Date.now() - startTime;
    console.log(`Socket latency: ${latency}ms`);
    
    // Send latency to monitoring service
    trackEvent('socket_latency', { latency });
  });
}

// Track message delivery time
function trackMessageDeliveryTime(chatId, messageId, startTime) {
  socket.on('message_delivered', (data) => {
    if (data.messageId === messageId) {
      const deliveryTime = Date.now() - startTime;
      console.log(`Message delivery time: ${deliveryTime}ms`);
      
      // Send delivery time to monitoring service
      trackEvent('message_delivery_time', { deliveryTime });
    }
  });
}
```

## Error Handling

The platform provides robust error handling for real-time features.

### Error Events

| Event         | Data                                | Description                                      |
|---------------|-------------------------------------|--------------------------------------------------|
| error         | { message }                         | Error message                                    |

### Example: Error Handling

```javascript
// Listen for error events
socket.on('error', (error) => {
  console.error('Socket error:', error);
  
  // Handle specific errors
  if (error.message.includes('authentication')) {
    // Handle authentication errors
    handleAuthError(error);
  } else if (error.message.includes('permission')) {
    // Handle permission errors
    handlePermissionError(error);
  } else {
    // Handle other errors
    handleGenericError(error);
  }
});
```

## Best Practices

1. **Always authenticate** with a valid JWT token when connecting to Socket.io.
2. **Handle reconnection** gracefully to ensure a seamless user experience.
3. **Implement error handling** to gracefully recover from errors.
4. **Use room-based communication** for chat to ensure messages are only sent to relevant users.
5. **Optimize performance** by minimizing the number of events and the size of data sent.
6. **Implement typing indicators** with debouncing to reduce the number of events sent.
7. **Handle offline scenarios** by storing messages locally and syncing when reconnected.
8. **Implement read receipts** to show when messages have been read.
9. **Monitor performance** to ensure optimal user experience.
10. **Test thoroughly** with different network conditions to ensure reliability.
