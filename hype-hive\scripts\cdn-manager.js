/**
 * CDN Configuration and Smart Caching for HypeHive
 * 
 * This module handles CDN integration and smart caching strategies
 */

const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

class CDNManager {
  constructor() {
    this.config = {
      // CDN providers configuration
      providers: {
        cloudflare: {
          endpoint: process.env.CLOUDFLARE_CDN_ENDPOINT || 'https://cdnjs.cloudflare.com',
          apiToken: process.env.CLOUDFLARE_API_TOKEN,
          zoneId: process.env.CLOUDFLARE_ZONE_ID,
          purgeEndpoint: 'https://api.cloudflare.com/client/v4/zones/{zone}/purge_cache'
        },
        aws: {
          endpoint: process.env.AWS_CLOUDFRONT_ENDPOINT,
          distributionId: process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID,
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          region: process.env.AWS_REGION || 'us-east-1'
        },
        generic: {
          endpoint: process.env.CDN_ENDPOINT || 'https://hypehive.linksynk.info',
          apiKey: process.env.CDN_API_KEY
        }
      },
      // Cache strategies
      cacheStrategies: {
        static: {
          maxAge: 31536000, // 1 year
          immutable: true,
          extensions: ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot']
        },
        html: {
          maxAge: 300, // 5 minutes
          mustRevalidate: true,
          extensions: ['.html']
        },
        api: {
          maxAge: 3600, // 1 hour
          mustRevalidate: true,
          paths: ['/api/']
        },
        dynamic: {
          maxAge: 0,
          noCache: true,
          paths: ['/chat/', '/notifications/', '/real-time/']
        }
      }
    };
  }

  async generateCDNConfig() {
    console.log('🌐 Generating CDN configuration...');
    
    try {
      const cdnConfig = {
        version: require('../package.json').version,
        buildDate: new Date().toISOString(),
        baseUrl: this.config.providers.generic.endpoint,
        assets: await this.generateAssetManifest(),
        cacheRules: this.generateCacheRules(),
        purgeEndpoints: this.generatePurgeEndpoints()
      };

      await fs.ensureDir(path.join(__dirname, '../dist/assets'));
      await fs.writeJson(path.join(__dirname, '../dist/assets/cdn-config.json'), cdnConfig, { spaces: 2 });
      console.log('✅ CDN configuration generated');
      
      return cdnConfig;
    } catch (error) {
      console.error('❌ Error generating CDN config:', error.message);
      throw error;
    }
  }

  async generateAssetManifest() {
    const distPath = path.join(__dirname, '../dist');
    if (!await fs.pathExists(distPath)) {
      return {};
    }

    const files = await this.getFileList(distPath);
    const manifest = {};

    for (const file of files) {
      const filePath = path.join(distPath, file);
      const stats = await fs.stat(filePath);
      const content = await fs.readFile(filePath);
      const hash = crypto.createHash('md5').update(content).digest('hex');
      
      manifest[file] = {
        hash,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        url: `${this.config.providers.generic.endpoint}/${file}`,
        cacheStrategy: this.getCacheStrategy(file)
      };
    }

    return manifest;
  }

  generateCacheRules() {
    const rules = [];

    // Static assets rule
    rules.push({
      pattern: this.config.cacheStrategies.static.extensions.map(ext => `*${ext}`).join('|'),
      headers: {
        'Cache-Control': `public, max-age=${this.config.cacheStrategies.static.maxAge}, immutable`,
        'Expires': new Date(Date.now() + this.config.cacheStrategies.static.maxAge * 1000).toUTCString()
      }
    });

    // HTML files rule
    rules.push({
      pattern: '*.html',
      headers: {
        'Cache-Control': `public, max-age=${this.config.cacheStrategies.html.maxAge}, must-revalidate`,
        'Expires': new Date(Date.now() + this.config.cacheStrategies.html.maxAge * 1000).toUTCString()
      }
    });

    // API responses rule
    rules.push({
      pattern: '/api/*',
      headers: {
        'Cache-Control': `public, max-age=${this.config.cacheStrategies.api.maxAge}, must-revalidate`,
        'Expires': new Date(Date.now() + this.config.cacheStrategies.api.maxAge * 1000).toUTCString()
      }
    });

    // Dynamic content rule
    rules.push({
      pattern: '/chat/*|/notifications/*|/real-time/*',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    return rules;
  }

  generatePurgeEndpoints() {
    const endpoints = [];

    if (this.config.providers.cloudflare.apiToken) {
      endpoints.push({
        provider: 'cloudflare',
        url: this.config.providers.cloudflare.purgeEndpoint.replace('{zone}', this.config.providers.cloudflare.zoneId),
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.providers.cloudflare.apiToken}`,
          'Content-Type': 'application/json'
        }
      });
    }

    if (this.config.providers.aws.distributionId) {
      endpoints.push({
        provider: 'aws',
        distributionId: this.config.providers.aws.distributionId,
        method: 'POST'
      });
    }

    return endpoints;
  }

  getCacheStrategy(filename) {
    const ext = path.extname(filename).toLowerCase();
    
    if (this.config.cacheStrategies.static.extensions.includes(ext)) {
      return 'static';
    }
    
    if (this.config.cacheStrategies.html.extensions.includes(ext)) {
      return 'html';
    }
    
    return 'default';
  }

  async getFileList(dir, baseDir = dir) {
    const files = [];
    const items = await fs.readdir(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        const subFiles = await this.getFileList(itemPath, baseDir);
        files.push(...subFiles);
      } else {
        files.push(path.relative(baseDir, itemPath));
      }
    }
    
    return files;
  }

  async purgeCache(files = []) {
    console.log('🗑️  Purging CDN cache...');
    
    try {
      const purgeEndpoints = this.generatePurgeEndpoints();
      
      for (const endpoint of purgeEndpoints) {
        switch (endpoint.provider) {
          case 'cloudflare':
            await this.purgeCloudflareCache(endpoint, files);
            break;
          case 'aws':
            await this.purgeAWSCache(endpoint, files);
            break;
          default:
            console.log(`⏭️  Skipping unknown provider: ${endpoint.provider}`);
        }
      }
      
      console.log('✅ CDN cache purged');
    } catch (error) {
      console.error('❌ Error purging CDN cache:', error.message);
    }
  }

  async purgeCloudflareCache(endpoint, files) {
    const fetch = require('node-fetch');
    
    const payload = {
      purge_everything: files.length === 0,
      files: files.length > 0 ? files.map(file => `${this.config.providers.generic.endpoint}/${file}`) : undefined
    };

    try {
      const response = await fetch(endpoint.url, {
        method: endpoint.method,
        headers: endpoint.headers,
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        console.log('✅ Cloudflare cache purged successfully');
      } else {
        console.error('❌ Cloudflare cache purge failed:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error purging Cloudflare cache:', error.message);
    }
  }

  async purgeAWSCache(endpoint, files) {
    console.log('⏭️  AWS CloudFront purging not implemented yet');
    // TODO: Implement AWS CloudFront invalidation
  }

  async createNginxConfig() {
    console.log('📋 Creating Nginx configuration for CDN...');
    
    try {
      const nginxConfig = `
# CDN Configuration for HypeHive
server {
    listen 80;
    server_name hypehive.linksynk.info;
    
    # Static assets with long cache
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-CDN-Cache "HIT";
        try_files $uri =404;
    }
    
    # HTML files with short cache
    location ~* \\.html$ {
        expires 5m;
        add_header Cache-Control "public, must-revalidate";
        add_header X-CDN-Cache "DYNAMIC";
        try_files $uri =404;
    }
    
    # API responses with moderate cache
    location /api/ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
        add_header X-CDN-Cache "API";
        proxy_pass http://localhost:3000;
    }
    
    # Dynamic content with no cache
    location ~* /(chat|notifications|real-time)/ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header X-CDN-Cache "BYPASS";
        proxy_pass http://localhost:3000;
    }
    
    # Default location
    location / {
        try_files $uri $uri/ /index.html;
        expires 5m;
        add_header Cache-Control "public, must-revalidate";
    }
}
`;

      await fs.ensureDir(path.join(__dirname, '../dist/config'));
      await fs.writeFile(path.join(__dirname, '../dist/config/nginx.conf'), nginxConfig.trim());
      console.log('✅ Nginx configuration created');
    } catch (error) {
      console.error('❌ Error creating Nginx config:', error.message);
    }
  }
}

async function main() {
  const cdnManager = new CDNManager();
  
  const command = process.argv[2] || 'generate';
  
  switch (command) {
    case 'generate':
      await cdnManager.generateCDNConfig();
      await cdnManager.createNginxConfig();
      break;
    case 'purge':
      const files = process.argv.slice(3);
      await cdnManager.purgeCache(files);
      break;
    case 'nginx':
      await cdnManager.createNginxConfig();
      break;
    default:
      console.log('Usage: node cdn-manager.js [generate|purge|nginx]');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = CDNManager;
