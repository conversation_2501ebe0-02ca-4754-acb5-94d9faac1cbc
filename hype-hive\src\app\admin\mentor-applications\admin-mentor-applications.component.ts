import { Component, OnInit } from '@angular/core';
import { MentorApplicationService } from './mentor-application.service';

@Component({
  selector: 'app-admin-mentor-applications',
  templateUrl: './admin-mentor-applications.component.html',
  styleUrls: ['./admin-mentor-applications.component.scss']
})
export class AdminMentorApplicationsComponent implements OnInit {
  applications: any[] = [];
  loading = false;
  error = '';

  constructor(private appService: MentorApplicationService) {}

  ngOnInit() {
    this.fetchApplications();
  }

  fetchApplications() {
    this.loading = true;
    this.appService.getApplications().subscribe({
      next: res => {
        this.applications = res.data;
        this.loading = false;
      },
      error: err => {
        this.error = 'Failed to load applications';
        this.loading = false;
      }
    });
  }

  approve(id: string) {
    this.appService.approveApplication(id).subscribe(() => this.fetchApplications());
  }

  reject(id: string) {
    this.appService.rejectApplication(id).subscribe(() => this.fetchApplications());
  }
}
