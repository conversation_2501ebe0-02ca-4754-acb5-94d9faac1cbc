<div class="mentor-application-container">
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading application form...</p>
    </div>
  } @else {
    <mat-card class="application-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>school</mat-icon>
          Apply to Become a Mentor
        </mat-card-title>
        <mat-card-subtitle>
          Help others learn and grow in the streaming community
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="applicationForm" (ngSubmit)="onSubmit()">
          <mat-stepper orientation="vertical" #stepper>
            <!-- Step 1: Basic Information -->
            <mat-step [stepControl]="applicationForm.get('reason')!" label="Why do you want to be a mentor?">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Reason for applying</mat-label>
                <textarea 
                  matInput 
                  formControlName="reason" 
                  placeholder="Tell us why you want to become a mentor and what motivates you to help others..."
                  rows="4"
                  maxlength="500">
                </textarea>
                <mat-hint align="end">{{applicationForm.get('reason')?.value?.length || 0}}/500</mat-hint>
                @if (applicationForm.get('reason')?.invalid && applicationForm.get('reason')?.touched) {
                  <mat-error>{{getErrorMessage('reason')}}</mat-error>
                }
              </mat-form-field>

              <div class="step-actions">
                <button mat-raised-button color="primary" matStepperNext type="button">Next</button>
              </div>
            </mat-step>

            <!-- Step 2: Experience -->
            <mat-step [stepControl]="applicationForm.get('experience')!" label="Your experience">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Experience and qualifications</mat-label>
                <textarea 
                  matInput 
                  formControlName="experience" 
                  placeholder="Describe your experience with streaming, content creation, or helping others. Include any relevant skills, achievements, or background..."
                  rows="6"
                  maxlength="1000">
                </textarea>
                <mat-hint align="end">{{applicationForm.get('experience')?.value?.length || 0}}/1000</mat-hint>
                @if (applicationForm.get('experience')?.invalid && applicationForm.get('experience')?.touched) {
                  <mat-error>{{getErrorMessage('experience')}}</mat-error>
                }
              </mat-form-field>

              <div class="step-actions">
                <button mat-button matStepperPrevious type="button">Back</button>
                <button mat-raised-button color="primary" matStepperNext type="button">Next</button>
              </div>
            </mat-step>

            <!-- Step 3: Areas of Expertise -->
            <mat-step [stepControl]="applicationForm.get('categories')!" label="Areas of expertise">
              <div class="categories-section">
                <p class="section-description">
                  Select the areas where you can provide mentorship and guidance:
                </p>
                
                <div class="categories-grid">
                  @for (category of categories; track category) {
                    <mat-checkbox 
                      [checked]="isCategorySelected(category)"
                      (change)="onCategoryToggle(category)"
                      class="category-checkbox">
                      {{category}}
                    </mat-checkbox>
                  }
                </div>

                @if (applicationForm.get('categories')?.invalid && applicationForm.get('categories')?.touched) {
                  <mat-error class="categories-error">Please select at least one area of expertise</mat-error>
                }
              </div>

              <div class="step-actions">
                <button mat-button matStepperPrevious type="button">Back</button>
                <button mat-raised-button color="primary" matStepperNext type="button">Next</button>
              </div>
            </mat-step>

            <!-- Step 4: Profile Information -->
            <mat-step label="Profile information (optional)">
              <div class="profile-section">
                <p class="section-description">
                  Enhance your mentor profile with additional information:
                </p>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Bio</mat-label>
                  <textarea 
                    matInput 
                    formControlName="bio" 
                    placeholder="Tell others about yourself, your streaming journey, and what you're passionate about..."
                    rows="3"
                    maxlength="500">
                  </textarea>
                  <mat-hint align="end">{{applicationForm.get('bio')?.value?.length || 0}}/500</mat-hint>
                </mat-form-field>

                <div class="social-links" formGroupName="socialLinks">
                  <h4>Social Links</h4>
                  <div class="social-links-grid">
                    <mat-form-field appearance="outline">
                      <mat-label>Twitch</mat-label>
                      <input matInput formControlName="twitch" placeholder="https://twitch.tv/username">
                      <mat-icon matPrefix>live_tv</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>YouTube</mat-label>
                      <input matInput formControlName="youtube" placeholder="https://youtube.com/channel/...">
                      <mat-icon matPrefix>play_circle</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Twitter</mat-label>
                      <input matInput formControlName="twitter" placeholder="https://twitter.com/username">
                      <mat-icon matPrefix>alternate_email</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Instagram</mat-label>
                      <input matInput formControlName="instagram" placeholder="https://instagram.com/username">
                      <mat-icon matPrefix>camera_alt</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <div class="step-actions">
                <button mat-button matStepperPrevious type="button">Back</button>
                <button mat-raised-button color="primary" matStepperNext type="button">Next</button>
              </div>
            </mat-step>

            <!-- Step 5: Agreement and Submit -->
            <mat-step label="Review and submit">
              <div class="review-section">
                <h4>Review Your Application</h4>
                <p class="section-description">
                  Please review your information and agree to the mentor guidelines before submitting.
                </p>

                <div class="review-summary">
                  <div class="summary-item">
                    <strong>Areas of Expertise:</strong>
                    <div class="selected-categories">
                      @for (category of selectedCategories; track category) {
                        <mat-chip color="primary" selected>{{category}}</mat-chip>
                      }
                    </div>
                  </div>
                </div>

                <mat-checkbox formControlName="agreement" class="agreement-checkbox">
                  I agree to the mentor guidelines and commit to helping community members in a respectful and constructive manner
                </mat-checkbox>

                @if (applicationForm.get('agreement')?.invalid && applicationForm.get('agreement')?.touched) {
                  <mat-error class="agreement-error">You must agree to the mentor guidelines</mat-error>
                }
              </div>

              <div class="step-actions">
                <button mat-button matStepperPrevious type="button">Back</button>
                <button 
                  mat-raised-button 
                  color="primary" 
                  type="submit"
                  [disabled]="applicationForm.invalid || isSubmitting">
                  @if (isSubmitting) {
                    <mat-spinner diameter="20"></mat-spinner>
                    Submitting...
                  } @else {
                    <ng-container>
                      <mat-icon>send</mat-icon>
                      Submit Application
                    </ng-container>
                  }
                </button>
              </div>
            </mat-step>
          </mat-stepper>
        </form>
      </mat-card-content>

      <mat-card-actions align="end">
        <button mat-button (click)="onCancel()" [disabled]="isSubmitting">
          Cancel
        </button>
      </mat-card-actions>
    </mat-card>
  }
</div>
