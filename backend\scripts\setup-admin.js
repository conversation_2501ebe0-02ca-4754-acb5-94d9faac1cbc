#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to set up system administrators in the database
 * This script ensures that designated Discord users have admin privileges
 */

const mongoose = require('mongoose');
const User = require('../src/models/User');
const { ADMIN_DISCORD_IDS } = require('../src/middleware/security');
const logger = require('../src/config/logger');

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hypehive');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
};

/**
 * Setup admin users in the database
 */
const setupAdminUsers = async () => {
  try {
    console.log('Setting up system administrators...');
    
    for (const discordId of ADMIN_DISCORD_IDS) {
      console.log(`Processing Discord ID: ${discordId}`);
      
      // Check if user exists
      const existingUser = await User.findOne({ discordId });
      
      if (existingUser) {
        console.log(`User found: ${existingUser.discordUsername} (${existingUser.discordId})`);
        
        // Update user to admin if not already
        if (existingUser.role !== 'admin') {
          console.log(`Promoting ${existingUser.discordUsername} to admin...`);
          await User.findByIdAndUpdate(existingUser._id, {
            role: 'admin',
            isMentor: true,
            isActive: true,
            'mentorApplication.status': 'approved',
            'mentorApplication.reviewedAt': new Date(),
            'mentorApplication.reviewedBy': existingUser._id // Self-approved
          });
          console.log(`✅ ${existingUser.discordUsername} is now an admin`);
        } else {
          console.log(`✅ ${existingUser.discordUsername} is already an admin`);
        }
      } else {
        console.log(`⚠️  User with Discord ID ${discordId} not found in database.`);
        console.log(`   This user will be auto-promoted to admin when they first log in.`);
        
        // Create a placeholder admin user entry
        const placeholderUser = new User({
          discordId: discordId,
          discordUsername: 'captain.sexy', // Default username, will be updated on first login
          role: 'admin',
          isMentor: true,
          isActive: true,
          'mentorApplication.status': 'approved',
          'mentorApplication.reviewedAt': new Date(),
          proficiencies: []
        });
        
        try {
          await placeholderUser.save();
          console.log(`✅ Created placeholder admin user for Discord ID: ${discordId}`);
        } catch (error) {
          if (error.code === 11000) {
            console.log(`   Placeholder already exists for Discord ID: ${discordId}`);
          } else {
            console.error(`   Error creating placeholder user:`, error.message);
          }
        }
      }
    }
    
    console.log('\n🎉 Admin setup completed successfully!');
    
    // Display all admin users
    const adminUsers = await User.find({ role: 'admin' }).select('discordId discordUsername role isMentor isActive');
    console.log('\nCurrent Admin Users:');
    console.log('==================');
    adminUsers.forEach(user => {
      console.log(`• ${user.discordUsername} (${user.discordId}) - Active: ${user.isActive}`);
    });
    
  } catch (error) {
    console.error('Error setting up admin users:', error);
    process.exit(1);
  }
};

/**
 * Main execution function
 */
const main = async () => {
  try {
    await connectDB();
    await setupAdminUsers();
  } catch (error) {
    console.error('Script execution failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { setupAdminUsers };
