import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class MentorApplicationService {
  private baseUrl = '/api/mentors/applications';

  constructor(private http: HttpClient) {}

  getApplications(): Observable<any> {
    return this.http.get(this.baseUrl);
  }

  approveApplication(id: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/${id}/approve`, {});
  }

  rejectApplication(id: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/${id}/reject`, {});
  }
}
