import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChatListComponent } from './chat-list/chat-list.component';
import { ChatDetailComponent } from './chat-detail/chat-detail.component';
import { NewChatComponent } from './new-chat/new-chat.component';

const routes: Routes = [
  {
    path: '',
    component: ChatListComponent
  },
  {
    path: 'new',
    component: NewChatComponent
  },
  {
    path: ':id',
    component: ChatDetailComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChatRoutingModule { }
