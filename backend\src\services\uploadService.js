const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');

/**
 * Save uploaded file
 * @param {Object} file - Multer file object
 * @param {string} userId - User ID
 * @returns {Promise<string>} - File URL
 */
const saveFile = async (file, userId) => {
  try {
    // Create upload directory if it doesn't exist
    const uploadDir = path.join(process.env.UPLOAD_DIR, userId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // Generate unique filename
    const timestamp = Date.now();
    const filename = `${timestamp}-${file.originalname}`;
    const filepath = path.join(uploadDir, filename);
    
    // Save file
    fs.writeFileSync(filepath, file.buffer);
    
    // Return file URL
    const fileUrl = `/uploads/${userId}/${filename}`;
    
    return fileUrl;
  } catch (error) {
    logger.error(`Error saving file: ${error.message}`);
    throw new Error('Failed to save file');
  }
};

/**
 * Delete file
 * @param {string} fileUrl - File URL
 * @returns {Promise<boolean>} - Success status
 */
const deleteFile = async (fileUrl) => {
  try {
    // Extract file path from URL
    const filepath = path.join(process.cwd(), fileUrl);
    
    // Check if file exists
    if (!fs.existsSync(filepath)) {
      throw new Error('File not found');
    }
    
    // Delete file
    fs.unlinkSync(filepath);
    
    return true;
  } catch (error) {
    logger.error(`Error deleting file: ${error.message}`);
    throw new Error('Failed to delete file');
  }
};

module.exports = {
  saveFile,
  deleteFile
};
