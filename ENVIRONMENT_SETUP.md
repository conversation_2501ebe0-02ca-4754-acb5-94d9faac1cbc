# Environment Setup Guide

This guide explains how to set up the environment variables for the HypeHive application.

## Prerequisites

1. Discord Developer Application
2. MongoDB instance
3. Node.js (v18 or higher)
4. Python 3.8+ (for Discord bot)

## Discord Application Setup

1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name (e.g., "HypeHive")
3. Navigate to the "OAuth2" section in the sidebar
4. Add the following redirect URLs:
   - For development: `http://localhost:4200/auth/callback`
   - For production: `https://your-production-url.com/auth/callback`
5. Copy the "Client ID" and "Client Secret"
6. Navigate to the "Bot" section and create a bot
7. Copy the bot token

## Environment Configuration

### Backend (.env)

Copy `backend/.env.example` to `backend/.env` and fill in the values:

```bash
cp backend/.env.example backend/.env
```

Required variables:
- `DISCORD_CLIENT_ID`: Your Discord application client ID
- `DISCORD_CLIENT_SECRET`: Your Discord application client secret
- `DISCORD_BOT_TOKEN`: Your Discord bot token
- `JWT_SECRET`: A secure random string (minimum 32 characters)
- `MONGODB_URI`: Your MongoDB connection string

### Discord Bot (.env)

Copy `discord-bot/.env.example` to `discord-bot/.env` and fill in the values:

```bash
cp discord-bot/.env.example discord-bot/.env
```

Required variables:
- `DISCORD_BOT_TOKEN`: Your Discord bot token (same as backend)
- `DISCORD_GUILD_ID`: Your Discord server ID

### Frontend Environment

The frontend environment files are already configured for development and production.
For production deployment, update the URLs in `hype-hive/src/environments/environment.prod.ts`.

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique secrets for JWT_SECRET
- Rotate Discord tokens regularly
- Use environment-specific configurations for production

## Verification

After setting up the environment variables:

1. Start the backend: `cd backend && npm run dev`
2. Start the frontend: `cd hype-hive && npm start`
3. Start the Discord bot: `cd discord-bot && python hive_helper.py`

All services should start without errors if configured correctly.
