const http = require('http');
const connectDB = require('./config/database');
const logger = require('./config/logger');
const { initializeSocketServer } = require('./config/socket');
const app = require('./app');
const {
  handleUnhandledRejection,
  handleUncaughtException
} = require('./middleware/errorHandler');

// Handle uncaught exceptions
handleUncaughtException();

// Handle unhandled promise rejections
handleUnhandledRejection();

// Connect to MongoDB
connectDB();

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.io
const io = initializeSocketServer(server);

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  logger.info('Socket.io server initialized');
});
