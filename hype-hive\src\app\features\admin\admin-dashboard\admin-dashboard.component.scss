.admin-dashboard {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    h1 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }
  }

  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 16px 0;
      color: #666;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;

    .stat-card {
      mat-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        mat-icon {
          font-size: 32px;
          width: 32px;
          height: 32px;
          color: #666;
        }
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1976d2;
        margin: 16px 0 8px 0;
      }

      .stat-details {
        display: flex;
        flex-direction: column;
        gap: 4px;

        span {
          font-size: 0.875rem;
          color: #666;
        }
      }
    }
  }

  .quick-actions {
    margin-bottom: 32px;

    h2 {
      margin-bottom: 16px;
      color: #333;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .pending-applications {
    margin-bottom: 32px;

    .application-list {
      .application-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 16px;
          flex: 1;

          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
          }

          .user-details {
            h4 {
              margin: 0 0 4px 0;
              font-weight: 500;
            }

            p {
              margin: 0;
              color: #666;
              font-size: 0.875rem;

              &.bio {
                margin-top: 4px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        .application-actions {
          display: flex;
          gap: 8px;

          button {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
    }
  }

  .recent-users {
    .users-table {
      width: 100%;

      .user-cell {
        display: flex;
        align-items: center;
        gap: 12px;

        .avatar-small {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      mat-chip {
        font-size: 0.75rem;
        min-height: 24px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;

    .dashboard-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      button {
        align-self: flex-end;
      }
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .quick-actions .action-buttons {
      flex-direction: column;

      button {
        justify-content: center;
      }
    }

    .pending-applications .application-item {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .application-actions {
        justify-content: center;
      }
    }
  }
}