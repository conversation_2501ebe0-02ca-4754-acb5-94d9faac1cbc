import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatStepperModule } from '@angular/material/stepper';

import { ProfileService } from '../../../core/services/profile.service';
import { AuthService } from '../../../core/services/auth.service';
import { User, InstructionCategory } from '../../../core/models/user.model';

@Component({
  selector: 'app-mentor-application',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatSelectModule,
    MatChipsModule,
    MatStepperModule
  ],
  templateUrl: './mentor-application.component.html',
  styleUrl: './mentor-application.component.scss'
})
export class MentorApplicationComponent implements OnInit {
  applicationForm!: FormGroup;
  currentUser: User | null = null;
  isLoading = false;
  isSubmitting = false;
  categories = Object.values(InstructionCategory);
  selectedCategories: string[] = [];

  constructor(
    private fb: FormBuilder,
    private profileService: ProfileService,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadCurrentUser();
  }

  /**
   * Initialize the application form
   */
  private initializeForm(): void {
    this.applicationForm = this.fb.group({
      reason: ['', [Validators.required, Validators.minLength(50), Validators.maxLength(500)]],
      experience: ['', [Validators.required, Validators.minLength(50), Validators.maxLength(1000)]],
      categories: [[], [Validators.required, Validators.minLength(1)]],
      bio: ['', [Validators.maxLength(500)]],
      socialLinks: this.fb.group({
        twitch: [''],
        youtube: [''],
        twitter: [''],
        instagram: ['']
      }),
      agreement: [false, Validators.requiredTrue]
    });
  }

  /**
   * Load current user data
   */
  private loadCurrentUser(): void {
    this.isLoading = true;
    this.currentUser = this.authService.getCurrentUser();
    
    if (!this.currentUser) {
      this.router.navigate(['/auth/login']);
      return;
    }

    // Check if user already has a pending or approved application
    if (this.currentUser.mentorApplication?.status === 'pending') {
      this.snackBar.open('You already have a pending mentor application', 'Close', {
        duration: 5000,
        panelClass: ['warning-snackbar']
      });
      this.router.navigate(['/profile']);
      return;
    }

    if (this.currentUser.mentorApplication?.status === 'approved' || this.currentUser.isMentor) {
      this.snackBar.open('You are already a mentor', 'Close', {
        duration: 5000,
        panelClass: ['info-snackbar']
      });
      this.router.navigate(['/profile']);
      return;
    }

    // Pre-fill form with existing user data
    if (this.currentUser.bio) {
      this.applicationForm.patchValue({ bio: this.currentUser.bio });
    }

    if (this.currentUser.socialLinks) {
      this.applicationForm.get('socialLinks')?.patchValue(this.currentUser.socialLinks);
    }

    // Pre-select categories based on user's proficiencies
    if (this.currentUser.proficiencies?.length > 0) {
      const userCategories = this.currentUser.proficiencies
        .filter(p => p.isSelected)
        .map(p => p.category);
      this.selectedCategories = [...new Set(userCategories)];
      this.applicationForm.patchValue({ categories: this.selectedCategories });
    }

    this.isLoading = false;
  }

  /**
   * Handle category selection
   */
  onCategoryToggle(category: string): void {
    const index = this.selectedCategories.indexOf(category);
    if (index > -1) {
      this.selectedCategories.splice(index, 1);
    } else {
      this.selectedCategories.push(category);
    }
    this.applicationForm.patchValue({ categories: this.selectedCategories });
  }

  /**
   * Check if category is selected
   */
  isCategorySelected(category: string): boolean {
    return this.selectedCategories.includes(category);
  }

  /**
   * Submit the mentor application
   */
  onSubmit(): void {
    if (this.applicationForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isSubmitting = true;
    
    // First update user profile with bio and social links if provided
    const profileData = {
      bio: this.applicationForm.value.bio,
      socialLinks: this.applicationForm.value.socialLinks
    };

    // Remove empty social links
    Object.keys(profileData.socialLinks).forEach(key => {
      if (!profileData.socialLinks[key]) {
        delete profileData.socialLinks[key];
      }
    });

    // Update profile first, then submit application
    this.profileService.updateUserProfile(this.currentUser!.id, profileData).subscribe({
      next: () => {
        // Now submit the mentor application
        this.submitApplication();
      },
      error: (error) => {
        console.error('Error updating profile:', error);
        // Continue with application submission even if profile update fails
        this.submitApplication();
      }
    });
  }

  /**
   * Submit the mentor application
   */
  private submitApplication(): void {
    this.profileService.applyForMentor().subscribe({
      next: (updatedUser) => {
        this.isSubmitting = false;
        this.snackBar.open('Mentor application submitted successfully!', 'Close', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
        
        // Update current user in auth service
        this.authService.updateCurrentUser(updatedUser);
        
        // Navigate to profile to show application status
        this.router.navigate(['/profile']);
      },
      error: (error) => {
        this.isSubmitting = false;
        const errorMessage = error.message || 'Failed to submit mentor application';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Mark all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.applicationForm.controls).forEach(key => {
      const control = this.applicationForm.get(key);
      control?.markAsTouched();
      
      if (control instanceof FormGroup) {
        Object.keys(control.controls).forEach(nestedKey => {
          control.get(nestedKey)?.markAsTouched();
        });
      }
    });
  }

  /**
   * Get form field error message
   */
  getErrorMessage(fieldName: string): string {
    const field = this.applicationForm.get(fieldName);
    
    if (field?.hasError('required')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
    }
    
    if (field?.hasError('minlength')) {
      const requiredLength = field.errors?.['minlength']?.requiredLength;
      return `Minimum ${requiredLength} characters required`;
    }
    
    if (field?.hasError('maxlength')) {
      const requiredLength = field.errors?.['maxlength']?.requiredLength;
      return `Maximum ${requiredLength} characters allowed`;
    }
    
    return '';
  }

  /**
   * Cancel application and go back
   */
  onCancel(): void {
    this.router.navigate(['/profile']);
  }
}
