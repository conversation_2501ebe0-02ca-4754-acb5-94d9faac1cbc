#!/usr/bin/env node

/**
 * Coverage report generator and analyzer for HypeHive backend
 * Generates detailed coverage reports and provides insights
 */

const fs = require('fs');
const path = require('path');

const COVERAGE_DIR = path.join(__dirname, '../coverage');
const COVERAGE_SUMMARY_FILE = path.join(COVERAGE_DIR, 'coverage-summary.json');

function formatPercentage(value) {
  const percentage = parseFloat(value);
  if (percentage >= 90) return `🟢 ${percentage.toFixed(1)}%`;
  if (percentage >= 75) return `🟡 ${percentage.toFixed(1)}%`;
  if (percentage >= 60) return `🟠 ${percentage.toFixed(1)}%`;
  return `🔴 ${percentage.toFixed(1)}%`;
}

function generateCoverageReport() {
  if (!fs.existsSync(COVERAGE_SUMMARY_FILE)) {
    console.error('❌ Coverage summary file not found. Run tests with coverage first:');
    console.error('   npm run test:coverage');
    process.exit(1);
  }

  try {
    const coverageData = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_FILE, 'utf8'));
    
    console.log('\n📊 HypeHive Backend Test Coverage Report\n');
    console.log('=' .repeat(60));
    
    // Overall coverage
    const total = coverageData.total;
    console.log('\n🎯 Overall Coverage:');
    console.log(`   Lines:      ${formatPercentage(total.lines.pct)}`);
    console.log(`   Functions:  ${formatPercentage(total.functions.pct)}`);
    console.log(`   Branches:   ${formatPercentage(total.branches.pct)}`);
    console.log(`   Statements: ${formatPercentage(total.statements.pct)}`);
    
    // File-by-file breakdown
    console.log('\n📁 File Coverage Breakdown:');
    console.log('-'.repeat(60));
    
    const files = Object.entries(coverageData)
      .filter(([key]) => key !== 'total')
      .sort(([a], [b]) => a.localeCompare(b));
    
    files.forEach(([filePath, coverage]) => {
      const relativePath = filePath.replace(process.cwd(), '').replace(/\\/g, '/');
      console.log(`\n📄 ${relativePath}`);
      console.log(`   Lines:      ${formatPercentage(coverage.lines.pct)}`);
      console.log(`   Functions:  ${formatPercentage(coverage.functions.pct)}`);
      console.log(`   Branches:   ${formatPercentage(coverage.branches.pct)}`);
      console.log(`   Statements: ${formatPercentage(coverage.statements.pct)}`);
    });
    
    // Coverage insights
    console.log('\n🔍 Coverage Insights:');
    console.log('-'.repeat(60));
    
    const lowCoverageFiles = files.filter(([, coverage]) => 
      coverage.lines.pct < 75 || 
      coverage.functions.pct < 75 || 
      coverage.branches.pct < 75 || 
      coverage.statements.pct < 75
    );
    
    if (lowCoverageFiles.length > 0) {
      console.log('\n⚠️  Files needing attention (< 75% coverage):');
      lowCoverageFiles.forEach(([filePath, coverage]) => {
        const relativePath = filePath.replace(process.cwd(), '').replace(/\\/g, '/');
        const minCoverage = Math.min(
          coverage.lines.pct,
          coverage.functions.pct,
          coverage.branches.pct,
          coverage.statements.pct
        );
        console.log(`   ${relativePath} (${minCoverage.toFixed(1)}%)`);
      });
    } else {
      console.log('\n✅ All files meet the minimum coverage threshold!');
    }
    
    // Uncovered lines
    const uncoveredFiles = files.filter(([, coverage]) => 
      coverage.lines.total > coverage.lines.covered
    );
    
    if (uncoveredFiles.length > 0) {
      console.log('\n📋 Uncovered Lines Summary:');
      uncoveredFiles.forEach(([filePath, coverage]) => {
        const relativePath = filePath.replace(process.cwd(), '').replace(/\\/g, '/');
        const uncoveredCount = coverage.lines.total - coverage.lines.covered;
        console.log(`   ${relativePath}: ${uncoveredCount} uncovered lines`);
      });
    }
    
    // Coverage trends (if previous report exists)
    const previousReportPath = path.join(COVERAGE_DIR, 'previous-coverage.json');
    if (fs.existsSync(previousReportPath)) {
      try {
        const previousData = JSON.parse(fs.readFileSync(previousReportPath, 'utf8'));
        const currentTotal = total.lines.pct;
        const previousTotal = previousData.total.lines.pct;
        const trend = currentTotal - previousTotal;
        
        console.log('\n📈 Coverage Trend:');
        if (trend > 0) {
          console.log(`   🟢 Coverage increased by ${trend.toFixed(1)}%`);
        } else if (trend < 0) {
          console.log(`   🔴 Coverage decreased by ${Math.abs(trend).toFixed(1)}%`);
        } else {
          console.log(`   ➡️  Coverage remained the same`);
        }
      } catch (error) {
        console.log('\n📈 Coverage Trend: Unable to compare with previous report');
      }
    }
    
    // Save current report as previous for next comparison
    fs.writeFileSync(previousReportPath, JSON.stringify(coverageData, null, 2));
    
    console.log('\n🌐 Detailed HTML Report:');
    console.log(`   Open: ${path.join(COVERAGE_DIR, 'lcov-report/index.html')}`);
    
    console.log('\n' + '='.repeat(60));
    
    // Exit with error code if coverage is below threshold
    const meetsThreshold = total.lines.pct >= 80 && 
                          total.functions.pct >= 80 && 
                          total.branches.pct >= 75 && 
                          total.statements.pct >= 80;
    
    if (!meetsThreshold) {
      console.log('\n❌ Coverage below minimum thresholds');
      process.exit(1);
    } else {
      console.log('\n✅ Coverage meets all minimum thresholds');
    }
    
  } catch (error) {
    console.error('❌ Error reading coverage data:', error.message);
    process.exit(1);
  }
}

function showHelp() {
  console.log('\n📊 Coverage Report Generator\n');
  console.log('Usage: npm run coverage:report\n');
  console.log('This script analyzes the test coverage data and provides:');
  console.log('  • Overall coverage statistics');
  console.log('  • File-by-file breakdown');
  console.log('  • Coverage insights and recommendations');
  console.log('  • Trend analysis (compared to previous run)');
  console.log('  • Links to detailed HTML reports\n');
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  generateCoverageReport();
}

if (require.main === module) {
  main();
}

module.exports = { generateCoverageReport, formatPercentage };
