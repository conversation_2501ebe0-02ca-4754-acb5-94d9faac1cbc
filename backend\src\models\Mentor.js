const mongoose = require('mongoose');

const MentorSchema = new mongoose.Schema({
  discord_id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  proficiencies: { type: [String], default: [] }
}, { timestamps: true });

// Add indexes for performance (discord_id already indexed via unique: true)
MentorSchema.index({ name: 'text' }); // Text search on name
MentorSchema.index({ proficiencies: 1 }); // Index for proficiency queries
MentorSchema.index({ createdAt: -1 }); // Index for sorting by creation date

module.exports = mongoose.model('Mentor', MentorSchema);
