<div class="home-container">
  <section class="hero-section">
    <div class="hero-content">
      <h1>Level Up Your Twitch Streaming Skills</h1>
      <p>Connect with experienced mentors, access quality educational content, and grow your streaming career</p>
      <div class="hero-buttons">
        <button mat-raised-button color="primary" routerLink="/auth/login" *ngIf="!isAuthenticated">
          Get Started
        </button>
        <button mat-raised-button color="accent" routerLink="/mentors">
          Find a Mentor
        </button>
      </div>
    </div>
  </section>

  <section class="main-content">
    <app-recommended-content></app-recommended-content>

    <div class="categories-section">
      <div class="section-header">
        <h2>Browse by Category</h2>
      </div>

      <div class="categories-grid">
        <div class="category-card" *ngFor="let category of categories" [routerLink]="['/content']" [queryParams]="{category: category}">
          <div class="category-icon">
            <mat-icon>{{ getCategoryIcon(category) }}</mat-icon>
          </div>
          <h3>{{ category }}</h3>
        </div>
      </div>
    </div>

    <div class="mentors-section" *ngIf="recommendedMentors.length > 0">
      <div class="section-header">
        <h2>Featured Mentors</h2>
        <a routerLink="/mentors" class="view-all">View All</a>
      </div>

      <div class="mentors-grid">
        <app-mentor-card *ngFor="let mentor of recommendedMentors" [mentor]="mentor"></app-mentor-card>
      </div>
    </div>
  </section>
</div>
