# HypeHive Deployment Guide

## Overview

HypeHive is a comprehensive education platform for Twitch streamers with the following components:
- **Backend API** (Node.js/Express)
- **Frontend Web App** (Angular)
- **Discord Bot** (Python)

## Prerequisites

### System Requirements
- Node.js 18+ 
- Python 3.8+
- MongoDB 4.4+
- Git
- PM2 (for production process management)

### Required Accounts & Services
- Discord Developer Application
- MongoDB Atlas (or local MongoDB)
- Domain with SSL certificate
- Web server (Nginx recommended)

## Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/hype-hive.git
cd hype-hive
```

### 2. Backend Setup
```bash
cd backend
npm install

# Copy environment template
cp .env.development .env

# Edit .env with your configuration
nano .env
```

### 3. Frontend Setup
```bash
cd ../hype-hive
npm install

# Update environment configuration
nano src/environments/environment.prod.ts
```

### 4. Discord Bot Setup
```bash
cd ../discord-bot
pip install -r requirements.txt

# Configure bot settings
nano config.py
```

## Configuration

### Backend Environment Variables (.env)
```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/hype-hive

# JWT Security (Generate a strong 32+ character secret)
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Discord OAuth
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=https://yourdomain.com/api/auth/discord/callback
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_ADMIN_GUILD_ID=your_discord_server_id
DISCORD_ADMIN_ROLE_ID=your_admin_role_id

# URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_API_URL=https://yourdomain.com/api
CORS_ORIGINS=https://yourdomain.com

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Security
COOKIE_DOMAIN=.yourdomain.com
TRUSTED_IPS=your_server_ip,your_office_ip

# Logging
LOG_LEVEL=info
```

### Frontend Environment (environment.prod.ts)
```typescript
export const environment = {
  production: true,
  discord: {
    clientId: 'your_discord_client_id',
    redirectUri: 'https://yourdomain.com/api/auth/discord/callback',
    apiEndpoint: 'https://discord.com/api/v10'
  },
  apiUrl: 'https://yourdomain.com/api',
  monitoring: {
    errorTrackingEnabled: true,
    analyticsEnabled: true
  }
};
```

## Build Process

### Backend Build
```bash
cd backend
npm run build  # If you have a build script
npm test       # Run tests
```

### Frontend Build
```bash
cd hype-hive
ng build --configuration=production --build-optimizer --aot
```

### Discord Bot Setup
```bash
cd discord-bot
python -m pytest  # Run tests if available
```

## Deployment Options

### Option 1: Traditional Server Deployment

#### 1. Server Setup (Ubuntu/Debian)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python
sudo apt install python3 python3-pip -y

# Install MongoDB (or use MongoDB Atlas)
sudo apt install mongodb -y

# Install PM2
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

#### 2. Deploy Application
```bash
# Clone repository
git clone https://github.com/yourusername/hype-hive.git
cd hype-hive

# Backend deployment
cd backend
npm install --production
cp .env.production .env
# Edit .env with production values

# Frontend deployment
cd ../hype-hive
npm install
ng build --configuration=production
sudo cp -r dist/hype-hive/* /var/www/html/

# Discord bot deployment
cd ../discord-bot
pip install -r requirements.txt
```

#### 3. Process Management with PM2
```bash
# Backend
cd backend
pm2 start src/index.js --name "hype-hive-backend"

# Discord bot
cd ../discord-bot
pm2 start bot.py --name "hype-hive-bot" --interpreter python3

# Save PM2 configuration
pm2 save
pm2 startup
```

#### 4. Nginx Configuration
```nginx
# /etc/nginx/sites-available/hype-hive
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Frontend
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # File uploads
    location /uploads/ {
        root /path/to/hype-hive/backend;
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

### Option 2: Docker Deployment

#### 1. Backend Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3000

CMD ["node", "src/index.js"]
```

#### 2. Frontend Dockerfile
```dockerfile
# hype-hive/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN ng build --configuration=production

FROM nginx:alpine
COPY --from=builder /app/dist/hype-hive /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
```

#### 3. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - ./backend/.env
    depends_on:
      - mongodb

  frontend:
    build: ./hype-hive
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend

  discord-bot:
    build: ./discord-bot
    env_file:
      - ./discord-bot/.env
    depends_on:
      - backend

  mongodb:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

volumes:
  mongodb_data:
```

## Monitoring & Maintenance

### Health Checks
- Backend: `https://yourdomain.com/api/health`
- Performance: `https://yourdomain.com/api/performance`
- Cache Stats: `https://yourdomain.com/api/cache/stats`

### Log Management
```bash
# PM2 logs
pm2 logs hype-hive-backend
pm2 logs hype-hive-bot

# Application logs
tail -f backend/logs/backend.log
tail -f discord-bot/logs/bot.log
```

### Backup Strategy
```bash
# Database backup
mongodump --uri="mongodb://localhost:27017/hype-hive" --out=/backups/$(date +%Y%m%d)

# File uploads backup
tar -czf /backups/uploads-$(date +%Y%m%d).tar.gz backend/uploads/
```

### Updates & Maintenance
```bash
# Update application
git pull origin main

# Backend updates
cd backend
npm install
pm2 restart hype-hive-backend

# Frontend updates
cd ../hype-hive
npm install
ng build --configuration=production
sudo cp -r dist/hype-hive/* /var/www/html/

# Discord bot updates
cd ../discord-bot
pip install -r requirements.txt
pm2 restart hype-hive-bot
```

## Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Environment variables secured (not in version control)
- [ ] Database access restricted
- [ ] Firewall configured (only necessary ports open)
- [ ] Regular security updates applied
- [ ] Rate limiting enabled
- [ ] CORS properly configured
- [ ] File upload restrictions in place
- [ ] Monitoring and alerting configured

## Troubleshooting

### Common Issues

1. **Backend won't start**
   - Check environment variables
   - Verify MongoDB connection
   - Check port availability

2. **Frontend build fails**
   - Clear node_modules and reinstall
   - Check Angular version compatibility
   - Verify environment configuration

3. **Discord bot connection issues**
   - Verify bot token
   - Check Discord API status
   - Review bot permissions

### Performance Issues
- Monitor `/api/performance` endpoint
- Check cache hit rates at `/api/cache/stats`
- Review PM2 process monitoring
- Analyze Nginx access logs

## Support

For deployment issues:
1. Check application logs
2. Review this deployment guide
3. Run security and performance audits
4. Contact development team with specific error messages

---

**Last Updated**: 2025-01-28
**Version**: 1.0
