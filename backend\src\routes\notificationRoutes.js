const express = require('express');
const {
  getUserNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification
} = require('../controllers/notificationController');
const { protect } = require('../middleware/auth');
const { commonValidations, handleValidationErrors } = require('../middleware/validation');

const router = express.Router();

// All notification routes require authentication
router.use(protect);

// GET /api/notifications - Get all notifications for the current user
router.get('/', [commonValidations.page, commonValidations.limit, handleValidationErrors], getUserNotifications);

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', markAllAsRead);

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', [commonValidations.mongoId, handleValidationErrors], markAsRead);

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id', [commonValidations.mongoId, handleValidationErrors], deleteNotification);

module.exports = router;
