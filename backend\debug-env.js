require('dotenv').config();

console.log('=== Environment Variables Debug ===');
console.log('DISCORD_CLIENT_ID:', process.env.DISCORD_CLIENT_ID);
console.log('DISCORD_CLIENT_SECRET:', process.env.DISCORD_CLIENT_SECRET ? '[SET]' : '[NOT SET]');
console.log('DISCORD_REDIRECT_URI:', process.env.DISCORD_REDIRECT_URI);
console.log('DISCORD_API_ENDPOINT:', process.env.DISCORD_API_ENDPOINT);

console.log('\n=== Discord Config Object ===');
const discordConfig = require('./src/config/discord');
console.log('clientId:', discordConfig.clientId);
console.log('clientSecret:', discordConfig.clientSecret ? '[SET]' : '[NOT SET]');
console.log('redirectUri:', discordConfig.redirectUri);
console.log('apiEndpoint:', discordConfig.apiEndpoint);
console.log('tokenURL:', discordConfig.tokenURL);
console.log('userURL:', discordConfig.userURL);

console.log('\n=== Type Checks ===');
console.log('clientId type:', typeof discordConfig.clientId);
console.log('clientId length:', discordConfig.clientId ? discordConfig.clientId.length : 'undefined');
console.log('Is clientId a snowflake?', /^\d{17,19}$/.test(discordConfig.clientId));
