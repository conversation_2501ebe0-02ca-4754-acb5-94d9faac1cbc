import { RenderMode, ServerRoute } from '@angular/ssr';
import { AuthGuard } from './core/guards/auth.guard';
import { AdminGuard } from './core/guards/admin.guard';

export function getPrerenderParams(route: string) {
  // For demo, return empty array for dynamic routes
  // In production, fetch IDs from DB or API
  if (route === 'mentors/:id') {
    return [];
  }
  if (route === 'content/:id') {
    return [];
  }
  if (route === 'chat/:id') {
    return [];
  }
  return undefined;
}

export const serverRoutes: ServerRoute[] = [
  // Home routes - Client side
  {
    path: '',
    renderMode: RenderMode.Client
  },
  // Mentors routes
  {
    path: 'mentors',
    renderMode: RenderMode.Client
  },
  {
    path: 'mentors/apply',
    renderMode: RenderMode.Client
  },
  {
    path: 'mentors/:id',
    renderMode: RenderMode.Server
  },
  // Content routes
  {
    path: 'content',
    renderMode: RenderMode.Client
  },
  {
    path: 'content/:id',
    renderMode: RenderMode.Server
  },
  // Chat routes
  {
    path: 'chat',
    renderMode: RenderMode.Client
  },
  {
    path: 'chat/:id',
    renderMode: RenderMode.Server
  },
  // Auth routes
  {
    path: 'auth',
    renderMode: RenderMode.Client
  },
  // Profile routes
  {
    path: 'profile',
    renderMode: RenderMode.Client
  },
  // Settings route
  {
    path: 'settings',
    renderMode: RenderMode.Client
  },
  // Admin routes
  {
    path: 'admin',
    renderMode: RenderMode.Client
  },
  // Fallback
  {
    path: '**',
    renderMode: RenderMode.Client
  }
];
