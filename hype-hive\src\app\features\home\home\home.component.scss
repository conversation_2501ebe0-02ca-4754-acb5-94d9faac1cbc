.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.hero-section {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border-radius: 16px;
  padding: 4rem 2rem;
  margin: 2rem 0;
  text-align: center;
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--text-inverse), var(--orange-200));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      opacity: 0.95;
      line-height: 1.6;
    }

    .hero-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;

      button {
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-md);

        &:first-child {
          background: var(--orange);
          color: var(--text-inverse);
          border: none;

          &:hover {
            background: var(--orange-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
          }
        }

        &:last-child {
          background: rgba(255, 255, 255, 0.15);
          color: var(--text-inverse);
          border: 2px solid rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 3rem 1rem;

    .hero-content h1 {
      font-size: 2.5rem;
    }

    .hero-buttons {
      flex-direction: column;
      align-items: center;

      button {
        width: 100%;
        max-width: 300px;
      }
    }
  }
}

.main-content {
  padding: 2rem 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin: 0;
    font-weight: 600;
  }

  .view-all {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-blue-50);
      color: var(--primary-blue-dark);
      transform: translateX(2px);
    }
  }
}

.categories-section {
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: var(--bg-primary);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);

  &:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue-200);
  }

  .category-icon {
    background: var(--gradient-blue-purple);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem;
    box-shadow: var(--shadow-sm);

    mat-icon {
      color: var(--text-inverse);
      font-size: 30px;
      height: 30px;
      width: 30px;
    }
  }

  h3 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  &:nth-child(3n+1) .category-icon {
    background: var(--gradient-blue-purple);
  }

  &:nth-child(3n+2) .category-icon {
    background: var(--gradient-purple-orange);
  }

  &:nth-child(3n+3) .category-icon {
    background: linear-gradient(135deg, var(--orange) 0%, var(--primary-blue) 100%);
  }
}

.mentors-section {
  margin-bottom: 3rem;
}

.mentors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1rem;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }

      .hero-buttons {
        flex-direction: column;

        button {
          width: 100%;
        }
      }
    }
  }

  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .mentors-grid {
    grid-template-columns: 1fr;
  }
}