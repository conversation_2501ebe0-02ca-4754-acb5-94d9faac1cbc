import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminDashboardComponent } from './admin-dashboard/admin-dashboard.component';
import { ContentManagementComponent } from './content-management/content-management.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { MentorApplicationsComponent } from './mentor-applications/mentor-applications.component';

const routes: Routes = [
  {
    path: '',
    component: AdminDashboardComponent
  },
  {
    path: 'users',
    component: UserManagementComponent
  },
  {
    path: 'content',
    component: ContentManagementComponent
  },
  {
    path: 'mentor-applications',
    component: MentorApplicationsComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule { }
