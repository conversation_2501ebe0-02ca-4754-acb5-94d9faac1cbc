import { Component, Input } from '@angular/core';
import { ChatMessage } from '../../../core/models/chat.model';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-chat-message',
  templateUrl: './chat-message.component.html',
  styleUrls: ['./chat-message.component.scss'],
  standalone: false
})
export class ChatMessageComponent {
  @Input() message!: ChatMessage;
  @Input() isOwnMessage = false;

  /**
   * Get sender username
   * @returns Sender username
   */
  getSenderName(): string {
    if (!this.message.sender) {
      return 'Unknown';
    }
    
    if (typeof this.message.sender === 'string') {
      return 'User';
    }
    
    return this.message.sender.discordUsername || 'User';
  }

  /**
   * Get sender avatar
   * @returns Avatar URL
   */
  getSenderAvatar(): string {
    if (!this.message.sender) {
      return '/helplogo.png';
    }
    
    if (typeof this.message.sender === 'string') {
      return '/helplogo.png';
    }
    
    return this.message.sender.discordAvatar || '/helplogo.png';
  }

  /**
   * Format message timestamp
   * @returns Formatted time string
   */
  formatTime(): string {
    if (!this.message.createdAt) {
      return '';
    }
    
    const date = new Date(this.message.createdAt);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * Format message date
   * @returns Formatted date string
   */
  formatDate(): string {
    if (!this.message.createdAt) {
      return '';
    }
    
    const date = new Date(this.message.createdAt);
    return date.toLocaleDateString();
  }

  /**
   * Check if message was sent today
   * @returns True if message was sent today
   */
  isSentToday(): boolean {
    if (!this.message.createdAt) {
      return false;
    }
    
    const today = new Date();
    const messageDate = new Date(this.message.createdAt);
    
    return (
      today.getDate() === messageDate.getDate() &&
      today.getMonth() === messageDate.getMonth() &&
      today.getFullYear() === messageDate.getFullYear()
    );
  }
}
