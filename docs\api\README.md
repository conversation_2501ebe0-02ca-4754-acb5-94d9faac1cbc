# HypeHive API Documentation

## Overview

The HypeHive API provides programmatic access to the HypeHive platform, allowing developers to integrate with and extend the functionality of the Twitch streaming education platform.

## Base URL

All API requests should be made to the following base URL:

```
http://localhost:3000/api
```

For production environments, use your deployed API URL.

## Authentication

Most API endpoints require authentication. HypeHive uses JSON Web Tokens (JWT) for authentication.

### Obtaining a Token

To obtain a token, users must authenticate through Discord OAuth. The authentication flow is as follows:

1. Redirect users to Discord's OAuth authorization URL
2. User authorizes the application on Discord
3. Discord redirects back to your callback URL with a code
4. Exchange the code for a Discord access token
5. Use the Discord token to authenticate with HypeHive API
6. Receive a JWT token from HypeHive

### Using the Token

Include the JWT token in the Authorization header of your requests:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Response Format

All API responses are returned in JSON format. A typical response has the following structure:

```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Operation successful"
}
```

### Error Responses

Error responses follow a similar structure:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information (optional)"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse. Current limits are:

- 100 requests per minute per IP address
- 1000 requests per hour per user

When rate limited, the API will respond with a 429 Too Many Requests status code.

## API Versioning

The current API version is v1. The version is included in the base URL.

## Available Endpoints

The API is organized around the following resources:

- [Authentication](./auth.md) - User authentication and session management
- [Users](./users.md) - User profiles and mentor management
- [Content](./content.md) - Educational content and interactions
- [Upload](./upload.md) - File upload functionality
- [Chat](./chat.md) - Real-time messaging between users
- [Notifications](./notifications.md) - User notifications
- [Real-time Features](./realtime.md) - Comprehensive guide to all real-time functionality

## Pagination

List endpoints support pagination through the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)

Paginated responses include pagination metadata:

```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

## Filtering and Sorting

Many list endpoints support filtering and sorting through query parameters. Refer to the specific endpoint documentation for available filters and sort options.

## SDK and Client Libraries

Official client libraries are currently in development. Check back for updates.

## Support

For API support, please contact the HypeHive development team or open an issue on the GitHub repository.
