const express = require('express');
const {
  getUserByDiscordId,
  updateUserProficiencies,
  getMentorsForBot,
  syncUserFromDiscord,
  getBotHealth,
  approveMentor,
  denyMentor
} = require('../controllers/discordBotController');

const router = express.Router();

// Health check for Discord bot
router.get('/health', getBotHealth);

// Get user by Discord ID
router.get('/user/:discordId', getUserByDiscordId);

// Get all mentors (formatted for Discord bot)
router.get('/mentors', getMentorsForBot);

// Sync user data from Discord (create/update user)
router.post('/sync-user', syncUserFromDiscord);

// Update user proficiencies
router.put('/user/:discordId/proficiencies', updateUserProficiencies);

// Approve mentor request from Discord bot
router.post('/approve-mentor', approveMentor);

// Deny mentor request from Discord bot
router.post('/deny-mentor', denyMentor);

module.exports = router;
