require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
const routes = require('./routes');
const logger = require('./config/logger');
const {
  helmetConfig,
  mongoSanitizeConfig,
  sanitizeInput,
  securityLogger,
  generalLimiter
} = require('./middleware/security');
const { staticCacheMiddleware } = require('./middleware/cache');
const { performanceMiddleware } = require('./middleware/performance');
const {
  globalErrorHandler,
  notFound
} = require('./middleware/errorHandler');

// Create Express app
const app = express();

app.set('trust proxy', 1);

// Create uploads directory if it doesn't exist (only in non-test environment)
if (process.env.NODE_ENV !== 'test') {
  const uploadsDir = path.join(process.cwd(), process.env.UPLOAD_DIR || 'uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  // Create logs directory if it doesn't exist
  const logsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Serve static files from uploads directory with caching
  app.use('/uploads', staticCacheMiddleware(86400), express.static(uploadsDir)); // 24 hours cache

  // Serve static files from public directory (for admin dashboards)
  const publicDir = path.join(process.cwd(), 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  app.use('/admin-dashboard', staticCacheMiddleware(3600), express.static(publicDir)); // 1 hour cache
}

// Performance middleware
app.use(performanceMiddleware);
app.use(compression({
  filter: (req, res) => {
    // Don't compress responses if the request includes a cache-control: no-transform directive
    if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
      return false;
    }
    // Use compression filter function
    return compression.filter(req, res);
  },
  level: 6, // Compression level (1-9, 6 is default)
  threshold: 1024 // Only compress responses larger than 1KB
}));

// Security middleware
app.use(helmetConfig);
app.use(mongoSanitizeConfig);
app.use(generalLimiter);
app.use(securityLogger);
app.use(sanitizeInput);

// Enhanced CORS configuration for tunnel and local development
const corsOriginsEnv = process.env.CORS_ORIGINS;
const allowedOrigins = (corsOriginsEnv
  ? corsOriginsEnv.split(',').map(origin => origin.trim())
  : [
      'http://localhost:4200',
      'http://**********:4200', // Local Angular dev host
      'https://hypehive.linksynk.info',
      process.env.FRONTEND_URL
    ]
).filter(Boolean);

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // Log unauthorized origins for debugging
    logger.warn(`CORS blocked origin: ${origin}`);
    return callback(new Error('Not allowed by CORS'), false);
  },
  credentials: true, // Allow cookies to be sent
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Only use morgan in non-test environment
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('dev'));
}

// Root-level health check (for load balancers and monitoring tools)
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'HypeHive API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API routes
app.use('/api', routes);

// 404 handler - must come before global error handler
app.use(notFound);

// Global error handling middleware
app.use(globalErrorHandler);

module.exports = app;
