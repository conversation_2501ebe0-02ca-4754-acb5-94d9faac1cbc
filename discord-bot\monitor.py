#!/usr/bin/env python3
"""
Discord Bot Monitoring Script
Monitors bot health and provides alerts for issues
"""

import os
import time
import logging
import requests
import json
from datetime import datetime, timedelta
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotMonitor:
    """Monitor Discord bot health and performance"""
    
    def __init__(self):
        self.backend_url = os.getenv('BACKEND_API_URL', 'http://localhost:3000/api')
        self.check_interval = 300  # 5 minutes
        self.log_file = 'discord_bot.log'
        self.error_threshold = 10  # Alert if more than 10 errors in check period
        self.last_check_time = None
        
    def check_log_file(self) -> dict:
        """Check bot log file for errors and warnings"""
        if not Path(self.log_file).exists():
            return {"status": "warning", "message": "Log file not found"}
        
        try:
            # Read last 100 lines of log file
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-100:] if len(lines) > 100 else lines
            
            # Count errors and warnings in recent logs
            error_count = 0
            warning_count = 0
            last_activity = None
            
            for line in recent_lines:
                if 'ERROR' in line:
                    error_count += 1
                elif 'WARNING' in line:
                    warning_count += 1
                
                # Extract timestamp for last activity
                if line.strip():
                    try:
                        timestamp_str = line.split(' - ')[0]
                        last_activity = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                    except:
                        pass
            
            # Check if bot has been active recently (within last 10 minutes)
            if last_activity:
                time_since_activity = datetime.now() - last_activity
                if time_since_activity > timedelta(minutes=10):
                    return {
                        "status": "error",
                        "message": f"No recent activity (last: {time_since_activity})",
                        "error_count": error_count,
                        "warning_count": warning_count
                    }
            
            if error_count > self.error_threshold:
                return {
                    "status": "error",
                    "message": f"High error count: {error_count}",
                    "error_count": error_count,
                    "warning_count": warning_count
                }
            elif error_count > 0 or warning_count > 5:
                return {
                    "status": "warning",
                    "message": f"Errors: {error_count}, Warnings: {warning_count}",
                    "error_count": error_count,
                    "warning_count": warning_count
                }
            else:
                return {
                    "status": "healthy",
                    "message": "No recent errors",
                    "error_count": error_count,
                    "warning_count": warning_count
                }
                
        except Exception as e:
            logger.error(f"Error checking log file: {e}")
            return {"status": "error", "message": f"Failed to check logs: {e}"}
    
    def check_backend_connectivity(self) -> dict:
        """Check if backend API is accessible"""
        try:
            response = requests.get(f"{self.backend_url}/users/mentors", timeout=10)
            if response.status_code == 200:
                return {"status": "healthy", "message": "Backend API accessible"}
            else:
                return {
                    "status": "warning",
                    "message": f"Backend returned status {response.status_code}"
                }
        except requests.exceptions.Timeout:
            return {"status": "error", "message": "Backend API timeout"}
        except requests.exceptions.ConnectionError:
            return {"status": "error", "message": "Cannot connect to backend API"}
        except Exception as e:
            return {"status": "error", "message": f"Backend check failed: {e}"}
    
    def check_bot_process(self) -> dict:
        """Check if bot process is running"""
        try:
            import psutil
            
            # Look for Python processes running hive_helper.py
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'python.exe' or proc.info['name'] == 'python':
                        cmdline = proc.info['cmdline']
                        if cmdline and any('hive_helper.py' in arg for arg in cmdline):
                            return {
                                "status": "healthy",
                                "message": f"Bot process running (PID: {proc.info['pid']})",
                                "pid": proc.info['pid']
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {"status": "error", "message": "Bot process not found"}
            
        except ImportError:
            logger.warning("psutil not available, skipping process check")
            return {"status": "unknown", "message": "Process check unavailable"}
        except Exception as e:
            return {"status": "error", "message": f"Process check failed: {e}"}
    
    def generate_health_report(self) -> dict:
        """Generate comprehensive health report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        # Run all health checks
        report["checks"]["logs"] = self.check_log_file()
        report["checks"]["backend"] = self.check_backend_connectivity()
        report["checks"]["process"] = self.check_bot_process()
        
        # Determine overall status
        statuses = [check["status"] for check in report["checks"].values()]
        if "error" in statuses:
            report["overall_status"] = "error"
        elif "warning" in statuses:
            report["overall_status"] = "warning"
        else:
            report["overall_status"] = "healthy"
        
        return report
    
    def save_report(self, report: dict):
        """Save health report to file"""
        try:
            with open('bot_health_report.json', 'w') as f:
                json.dump(report, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save health report: {e}")
    
    def send_alert(self, report: dict):
        """Send alert if issues detected"""
        if report["overall_status"] in ["error", "warning"]:
            logger.warning(f"Bot health alert: {report['overall_status']}")
            for check_name, check_result in report["checks"].items():
                if check_result["status"] in ["error", "warning"]:
                    logger.warning(f"{check_name}: {check_result['message']}")
    
    def run_monitoring_cycle(self):
        """Run a single monitoring cycle"""
        logger.info("Running bot health check...")
        
        report = self.generate_health_report()
        self.save_report(report)
        self.send_alert(report)
        
        logger.info(f"Health check complete. Overall status: {report['overall_status']}")
        self.last_check_time = datetime.now()
        
        return report
    
    def start_monitoring(self):
        """Start continuous monitoring"""
        logger.info(f"Starting bot monitoring (check interval: {self.check_interval}s)")
        
        while True:
            try:
                self.run_monitoring_cycle()
                time.sleep(self.check_interval)
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring cycle: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

def main():
    """Main monitoring function"""
    monitor = BotMonitor()
    
    # Check if this is a one-time check or continuous monitoring
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        report = monitor.run_monitoring_cycle()
        print(json.dumps(report, indent=2))
    else:
        monitor.start_monitoring()

if __name__ == '__main__':
    main()
