import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { AuthService } from '../../core/services/auth.service';
import { ThemeService, Theme } from '../../core/services/theme.service';
import { User } from '../../core/models/user.model';

interface UserSettings {
  theme: Theme;
  notifications: {
    email: boolean;
    push: boolean;
    mentorRequests: boolean;
    systemUpdates: boolean;
  };
  privacy: {
    showProfile: boolean;
    showActivity: boolean;
    allowDirectMessages: boolean;
  };
}

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent implements OnInit {
  settingsForm: FormGroup;
  user: User | null = null;
  isLoading = true;
  isSaving = false;
  
  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private themeService: ThemeService,
    private snackBar: MatSnackBar,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.settingsForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadUserSettings();
    this.setupThemeFormListener();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      theme: [this.themeService.getCurrentTheme()],
      notifications: this.fb.group({
        email: [true],
        push: [true],
        mentorRequests: [true],
        systemUpdates: [false]
      }),
      privacy: this.fb.group({
        showProfile: [true],
        showActivity: [true],
        allowDirectMessages: [true]
      })
    });
  }

  private loadUserSettings(): void {
    this.isLoading = true;

    this.authService.currentUser$.subscribe({
      next: (user) => {
        if (user) {
          this.user = user;
          this.loadSettingsFromStorage();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user:', error);
        this.isLoading = false;
      }
    });
  }

  private loadSettingsFromStorage(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }
    
    // Load settings from localStorage or use defaults
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      try {
        const settings: UserSettings = JSON.parse(savedSettings);
        // Update theme to current theme service value
        settings.theme = this.themeService.getCurrentTheme();
        this.settingsForm.patchValue(settings);
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }
  }

  private setupThemeFormListener(): void {
    // Listen for theme changes and apply them immediately
    this.settingsForm.get('theme')?.valueChanges.subscribe((theme: Theme) => {
      this.themeService.setTheme(theme);
    });
  }

  onSaveSettings(): void {
    if (this.settingsForm.invalid) {
      return;
    }

    this.isSaving = true;
    const settings: UserSettings = this.settingsForm.value;

    // Save settings to localStorage (browser only)
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('userSettings', JSON.stringify(settings));
    }

    // Theme is already applied via the form listener
    // No need to call applyTheme here

    // Simulate API call delay
    setTimeout(() => {
      this.isSaving = false;
      this.snackBar.open('Settings saved successfully!', 'Close', {
        duration: 3000,
        panelClass: ['success-snackbar']
      });
    }, 1000);
  }

  onDeleteAccount(): void {
    // This would typically show a confirmation dialog
    this.snackBar.open('Account deletion is not implemented yet', 'Close', {
      duration: 3000,
      panelClass: ['warning-snackbar']
    });
  }

  onResetSettings(): void {
    // Reset to default values
    const defaultTheme: Theme = 'auto';
    this.settingsForm.reset({
      theme: defaultTheme,
      notifications: {
        email: true,
        push: true,
        mentorRequests: true,
        systemUpdates: false
      },
      privacy: {
        showProfile: true,
        showActivity: true,
        allowDirectMessages: true
      }
    });

    // Apply the default theme
    this.themeService.setTheme(defaultTheme);

    this.snackBar.open('Settings reset to defaults', 'Close', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }

  onExportData(): void {
    if (!this.user) return;

    const userData = {
      profile: {
        discordUsername: this.user.discordUsername,
        bio: this.user.bio,
        socialLinks: this.user.socialLinks,
        proficiencies: this.user.proficiencies
      },
      settings: this.settingsForm.value,
      exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(userData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `hype-hive-data-${this.user.discordUsername}-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    
    this.snackBar.open('Data exported successfully!', 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }
}
