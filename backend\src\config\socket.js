const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Chat = require('../models/Chat');
const Notification = require('../models/Notification');
const logger = require('./logger');

// Store connected users
const connectedUsers = new Map();

/**
 * Initialize Socket.io server
 * @param {Object} server - HTTP server instance
 * @returns {Object} - Socket.io server instance
 */
const initializeSocketServer = (server) => {
  const io = socketIo(server, {
    cors: {
      origin: process.env.FRONTEND_URL || '*',
      methods: ['GET', 'POST'],
      credentials: true
    },
    allowEIO3: true, // Enable Engine.IO v3 compatibility for tunnels
    transports: ['websocket', 'polling'], // Allow fallback to polling
    upgradeTimeout: 30000,
    pingTimeout: 60000
  });

  // Middleware for authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error: Token not provided'));
      }
      
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await User.findById(decoded.id).select('-__v');
      
      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }
      
      // Attach user to socket
      socket.user = user;
      next();
    } catch (error) {
      logger.error(`Socket authentication error: ${error.message}`);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Connection event
  io.on('connection', (socket) => {
    logger.info(`User connected: ${socket.user.discordUsername} (${socket.user._id})`);
    
    // Add user to connected users map
    connectedUsers.set(socket.user._id.toString(), socket.id);
    
    // Join user to their own room for private messages
    socket.join(socket.user._id.toString());
    
    // Handle join chat event
    socket.on('join_chat', (chatId) => {
      socket.join(chatId);
      logger.info(`User ${socket.user.discordUsername} joined chat ${chatId}`);
    });
    
    // Handle leave chat event
    socket.on('leave_chat', (chatId) => {
      socket.leave(chatId);
      logger.info(`User ${socket.user.discordUsername} left chat ${chatId}`);
    });
    
    // Handle send message event
    socket.on('send_message', async (data) => {
      try {
        const { chatId, content } = data;
        
        // Validate data
        if (!chatId || !content) {
          return socket.emit('error', { message: 'Chat ID and content are required' });
        }
        
        // Get chat
        const chat = await Chat.findById(chatId)
          .populate('participants', 'discordUsername discordAvatar');
        
        if (!chat) {
          return socket.emit('error', { message: 'Chat not found' });
        }
        
        // Check if user is a participant
        const isParticipant = chat.participants.some(
          p => p._id.toString() === socket.user._id.toString()
        );
        
        if (!isParticipant) {
          return socket.emit('error', { message: 'You are not a participant in this chat' });
        }
        
        // Create new message
        const newMessage = {
          sender: socket.user._id,
          content,
          readBy: [socket.user._id],
          createdAt: new Date()
        };
        
        // Add message to chat
        chat.messages.push(newMessage);
        chat.lastMessage = {
          content,
          sender: socket.user._id,
          createdAt: new Date()
        };
        chat.updatedAt = new Date();
        
        await chat.save();
        
        // Populate sender info
        await chat.populate('messages.sender', 'discordUsername discordAvatar');
        await chat.populate('lastMessage.sender', 'discordUsername discordAvatar');
        
        const message = chat.messages[chat.messages.length - 1];
        
        // Emit message to all participants in the chat
        io.to(chatId).emit('new_message', {
          chatId,
          message
        });
        
        // Create notifications for offline participants
        chat.participants.forEach(async (participant) => {
          // Skip sender
          if (participant._id.toString() === socket.user._id.toString()) {
            return;
          }
          
          // Check if participant is online
          const isOnline = connectedUsers.has(participant._id.toString());
          
          // Create notification for offline participants
          if (!isOnline) {
            const notification = new Notification({
              recipient: participant._id,
              sender: socket.user._id,
              type: 'message',
              content: `New message from ${socket.user.discordUsername}`,
              relatedContent: {
                contentType: 'chat',
                contentId: chatId
              }
            });
            
            await notification.save();
          }
        });
      } catch (error) {
        logger.error(`Send message error: ${error.message}`);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });
    
    // Handle typing event
    socket.on('typing', (chatId) => {
      socket.to(chatId).emit('typing', {
        chatId,
        user: {
          _id: socket.user._id,
          discordUsername: socket.user.discordUsername
        }
      });
    });
    
    // Handle stop typing event
    socket.on('stop_typing', (chatId) => {
      socket.to(chatId).emit('stop_typing', {
        chatId,
        user: {
          _id: socket.user._id,
          discordUsername: socket.user.discordUsername
        }
      });
    });
    
    // Handle disconnect event
    socket.on('disconnect', () => {
      logger.info(`User disconnected: ${socket.user.discordUsername} (${socket.user._id})`);
      connectedUsers.delete(socket.user._id.toString());
    });
  });

  return io;
};

module.exports = {
  initializeSocketServer,
  connectedUsers
};
