.mentor-search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 2rem;

  .header-content {
    flex: 1;
    text-align: center;

    h1 {
      color: #6441a5;
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
    }
  }

  .header-actions {
    .become-mentor-btn {
      background: linear-gradient(135deg, #6441a5 0%, #9146ff 100%);
      color: white;
      font-weight: 500;
      padding: 0.75rem 1.5rem;

      mat-icon {
        margin-right: 0.5rem;
        vertical-align: middle;
        flex-shrink: 0;
      }

      &:hover {
        background: linear-gradient(135deg, #5a3a94 0%, #8039e6 100%);
      }
    }
  }
}

.search-filters {
  margin-bottom: 2rem;
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  
  .search-input {
    flex: 2 1 300px;
  }
  
  .category-select {
    flex: 1 1 200px;
  }
  
  button {
    height: 56px;
    margin-top: 4px;
  }
}

.mentors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.loading-container, .error-container, .no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
    line-height: 1;
    vertical-align: middle;
  }
  
  h3 {
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #666;
    margin-bottom: 1rem;
  }
}

.error-container mat-icon {
  color: #f44336;
}

.no-results mat-icon {
  color: #9e9e9e;
}

mat-paginator {
  background-color: transparent;
}

@media (max-width: 768px) {
  .mentor-search-container {
    margin: 1rem;
    padding: 0.5rem;
  }

  .search-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;

    .header-content {
      h1 {
        font-size: 2rem;
      }
    }

    .header-actions {
      .become-mentor-btn {
        width: 100%;
        max-width: 250px;
      }
    }
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;

    button {
      width: 100%;
    }
  }

  .mentors-grid {
    grid-template-columns: 1fr;
  }
}
