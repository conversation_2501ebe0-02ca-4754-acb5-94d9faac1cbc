import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, map, tap, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Notification, NotificationPaginationResult, NotificationType } from '../models/notification.model';
import { SocketService } from './socket.service';
import { MonitoringService } from './monitoring.service';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = `/api/notifications`;
  private unreadCount = new BehaviorSubject<number>(0);
  private notifications = new BehaviorSubject<Notification[]>([]);

  constructor(
    private http: HttpClient,
    private socketService: SocketService,
    private monitoringService: MonitoringService
  ) {
    // Initialize socket listeners
    this.initializeSocketListeners();
    
    // Load initial unread count
    this.loadUnreadCount();
  }

  /**
   * Initialize socket listeners for real-time updates
   */
  private initializeSocketListeners(): void {
    // Listen for new notifications
    this.socketService.onNewNotification().subscribe((notification: Notification) => {
      this.handleNewNotification(notification);
    });
  }

  /**
   * Handle new notification event
   * @param notification New notification
   */
  private handleNewNotification(notification: Notification): void {
    // Update notifications list
    const currentNotifications = this.notifications.value;
    this.notifications.next([notification, ...currentNotifications]);
    
    // Update unread count
    this.unreadCount.next(this.unreadCount.value + 1);
    
    // Track notification received
    this.monitoringService.trackEvent('notification_received', { 
      type: notification.type 
    });
  }

  /**
   * Load unread notification count
   */
  private loadUnreadCount(): void {
    this.getNotifications(1, 1, false).subscribe({
      next: (result) => {
        this.unreadCount.next(result.pagination.total);
      },
      error: (error) => {
        console.error('Failed to load unread count:', error);
      }
    });
  }

  /**
   * Get user notifications
   * @param page Page number
   * @param limit Items per page
   * @param isRead Filter by read status
   * @param type Filter by notification type
   * @returns Observable of notification pagination result
   */
  public getNotifications(
    page = 1, 
    limit = 20, 
    isRead?: boolean,
    type?: NotificationType
  ): Observable<NotificationPaginationResult> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    
    if (isRead !== undefined) {
      params = params.set('isRead', isRead.toString());
    }
    
    if (type) {
      params = params.set('type', type);
    }
    
    return this.http.get<any>(this.apiUrl, { params }).pipe(
      map(response => {
        if (response.success) {
          // Update notifications
          if (page === 1) {
            this.notifications.next(response.data);
          } else {
            const currentNotifications = this.notifications.value;
            this.notifications.next([...currentNotifications, ...response.data]);
          }
          
          return {
            notifications: response.data,
            pagination: response.pagination
          };
        }
        throw new Error(response.message || 'Failed to get notifications');
      }),
      catchError(error => {
        this.monitoringService.trackError('get_notifications_error', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Mark notification as read
   * @param notificationId Notification ID
   * @returns Observable of success status
   */
  public markAsRead(notificationId: string): Observable<boolean> {
    return this.http.put<any>(`${this.apiUrl}/${notificationId}/read`, {}).pipe(
      map(response => {
        if (response.success) {
          // Update unread count
          this.unreadCount.next(Math.max(0, this.unreadCount.value - 1));
          
          // Update notification in list
          const currentNotifications = this.notifications.value;
          const updatedNotifications = currentNotifications.map(notification => {
            if (notification._id === notificationId) {
              return { ...notification, isRead: true };
            }
            return notification;
          });
          
          this.notifications.next(updatedNotifications);
          
          return true;
        }
        return false;
      }),
      catchError(() => {
        return throwError(() => new Error('Failed to mark notification as read'));
      })
    );
  }

  /**
   * Mark all notifications as read
   * @returns Observable of success status
   */
  public markAllAsRead(): Observable<boolean> {
    return this.http.put<any>(`${this.apiUrl}/read-all`, {}).pipe(
      map(response => {
        if (response.success) {
          // Reset unread count
          this.unreadCount.next(0);
          
          // Update all notifications in list
          const currentNotifications = this.notifications.value;
          const updatedNotifications = currentNotifications.map(notification => {
            return { ...notification, isRead: true };
          });
          
          this.notifications.next(updatedNotifications);
          
          return true;
        }
        return false;
      }),
      catchError(() => {
        return throwError(() => new Error('Failed to mark all notifications as read'));
      })
    );
  }

  /**
   * Delete notification
   * @param notificationId Notification ID
   * @returns Observable of success status
   */
  public deleteNotification(notificationId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.apiUrl}/${notificationId}`).pipe(
      map(response => {
        if (response.success) {
          // Update notifications list
          const currentNotifications = this.notifications.value;
          const notification = currentNotifications.find(n => n._id === notificationId);
          
          // If notification was unread, update count
          if (notification && !notification.isRead) {
            this.unreadCount.next(Math.max(0, this.unreadCount.value - 1));
          }
          
          // Remove from list
          const updatedNotifications = currentNotifications.filter(
            n => n._id !== notificationId
          );
          
          this.notifications.next(updatedNotifications);
          
          return true;
        }
        return false;
      }),
      catchError(() => {
        return throwError(() => new Error('Failed to delete notification'));
      })
    );
  }

  /**
   * Get unread notification count
   * @returns Observable of unread count
   */
  public getUnreadCount(): Observable<number> {
    return this.unreadCount.asObservable();
  }

  /**
   * Get notifications list
   * @returns Observable of notifications
   */
  public getNotificationsList(): Observable<Notification[]> {
    return this.notifications.asObservable();
  }
}
