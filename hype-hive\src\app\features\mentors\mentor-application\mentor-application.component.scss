.mentor-application-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
}

.application-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  
  mat-card-header {
    background: linear-gradient(135deg, #6441a5 0%, #9146ff 100%);
    color: white;
    padding: 2rem;
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      
      mat-icon {
        font-size: 1.8rem;
        width: 1.8rem;
        height: 1.8rem;
      }
    }
    
    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1rem;
    }
  }
  
  mat-card-content {
    padding: 2rem;
  }
}

.full-width {
  width: 100%;
  margin-bottom: 1rem;
}

.step-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  
  button {
    min-width: 100px;
  }
}

.categories-section {
  .section-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
    
    .category-checkbox {
      padding: 0.5rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #f5f5f5;
        border-color: #6441a5;
      }
      
      &.mat-mdc-checkbox-checked {
        background-color: rgba(100, 65, 165, 0.1);
        border-color: #6441a5;
      }
    }
  }
  
  .categories-error {
    color: #f44336;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: block;
  }
}

.profile-section {
  .section-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }
  
  .social-links {
    margin-top: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
      font-weight: 500;
    }
    
    .social-links-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      
      mat-form-field {
        mat-icon {
          color: #666;
          margin-right: 0.5rem;
        }
      }
    }
  }
}

.review-section {
  .section-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }
  
  .review-summary {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    
    .summary-item {
      margin-bottom: 1rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      strong {
        display: block;
        margin-bottom: 0.5rem;
        color: #333;
      }
      
      .selected-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        
        mat-chip {
          font-size: 0.875rem;
        }
      }
    }
  }
  
  .agreement-checkbox {
    margin-bottom: 1rem;
    
    .mat-mdc-checkbox-label {
      line-height: 1.5;
    }
  }
  
  .agreement-error {
    color: #f44336;
    font-size: 0.875rem;
    display: block;
    margin-top: 0.5rem;
  }
}

// Stepper customization
::ng-deep {
  .mat-stepper-vertical {
    .mat-step-header {
      padding: 1rem 0;
      
      .mat-step-icon {
        background-color: #6441a5;
        
        &.mat-step-icon-state-done {
          background-color: #4caf50;
        }
      }
      
      .mat-step-label {
        font-weight: 500;
        color: #333;
      }
    }
    
    .mat-vertical-content {
      padding: 1.5rem 0 2rem 36px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .mentor-application-container {
    margin: 1rem;
    padding: 0.5rem;
  }
  
  .application-card {
    mat-card-header {
      padding: 1.5rem;
      
      mat-card-title {
        font-size: 1.25rem;
      }
    }
    
    mat-card-content {
      padding: 1.5rem;
    }
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .social-links-grid {
    grid-template-columns: 1fr;
  }
  
  .step-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}

// Snackbar styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50;
    color: white;
  }
  
  .error-snackbar {
    background-color: #f44336;
    color: white;
  }
  
  .warning-snackbar {
    background-color: #ff9800;
    color: white;
  }
  
  .info-snackbar {
    background-color: #2196f3;
    color: white;
  }
}
