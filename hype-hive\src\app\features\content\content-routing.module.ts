import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ContentListComponent } from './content-list/content-list.component';
import { ContentDetailComponent } from './content-detail/content-detail.component';
import { AuthGuard } from '../../core/guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: ContentListComponent
  },
  {
    path: 'upload',
    loadComponent: () => import('./content-upload/content-upload.component').then(m => m.ContentUploadComponent),
    canActivate: [AuthGuard]
  },
  {
    path: ':id',
    component: ContentDetailComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ContentRoutingModule { }
