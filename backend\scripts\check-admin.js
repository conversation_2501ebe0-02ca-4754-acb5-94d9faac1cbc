#!/usr/bin/env node
/**
 * Script to check current admin users and their privileges
 */

const mongoose = require('mongoose');
const User = require('../src/models/User');
const logger = require('../src/config/logger');

// Import the admin configuration
const ADMIN_DISCORD_IDS = [
  '323345923964928001', // captain.sexy - System Administrator
];

const isSystemAdmin = (discordId) => {
  return ADMIN_DISCORD_IDS.includes(discordId);
};

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hypehive');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
};

/**
 * Check admin status
 */
const checkAdminStatus = async () => {
  try {
    console.log('🔍 Checking System Administrator Status\n');
    
    console.log('Configured System Admin Discord IDs:');
    console.log('===================================');
    ADMIN_DISCORD_IDS.forEach(id => {
      console.log(`• ${id} - ${isSystemAdmin(id) ? '✅ Active' : '❌ Inactive'}`);
    });
    
    console.log('\nDatabase Admin Users:');
    console.log('====================');
    
    const adminUsers = await User.find({ role: 'admin' })
      .select('discordId discordUsername role isMentor isActive lastLogin createdAt')
      .sort({ createdAt: -1 });
    
    if (adminUsers.length === 0) {
      console.log('No admin users found in database.');
      return;
    }
    
    adminUsers.forEach(user => {
      const isSystemAdminUser = isSystemAdmin(user.discordId);
      const lastLoginStr = user.lastLogin ? user.lastLogin.toLocaleDateString() : 'Never';
      
      console.log(`\n👤 ${user.discordUsername} (${user.discordId})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Mentor: ${user.isMentor ? '✅' : '❌'}`);
      console.log(`   Active: ${user.isActive ? '✅' : '❌'}`);
      console.log(`   System Admin: ${isSystemAdminUser ? '✅' : '❌'}`);
      console.log(`   Last Login: ${lastLoginStr}`);
      console.log(`   Created: ${user.createdAt.toLocaleDateString()}`);
    });
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`Total Admin Users: ${adminUsers.length}`);
    console.log(`System Admins: ${adminUsers.filter(u => isSystemAdmin(u.discordId)).length}`);
    console.log(`Active Admins: ${adminUsers.filter(u => u.isActive).length}`);
    console.log(`Admin Mentors: ${adminUsers.filter(u => u.isMentor).length}`);
    
  } catch (error) {
    console.error('Error checking admin status:', error);
    process.exit(1);
  }
};

/**
 * Main execution function
 */
const main = async () => {
  try {
    await connectDB();
    await checkAdminStatus();
  } catch (error) {
    console.error('Script execution failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { checkAdminStatus };
