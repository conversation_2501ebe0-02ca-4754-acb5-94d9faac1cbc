const express = require('express');
const {
  getAllUsers,
  getUserById,
  updateUserProfile,
  applyForMentor,
  reviewMentorApplication,
  getMentors
} = require('../controllers/userController');
const { protect, authorize } = require('../middleware/auth');
const { userValidation } = require('../middleware/validation');
const { mentorApplicationLimiter, profileUpdateLimiter } = require('../middleware/security');

const router = express.Router();

// GET /api/users - Get all users (admin only)
router.get('/', protect, authorize('admin'), userValidation.searchUsers, getAllUsers);

// GET /api/users/mentors - Get all mentors
router.get('/mentors', userValidation.searchUsers, getMentors);

// GET /api/users/:id - Get user by ID
router.get('/:id', protect, userValidation.getUserById, getUserById);

// PUT /api/users/:id - Update user profile
router.put('/:id', protect, profileUpdateLimiter, userValidation.updateProfile, updateUserProfile);

// POST /api/users/mentor/apply - Apply for mentor status
router.post('/mentor/apply', protect, mentorApplicationLimiter, userValidation.mentorApplication, applyForMentor);

// PUT /api/users/:id/mentor/review - Review mentor application (admin only)
router.put('/:id/mentor/review', protect, authorize('admin'), reviewMentorApplication);

module.exports = router;
