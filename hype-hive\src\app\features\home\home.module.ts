import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { HomeRoutingModule } from './home-routing.module';
import { HomeComponent } from './home/<USER>';
import { RecommendedContentComponent } from './recommended-content/recommended-content.component';
import { MentorCardComponent } from '../mentors/mentor-card/mentor-card.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    HomeRoutingModule,
    HomeComponent,
    RecommendedContentComponent,
    MentorCardComponent
  ]
})
export class HomeModule { }
