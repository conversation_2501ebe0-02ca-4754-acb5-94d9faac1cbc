const User = require('../models/User');
const Content = require('../models/Content');
const logger = require('../config/logger');

/**
 * Get personalized content recommendations for the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getContentRecommendations = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get user's selected proficiencies
    const userProficiencies = user.proficiencies
      .filter(p => p.isSelected)
      .map(p => p.category);

    let recommendations = [];

    if (userProficiencies.length > 0) {
      // Get content based on user's proficiencies
      recommendations = await Content.find({
        isPublished: true,
        category: { $in: userProficiencies }
      })
      .populate('createdBy', 'discordUsername discordAvatar')
      .sort({ createdAt: -1, likes: -1 })
      .limit(6);
    }

    // If not enough recommendations, fill with popular content
    if (recommendations.length < 6) {
      const additionalContent = await Content.find({
        isPublished: true,
        _id: { $nin: recommendations.map(r => r._id) }
      })
      .populate('createdBy', 'discordUsername discordAvatar')
      .sort({ likes: -1, views: -1 })
      .limit(6 - recommendations.length);

      recommendations = [...recommendations, ...additionalContent];
    }

    logger.info(`Generated ${recommendations.length} content recommendations for user ${user.discordUsername}`);

    return res.status(200).json({
      success: true,
      data: recommendations,
      message: 'Content recommendations retrieved successfully'
    });
  } catch (error) {
    logger.error(`Get content recommendations error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get content recommendations',
      error: error.message
    });
  }
};

/**
 * Get recommended mentors based on user interests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMentorRecommendations = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get user's selected proficiencies
    const userProficiencies = user.proficiencies
      .filter(p => p.isSelected)
      .map(p => p.category);

    let recommendations = [];

    if (userProficiencies.length > 0) {
      // Find mentors who have proficiencies matching user's interests
      recommendations = await User.find({
        isMentor: true,
        isActive: true,
        _id: { $ne: userId }, // Exclude current user
        'proficiencies.category': { $in: userProficiencies },
        'proficiencies.isSelected': true
      })
      .select('discordId discordUsername discordAvatar bio socialLinks proficiencies createdAt')
      .sort({ createdAt: -1 })
      .limit(6);
    }

    // If not enough recommendations, get popular mentors
    if (recommendations.length < 6) {
      const additionalMentors = await User.find({
        isMentor: true,
        isActive: true,
        _id: { 
          $ne: userId,
          $nin: recommendations.map(r => r._id)
        }
      })
      .select('discordId discordUsername discordAvatar bio socialLinks proficiencies createdAt')
      .sort({ createdAt: -1 })
      .limit(6 - recommendations.length);

      recommendations = [...recommendations, ...additionalMentors];
    }

    logger.info(`Generated ${recommendations.length} mentor recommendations for user ${user.discordUsername}`);

    return res.status(200).json({
      success: true,
      data: recommendations,
      message: 'Mentor recommendations retrieved successfully'
    });
  } catch (error) {
    logger.error(`Get mentor recommendations error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get mentor recommendations',
      error: error.message
    });
  }
};

/**
 * Get similar content based on a specific content item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSimilarContent = async (req, res) => {
  try {
    const { contentId } = req.params;
    
    // Get the original content
    const originalContent = await Content.findById(contentId);
    if (!originalContent) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    // Find similar content in the same category
    const similarContent = await Content.find({
      _id: { $ne: contentId },
      category: originalContent.category,
      isPublished: true
    })
    .populate('createdBy', 'discordUsername discordAvatar')
    .sort({ likes: -1, views: -1 })
    .limit(4);

    return res.status(200).json({
      success: true,
      data: similarContent,
      message: 'Similar content retrieved successfully'
    });
  } catch (error) {
    logger.error(`Get similar content error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get similar content',
      error: error.message
    });
  }
};

/**
 * Track user content interaction for better recommendations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const trackContentInteraction = async (req, res) => {
  try {
    const { contentId, action } = req.body;
    const userId = req.user._id;

    // Validate action
    if (!['view', 'like', 'comment'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be view, like, or comment'
      });
    }

    // Check if content exists
    const content = await Content.findById(contentId);
    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    // For now, just log the interaction
    // In a more advanced system, you would store this in a separate interactions collection
    logger.info(`User ${req.user.discordUsername} performed ${action} on content ${contentId}`);

    return res.status(200).json({
      success: true,
      message: 'Interaction tracked successfully'
    });
  } catch (error) {
    logger.error(`Track content interaction error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to track interaction',
      error: error.message
    });
  }
};

module.exports = {
  getContentRecommendations,
  getMentorRecommendations,
  getSimilarContent,
  trackContentInteraction
};
