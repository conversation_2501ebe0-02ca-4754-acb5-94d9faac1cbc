const { isSystemAdmin } = require('./security');
const logger = require('../config/logger');

/**
 * Middleware to provide system admin privileges
 * This middleware should be used early in the middleware chain
 * to ensure system admins have unrestricted access
 */
const systemAdminMiddleware = (req, res, next) => {
  // Check if user is authenticated and is a system admin
  if (req.user && isSystemAdmin(req.user.discordId)) {
    // Mark request as coming from system admin
    req.isSystemAdmin = true;
    
    // Log system admin access for audit purposes
    logger.info(`System admin access: ${req.user.discordUsername} (${req.user.discordId})`, {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }
  
  next();
};

/**
 * Check if the current request is from a system admin
 * @param {Object} req - Express request object
 * @returns {boolean} - True if request is from system admin
 */
const isSystemAdminRequest = (req) => {
  return req.isSystemAdmin === true;
};

module.exports = {
  systemAdminMiddleware,
  isSystemAdminRequest
};
