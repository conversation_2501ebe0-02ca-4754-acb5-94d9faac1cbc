import { InstructionCategory } from './user.model';

export interface Content {
  _id: string;
  id?: string; // For backward compatibility
  title: string;
  description: string;
  category: InstructionCategory;
  videoUrl?: string;
  embedUrl?: string;
  thumbnailUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: any; // Can be string or populated user object
  isPublished: boolean;
  tags: string[];
  views: number;
  likes: number;
  likedBy: string[];
  comments: any[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface ContentFilter {
  category?: InstructionCategory;
  searchTerm?: string;
}
