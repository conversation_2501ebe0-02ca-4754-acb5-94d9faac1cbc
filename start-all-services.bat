@echo off
REM HypeHive Services Startup Script
REM Starts Backend, SSR Frontend, and Discord Bot with logging

setlocal enabledelayedexpansion

REM Configuration
set SCRIPT_DIR=%~dp0
set LOG_DIR=%SCRIPT_DIR%logs
set BACKEND_DIR=%SCRIPT_DIR%backend
set FRONTEND_DIR=%SCRIPT_DIR%hype-hive
set DISCORD_BOT_DIR=%SCRIPT_DIR%discord-bot

REM Create logs directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Log files
set MASTER_LOG=%LOG_DIR%\services-master.log
set BACKEND_LOG=%LOG_DIR%\backend.log
set FRONTEND_LOG=%LOG_DIR%\frontend.log
set DISCORD_LOG=%LOG_DIR%\discord-bot.log

REM Clear previous logs
echo. > "%MASTER_LOG%"
echo. > "%BACKEND_LOG%"
echo. > "%FRONTEND_LOG%"
echo. > "%DISCORD_LOG%"

echo [%date% %time%] Starting HypeHive services... >> "%MASTER_LOG%"
echo Starting HypeHive services...

REM Check if directories exist
if not exist "%BACKEND_DIR%" (
    echo [%date% %time%] ERROR: Backend directory not found: %BACKEND_DIR% >> "%MASTER_LOG%"
    echo ERROR: Backend directory not found: %BACKEND_DIR%
    goto :error
)

if not exist "%FRONTEND_DIR%" (
    echo [%date% %time%] ERROR: Frontend directory not found: %FRONTEND_DIR% >> "%MASTER_LOG%"
    echo ERROR: Frontend directory not found: %FRONTEND_DIR%
    goto :error
)

if not exist "%DISCORD_BOT_DIR%" (
    echo [%date% %time%] ERROR: Discord bot directory not found: %DISCORD_BOT_DIR% >> "%MASTER_LOG%"
    echo ERROR: Discord bot directory not found: %DISCORD_BOT_DIR%
    goto :error
)

echo [%date% %time%] All directories found. Starting services... >> "%MASTER_LOG%"
echo All directories found. Starting services...

REM Start Backend Server
echo [%date% %time%] Starting backend server... >> "%MASTER_LOG%"
echo Starting backend server...
cd /d "%BACKEND_DIR%"
start "HypeHive Backend" cmd /c "npm run dev 2>&1 | tee -a \"%BACKEND_LOG%\" && pause"

REM Wait a moment for backend to start
timeout /t 5 /nobreak > nul

REM Start Frontend SSR Server
echo [%date% %time%] Starting frontend SSR server... >> "%MASTER_LOG%"
echo Starting frontend SSR server...
cd /d "%FRONTEND_DIR%"
start "HypeHive Frontend" cmd /c "npm run start:local 2>&1 | tee -a \"%FRONTEND_LOG%\" && pause"

REM Wait a moment for frontend to start
timeout /t 5 /nobreak > nul

REM Start Discord Bot
echo [%date% %time%] Starting Discord bot... >> "%MASTER_LOG%"
echo Starting Discord bot...
cd /d "%DISCORD_BOT_DIR%"
start "HypeHive Discord Bot" cmd /c "python start.py 2>&1 | tee -a \"%DISCORD_LOG%\" && pause"

REM Return to original directory
cd /d "%SCRIPT_DIR%"

echo [%date% %time%] All services started successfully! >> "%MASTER_LOG%"
echo.
echo ========================================
echo All HypeHive services have been started!
echo ========================================
echo.
echo Services running:
echo - Backend Server (Development mode)
echo - Frontend SSR Server (Local configuration)
echo - Discord Bot
echo.
echo Log files:
echo - Master log: %MASTER_LOG%
echo - Backend log: %BACKEND_LOG%
echo - Frontend log: %FRONTEND_LOG%
echo - Discord bot log: %DISCORD_LOG%
echo.
echo To stop all services, close all the opened command windows.
echo.
pause

goto :end

:error
echo [%date% %time%] Script execution failed >> "%MASTER_LOG%"
echo Script execution failed. Check the logs for details.
pause
exit /b 1

:end
echo [%date% %time%] Script completed >> "%MASTER_LOG%"
exit /b 0
