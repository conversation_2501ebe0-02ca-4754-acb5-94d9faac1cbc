import os
import discord
from discord import app_commands, Interaction
from discord.ext import commands
from dotenv import load_dotenv
import requests
import logging
import asyncio
import time
from typing import Dict, Any
from error_handler import setup_error_handler, handle_errors, <PERSON><PERSON>Handler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('discord_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_BOT_TOKEN')
BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:3000/api')
GUILD_ID = int(os.getenv('DISCORD_GUILD_ID', '0'))
MENTOR_ROLE_NAME = 'Mentor'
MENTOR_CHANNEL_NAME = 'mentors-list'

# Updated proficiencies to match backend categories
PROFICIENCIES = [
    'Account Setup', 'Bots', 'Networking', 'Streaming Platforms',
    'Emotes', 'Affiliate Roles', 'Console Help', 'PC Help'
]

intents = discord.Intents.default()
intents.members = True
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

async def get_or_create_role(guild: discord.Guild, role_name: str) -> discord.Role:
    """Get existing role or create new one"""
    try:
        role = discord.utils.get(guild.roles, name=role_name)
        if not role:
            logger.info(f"Creating role '{role_name}' in guild {guild.name}")
            role = await guild.create_role(name=role_name, mentionable=True, reason='Mentor role for HiveHelper')
        return role
    except Exception as e:
        logger.error(f"Error creating/getting role {role_name}: {e}")
        raise

async def get_or_create_channel(guild: discord.Guild, channel_name: str) -> discord.TextChannel:
    """Get existing channel or create new one"""
    try:
        channel = discord.utils.get(guild.text_channels, name=channel_name)
        if not channel:
            logger.info(f"Creating channel '{channel_name}' in guild {guild.name}")
            overwrites = {guild.default_role: discord.PermissionOverwrite(send_messages=False)}
            channel = await guild.create_text_channel(channel_name, overwrites=overwrites, reason='Mentor list channel for HiveHelper')
        return channel
    except Exception as e:
        logger.error(f"Error creating/getting channel {channel_name}: {e}")
        raise

async def make_api_request(method: str, endpoint: str, data: Dict[Any, Any] = None) -> Dict[Any, Any]:
    """Make API request to backend with error handling and retry logic"""
    async def _make_request():
        url = f"{BACKEND_API_URL}/{endpoint.lstrip('/')}"
        logger.debug(f"Making {method} request to {url}")

        if method.upper() == 'GET':
            response = requests.get(url, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, timeout=10)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, timeout=10)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        response.raise_for_status()
        return response.json() if response.content else {}

    try:
        # Use retry handler for API requests
        return await RetryHandler.retry_with_backoff(_make_request, max_retries=3)
    except requests.exceptions.Timeout:
        logger.error(f"API request timeout for {endpoint}")
        return {"error": "timeout"}
    except requests.exceptions.ConnectionError:
        logger.error(f"API connection error for {endpoint}")
        return {"error": "connection"}
    except requests.exceptions.HTTPError as e:
        logger.error(f"API HTTP error for {endpoint}: {e}")
        return {"error": "http", "status_code": e.response.status_code if e.response else None}
    except Exception as e:
        logger.error(f"Unexpected API error for {endpoint}: {e}")
        return {"error": "unknown"}

async def update_mentor_list(guild: discord.Guild):
    """Update the mentor list in the designated channel"""
    try:
        channel = discord.utils.get(guild.text_channels, name=MENTOR_CHANNEL_NAME)
        if not channel:
            channel = await get_or_create_channel(guild, MENTOR_CHANNEL_NAME)

        # Fetch mentor data from backend using Discord bot specific endpoint
        mentors_data = await make_api_request('GET', 'discord-bot/mentors')

        if 'error' in mentors_data:
            logger.error(f"API error fetching mentors: {mentors_data}")
            embed = discord.Embed(
                title="Current Mentors",
                description="⚠️ Unable to fetch mentor list. Please try again later.",
                color=0xFF6B6B
            )
            await channel.send(embed=embed)
            return

        mentors = mentors_data.get('data', [])

        # Create embed for mentor list with website header
        if not mentors:
            embed = discord.Embed(
                title="Current Mentors",
                description="[Visit HypeHive Website](https://hypehive.linksynk.info)\n\nNo mentors are currently available.",
                color=0xFFE66D
            )
        else:
            embed = discord.Embed(
                title="Current Mentors",
                description=f"[Visit HypeHive Website](https://hypehive.linksynk.info)\n\nWe have {len(mentors)} mentor(s) available to help!",
                color=0x4ECDC4
            )

            mentor_list = []
            for mentor in mentors:
                user = guild.get_member(int(mentor['discordId']))
                if user:
                    # Use the formatted data from Discord bot endpoint
                    selected_profs = mentor.get('selectedProficiencies', [])
                    profs_text = ', '.join(selected_profs) if selected_profs else 'General help'
                    
                    # Create mentor page link using the user's ID
                    mentor_link = f"https://hypehive.linksynk.info/mentors/{mentor.get('userId', mentor['discordId'])}"
                    mentor_entry = f"• [{mentor['discordUsername']}]({mentor_link}) - {profs_text}"
                    mentor_list.append(mentor_entry)

            # Add mentors as a single field to maintain list format
            if mentor_list:
                embed.add_field(
                    name="Available Mentors",
                    value="\n".join(mentor_list),
                    inline=False
                )

        # Update existing message or create new one
        async for msg in channel.history(limit=10):
            if msg.author == bot.user and msg.embeds:
                await msg.edit(embed=embed)
                logger.info("Updated mentor list message")
                return

        # No existing message found, send new one
        await channel.send(embed=embed)
        logger.info("Created new mentor list message")

    except Exception as e:
        logger.error(f"Error updating mentor list: {e}")
        # Try to send error message to channel
        try:
            channel = discord.utils.get(guild.text_channels, name=MENTOR_CHANNEL_NAME)
            if channel:
                embed = discord.Embed(
                    title="Current Mentors",
                    description="❌ Error updating mentor list. Please contact an administrator.",
                    color=0xFF6B6B
                )
                await channel.send(embed=embed)
        except:
            pass  # Ignore if we can't send error message

@bot.event
async def on_ready():
    """Bot startup event"""
    logger.info(f'Logged in as {bot.user} (ID: {bot.user.id})')

    # Initialize error handler
    error_handler = setup_error_handler(bot)
    logger.info("Error handler initialized")

    try:
        # Sync slash commands only for specific guild
        guild = bot.get_guild(GUILD_ID)
        if guild:
            synced = await bot.tree.sync(guild=guild)
            logger.info(f'Synced {len(synced)} command(s) for guild {guild.name}')
            
            # Setup guild-specific features
            try:
                await get_or_create_role(guild, MENTOR_ROLE_NAME)
                await get_or_create_channel(guild, MENTOR_CHANNEL_NAME)
                await update_mentor_list(guild)
                logger.info("Bot startup setup completed successfully")
            except Exception as e:
                logger.error(f'Error during startup setup: {e}')
                if error_handler:
                    await error_handler.handle_discord_error(e, "startup_setup")
        else:
            logger.warning(f'Could not find guild with ID: {GUILD_ID}')
    except Exception as e:
        logger.error(f'Failed to sync commands: {e}')

    guild = bot.get_guild(GUILD_ID)
    if guild:
        logger.info(f'Connected to guild: {guild.name} (ID: {guild.id})')
        try:
            await get_or_create_role(guild, MENTOR_ROLE_NAME)
            await get_or_create_channel(guild, MENTOR_CHANNEL_NAME)
            await update_mentor_list(guild)
            logger.info("Bot startup setup completed successfully")
        except Exception as e:
            logger.error(f'Error during startup setup: {e}')
            if error_handler:
                await error_handler.handle_discord_error(e, "startup_setup")
    else:
        logger.warning(f'Could not find guild with ID: {GUILD_ID}')

@bot.event
async def on_error(event, *args, **kwargs):
    """Global error handler"""
    logger.error(f'Error in event {event}', exc_info=True)

@bot.tree.command(name='help', description='Show available commands')
async def help_command(interaction: Interaction):
    """Display help information"""
    embed = discord.Embed(
        title="HiveHelper Bot Commands",
        description="Available commands for mentor management",
        color=0x7289DA
    )
    embed.add_field(
        name="/mentor_status",
        value="Check if you're registered as a mentor",
        inline=False
    )
    embed.add_field(
        name="/list_mentors",
        value="Show all current mentors and their specializations",
        inline=False
    )
    embed.add_field(
        name="/sync_mentors",
        value="Manually sync mentor list (Admin only)",
        inline=False
    )
    embed.set_footer(text="Note: Mentor applications must be submitted through the website")

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name='mentor_status', description='Check your mentor status')
async def mentor_status(interaction: Interaction):
    """Check if user is registered as a mentor"""
    try:
        await interaction.response.defer(ephemeral=True)

        # Sync user data first (create/update user in backend)
        sync_data = await make_api_request('POST', 'discord-bot/sync-user', {
            'discordId': str(interaction.user.id),
            'discordUsername': interaction.user.display_name,
            'discordAvatar': str(interaction.user.avatar.url) if interaction.user.avatar else None
        })

        if 'error' in sync_data:
            logger.error(f"Failed to sync user data: {sync_data}")
            await interaction.followup.send("❌ Unable to check your status. Please try again later.", ephemeral=True)
            return

        # Get user data from backend using Discord bot endpoint
        user_data = await make_api_request('GET', f'discord-bot/user/{interaction.user.id}')

        if 'error' in user_data or not user_data.get('success'):
            embed = discord.Embed(
                title="Mentor Status",
                description="You are not registered in the HypeHive system. Please visit the website to create an account.",
                color=0xFF6B6B
            )
        else:
            user = user_data.get('data', {})
            is_mentor = user.get('isMentor', False)

            if is_mentor:
                selected_profs = [p['category'] for p in user.get('proficiencies', []) if p.get('isSelected', False)]
                profs_text = ', '.join(selected_profs) if selected_profs else 'No specializations set'

                embed = discord.Embed(
                    title="Mentor Status",
                    description="✅ You are registered as a mentor!",
                    color=0x4ECDC4
                )
                embed.add_field(name="Specializations", value=profs_text, inline=False)
            else:
                app_status = user.get('mentorApplication', {}).get('status', 'none')
                if app_status == 'pending':
                    description = "⏳ Your mentor application is pending review."
                elif app_status == 'rejected':
                    description = "❌ Your mentor application was rejected. You can apply again through the website."
                else:
                    description = "You are not a mentor. Visit the website to apply!"

                embed = discord.Embed(
                    title="Mentor Status",
                    description=description,
                    color=0xFFE66D
                )

        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in mentor_status command: {e}")
        await interaction.followup.send("An error occurred while checking your mentor status.", ephemeral=True)

@bot.tree.command(name='list_mentors', description='Show all current mentors')
async def list_mentors(interaction: Interaction):
    """List all current mentors"""
    try:
        await interaction.response.defer()

        # Get mentors from backend using Discord bot endpoint
        mentors_data = await make_api_request('GET', 'discord-bot/mentors')

        if 'error' in mentors_data:
            logger.error(f"API error fetching mentors: {mentors_data}")
            await interaction.followup.send("❌ Unable to fetch mentor list. Please try again later.", ephemeral=True)
            return

        mentors = mentors_data.get('data', [])

        if not mentors:
            embed = discord.Embed(
                title="Current Mentors",
                description="[Visit HypeHive Website](https://hypehive.linksynk.info)\n\nNo mentors are currently available.",
                color=0xFFE66D
            )
        else:
            embed = discord.Embed(
                title="Current Mentors",
                description=f"[Visit HypeHive Website](https://hypehive.linksynk.info)\n\nWe have {len(mentors)} mentor(s) available to help!",
                color=0x4ECDC4
            )

            mentor_list = []
            for mentor in mentors[:10]:  # Limit to 10 to avoid embed limits
                user = interaction.guild.get_member(int(mentor['discordId']))
                if user:
                    # Use the formatted data from Discord bot endpoint
                    selected_profs = mentor.get('selectedProficiencies', [])
                    profs_text = ', '.join(selected_profs) if selected_profs else 'General help'
                    
                    # Create mentor page link using the user's ID
                    mentor_link = f"https://hypehive.linksynk.info/mentors/{mentor.get('userId', mentor['discordId'])}"
                    mentor_entry = f"• [{mentor['discordUsername']}]({mentor_link}) - {profs_text}"
                    mentor_list.append(mentor_entry)

            # Add mentors as a single field to maintain list format
            if mentor_list:
                embed.add_field(
                    name="Available Mentors",
                    value="\n".join(mentor_list),
                    inline=False
                )

            if len(mentors) > 10:
                embed.set_footer(text=f"Showing first 10 of {len(mentors)} mentors")

        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in list_mentors command: {e}")
        await interaction.followup.send("An error occurred while fetching the mentor list.", ephemeral=True)

@bot.tree.command(name='sync_mentors', description='Manually sync mentor list (Admin only)')
async def sync_mentors(interaction: Interaction):
    """Manually sync the mentor list - admin only"""
    try:
        # Check if user has admin permissions
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ This command requires administrator permissions.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)
        await update_mentor_list(interaction.guild)
        await interaction.followup.send("✅ Mentor list has been synced successfully!")

    except Exception as e:
        logger.error(f"Error in sync_mentors command: {e}")
        await interaction.followup.send("An error occurred while syncing the mentor list.", ephemeral=True)

@bot.tree.command(name='bot_health', description='Check bot health and status (Admin only)')
async def bot_health(interaction: Interaction):
    """Check bot health and connectivity - admin only"""
    try:
        # Check if user has admin permissions
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ This command requires administrator permissions.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Check backend health using Discord bot endpoint
        backend_health = await make_api_request('GET', 'discord-bot/health')

        # Create health report embed
        is_healthy = backend_health.get('success', False) and backend_health.get('status') == 'healthy'
        embed = discord.Embed(
            title="🏥 Bot Health Report",
            color=0x4ECDC4 if is_healthy else 0xFF6B6B
        )

        # Bot status
        bot_status = "🟢 Online"
        embed.add_field(name="Bot Status", value=bot_status, inline=True)
        embed.add_field(name="Latency", value=f"{round(bot.latency * 1000, 2)}ms", inline=True)
        embed.add_field(name="Guilds", value=str(len(bot.guilds)), inline=True)

        # Backend status
        if is_healthy:
            backend_status = "🟢 Connected"
            backend_data = backend_health.get('data', {})
            embed.add_field(name="Backend API", value=backend_status, inline=True)
            embed.add_field(name="Database", value=f"🟢 {backend_data.get('database', 'Unknown')}", inline=True)
            embed.add_field(name="Users", value=str(backend_data.get('userCount', 'Unknown')), inline=True)
            embed.add_field(name="Mentors", value=str(backend_data.get('mentorCount', 'Unknown')), inline=True)
        else:
            backend_status = "🔴 Disconnected"
            embed.add_field(name="Backend API", value=backend_status, inline=True)
            if 'error' in backend_health:
                embed.add_field(name="Error", value=backend_health['error'][:100], inline=False)

        embed.set_footer(text=f"Last checked: {backend_health.get('data', {}).get('timestamp', 'Unknown')}")

        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in bot_health command: {e}")
        await interaction.followup.send("An error occurred while checking bot health.", ephemeral=True)

# Error handler for application commands
@bot.tree.error
async def on_app_command_error(interaction: Interaction, error: app_commands.AppCommandError):
    """Handle application command errors"""
    logger.error(f"Application command error: {error}")

    if interaction.response.is_done():
        await interaction.followup.send("An unexpected error occurred.", ephemeral=True)
    else:
        await interaction.response.send_message("An unexpected error occurred.", ephemeral=True)

if __name__ == '__main__':
    # Create async setup function to properly await coroutines
    async def setup_bot():
        try:
            # Import but don't instantiate the class directly to avoid duplicate commands
            from mentor_requests import setup
            await setup(bot)
            print('MentorRequest cog loaded successfully')
        except Exception as e:
            logger.error(f'Failed to load MentorRequest cog: {e}')
    
    # Run the async setup before starting the bot
    asyncio.run(setup_bot())

    if not TOKEN:
        logger.error("DISCORD_BOT_TOKEN not found in environment variables")
        exit(1)

    if GUILD_ID == 0:
        logger.error("DISCORD_GUILD_ID not found or invalid in environment variables")
        exit(1)

    logger.info("Starting HiveHelper Discord Bot...")
    try:
        bot.run(TOKEN)
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
