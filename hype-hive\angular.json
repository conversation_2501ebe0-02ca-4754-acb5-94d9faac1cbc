{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"hype-hive": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/hype-hive", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "outputMode": "server", "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "10kB", "maximumError": "16kB"}], "allowedCommonJsDependencies": ["debug", "socket.io-client", "socket.io-parser", "engine.io-client", "xmlhttprequest-ssl"], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "deleteOutputPath": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"buildTarget": "hype-hive:build:production", "host": "0.0.0.0", "disableHostCheck": true}, "local": {"buildTarget": "hype-hive:build:local", "host": "0.0.0.0", "disableHostCheck": true}, "development": {"buildTarget": "hype-hive:build:development", "host": "0.0.0.0", "disableHostCheck": true}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "d050beb9-9a20-45dd-a84d-c08662964c8d"}}