# HypeHive Tunnel Troubleshooting Guide

## Problem Analysis
The tunnel errors you're experiencing show "Incoming request ended abruptly: context canceled" which indicates connectivity issues between Cloudflare tunnel and your local server.

## Common Issues & Solutions

### 1. Local Server Not Running
**Symptoms:** 502 Bad Gateway, connection refused
**Solution:** 
```bash
npm start
# or
ng serve --port 4200
```

### 2. Backend Service Issues
**Symptoms:** API calls failing
**Solution:**
```bash
cd backend
npm start
# or
node server.js --port 3000
```

### 3. Tunnel Configuration
**Symptoms:** Persistent connection errors
**Solution:** Check cloudflared config and restart tunnel

## Health Check Commands
```bash
# Check services
node health-check.js

# PowerShell diagnostics
.\tunnel-fix.ps1 -CheckOnly

# Manual port checks
netstat -an | findstr :4200
netstat -an | findstr :3000
```

## Quick Fix Steps
1. Ensure both frontend (4200) and backend (3000) are running
2. Test local endpoints: http://localhost:4200 and http://localhost:3000
3. Restart cloudflared tunnel if needed
4. Check firewall/antivirus blocking local ports