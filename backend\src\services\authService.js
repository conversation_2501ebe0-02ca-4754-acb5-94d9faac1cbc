const axios = require('axios');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const discordConfig = require('../config/discord');
const logger = require('../config/logger');

/**
 * Exchange authorization code for Discord access token
 * @param {string} code - The authorization code from Discord OAuth
 * @returns {Promise<Object>} - The Discord OAuth token response
 */
const exchangeCodeForToken = async (code) => {
  try {
    const params = new URLSearchParams({
      client_id: discordConfig.clientId,
      client_secret: discordConfig.clientSecret,
      grant_type: 'authorization_code',
      code,
      redirect_uri: discordConfig.redirectUri,
      scope: discordConfig.scope
    });

    const response = await axios.post(discordConfig.tokenURL, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return response.data;
  } catch (error) {
    if (error.response) {
      logger.error(`Error exchanging code for token: ${error.message} | Discord response: ${JSON.stringify(error.response.data)}`);
    } else {
      logger.error(`Error exchanging code for token: ${error.message}`);
    }
    throw new Error('Failed to exchange authorization code for token');
  }
};

/**
 * Get Discord user profile using access token
 * @param {string} accessToken - The Discord access token
 * @returns {Promise<Object>} - The Discord user profile
 */
const getDiscordUserProfile = async (accessToken) => {
  try {
    const response = await axios.get(discordConfig.userURL, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    return response.data;
  } catch (error) {
    logger.error(`Error getting Discord user profile: ${error.message}`);
    throw new Error('Failed to get Discord user profile');
  }
};

/**
 * Find or create user from Discord profile
 * @param {Object} discordProfile - The Discord user profile
 * @returns {Promise<Object>} - The user document
 */
const findOrCreateUser = async (discordProfile) => {
  try {
    // Find existing user
    let user = await User.findOne({ discordId: discordProfile.id });

    // If user doesn't exist, create a new one
    if (!user) {
      user = new User({
        discordId: discordProfile.id,
        discordUsername: discordProfile.username,
        discordAvatar: discordProfile.avatar 
          ? `https://cdn.discordapp.com/avatars/${discordProfile.id}/${discordProfile.avatar}.png` 
          : null,
        email: discordProfile.email
      });

      // Create default proficiencies
      const categories = [
        'Account Setup',
        'Bots',
        'Networking',
        'Emotes',
        'Streaming Platforms',
        'Affiliate Roles',
        'Console Help',
        'PC Help'
      ];

      user.proficiencies = categories.map(category => ({
        name: category,
        category,
        isSelected: false
      }));

      await user.save();
      logger.info(`New user created: ${user.discordUsername} (${user.discordId})`);
    } else {
      // Update existing user information
      user.discordUsername = discordProfile.username;
      user.discordAvatar = discordProfile.avatar 
        ? `https://cdn.discordapp.com/avatars/${discordProfile.id}/${discordProfile.avatar}.png` 
        : user.discordAvatar;
      user.email = discordProfile.email || user.email;
      user.lastLogin = new Date();
      
      await user.save();
      logger.info(`User logged in: ${user.discordUsername} (${user.discordId})`);
    }

    // Check if user has the admin role in the configured Discord guild
    try {
      const guildMemberRes = await axios.get(
        `${discordConfig.apiEndpoint}/guilds/${discordConfig.adminGuildId}/members/${discordProfile.id}`,
        { headers: { Authorization: `Bot ${discordConfig.botToken}` } }
      );
      const roles = guildMemberRes.data.roles || [];
      if (roles.includes(discordConfig.adminRoleId)) {
        // Grant admin role on website
        if (user.role !== 'admin') {
          user.role = 'admin';
          user.isMentor = true;
          await user.save();
          logger.info(`User granted admin role via Discord guild membership: ${user.discordUsername} (${user.discordId})`);
        }
      }
    } catch (err) {
      logger.warn(`Failed to fetch Discord guild member for admin check: ${err.message}`);
    }
    return user;
  } catch (error) {
    logger.error(`Error finding or creating user: ${error.message}`);
    throw new Error('Failed to process user data');
  }
};

/**
 * Generate JWT token for user
 * @param {Object} user - The user document
 * @returns {string} - The JWT token
 */
const generateToken = (user) => {
  return jwt.sign(
    {
      id: user._id,
      discordId: user.discordId,
      role: user.role
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

/**
 * Generate JWT token for user (alias for generateToken)
 * @param {Object} user - The user document
 * @returns {string} - The JWT token
 */
const generateJWT = (user) => {
  return jwt.sign(
    {
      userId: user._id,
      discordId: user.discordId,
      username: user.discordUsername,
      role: user.role
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

/**
 * Verify JWT token
 * @param {string} token - The JWT token to verify
 * @returns {Object} - The decoded token payload
 */
const verifyJWT = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

module.exports = {
  exchangeCodeForToken,
  getDiscordUserProfile,
  findOrCreateUser,
  generateToken,
  generateJWT,
  verifyJWT
};
