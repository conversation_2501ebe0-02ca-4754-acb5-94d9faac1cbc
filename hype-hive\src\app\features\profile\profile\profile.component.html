<div class="profile-container">
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner></mat-spinner>
      <p>Loading profile...</p>
    </div>
  } @else if (errorMessage) {
    <div class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="loadUserProfile()">Try Again</button>
    </div>
  } @else if (user) {
    <div class="profile-content">
      <mat-card class="profile-card">
        <mat-card-header>
          <div mat-card-avatar class="profile-avatar" [style.background-image]="user.discordAvatar ? 'url(' + user.discordAvatar + ')' : ''">
            @if (!user.discordAvatar) {
              <mat-icon>person</mat-icon>
            }
          </div>
          <mat-card-title>{{ user.discordUsername }}</mat-card-title>
          <mat-card-subtitle>
            @if (user.isMentor) {
              <span class="mentor-badge">Mentor</span>
            } @else {
              <span>Learner</span>
            }
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Mentor Application Section -->
          @if (!user.isMentor) {
            <div class="mentor-application-section">
              <h3>Mentor Application</h3>

              @if (user.mentorApplication?.status && user.mentorApplication?.status !== 'none') {
                <div class="application-status">
                  <div class="status-badge" [ngClass]="'status-' + user.mentorApplication?.status">
                    <mat-icon>
                      @switch (user.mentorApplication?.status) {
                        @case ('pending') { schedule }
                        @case ('approved') { check_circle }
                        @case ('rejected') { cancel }
                      }
                    </mat-icon>
                    {{ getMentorApplicationStatusText() }}
                  </div>

                  @if (user.mentorApplication?.status === 'pending') {
                    <p class="status-description">
                      Your mentor application is being reviewed by our team. We'll notify you once a decision has been made.
                    </p>
                  } @else if (user.mentorApplication?.status === 'rejected') {
                    <p class="status-description">
                      Your previous application was not approved. You can submit a new application if you'd like to try again.
                    </p>
                  }
                </div>
              } @else {
                <p class="mentor-info">
                  Become a mentor and help others in the streaming community! Share your knowledge and experience with fellow streamers.
                </p>
              }

              @if (canApplyForMentor()) {
                <button mat-raised-button color="primary" (click)="applyForMentor()" class="apply-button">
                  <mat-icon>school</mat-icon>
                  Apply to be a Mentor
                </button>
              }
            </div>

            <mat-divider></mat-divider>
          }

          <div class="profile-section">
            <h3>Proficiencies</h3>

            @for (category of instructionCategories; track category) {
              @if (hasProficienciesInCategory(category)) {
                <div class="category-section">
                  <h4>{{ category }}</h4>
                  <div class="proficiency-chips">
                    @for (proficiency of getProficienciesForCategory(category); track proficiency) {
                      <mat-chip>{{ proficiency }}</mat-chip>
                    }
                  </div>
                </div>
              }
            }

            @if (!hasSelectedProficiencies()) {
              <p class="no-proficiencies">No proficiencies selected yet.</p>
            }
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button color="primary" routerLink="/profile/edit">
            <mat-icon>edit</mat-icon> Edit Profile
          </button>
          <button mat-button color="warn" (click)="logout()">
            <mat-icon>logout</mat-icon> Logout
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  } @else {
    <div class="not-authenticated">
      <mat-icon color="warn">lock</mat-icon>
      <h2>Not Authenticated</h2>
      <p>Please log in to view your profile.</p>
      <button mat-raised-button color="primary" routerLink="/auth/login">Login</button>
    </div>
  }
</div>
