#!/usr/bin/env node

/**
 * Security audit script for HypeHive backend
 * Checks for common security vulnerabilities and misconfigurations
 */

const fs = require('fs');
const path = require('path');
const { validateSecurityConfig } = require('../src/config/security');

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
  }

  addIssue(category, message, severity = 'medium') {
    this.issues.push({ category, message, severity });
  }

  addWarning(category, message) {
    this.warnings.push({ category, message });
  }

  addPassed(category, message) {
    this.passed.push({ category, message });
  }

  // Check environment configuration
  checkEnvironmentConfig() {
    console.log('🔍 Checking environment configuration...');
    
    const envFiles = ['.env', '.env.development', '.env.production', '.env.test'];
    
    envFiles.forEach(envFile => {
      const envPath = path.join(__dirname, '..', envFile);
      
      if (fs.existsSync(envPath)) {
        const content = fs.readFileSync(envPath, 'utf8');
        
        // Check for exposed secrets
        const secretPatterns = [
          { pattern: /DISCORD_CLIENT_SECRET=.{10,}/, name: 'Discord Client Secret' },
          { pattern: /DISCORD_BOT_TOKEN=.{10,}/, name: 'Discord Bot Token' },
          { pattern: /JWT_SECRET=.{10,}/, name: 'JWT Secret' },
          { pattern: /MONGODB_URI=.+/, name: 'MongoDB URI' }
        ];
        
        secretPatterns.forEach(({ pattern, name }) => {
          if (pattern.test(content)) {
            if (content.includes('REPLACE_WITH') || content.includes('YOUR_')) {
              this.addPassed('Environment', `${name} properly templated in ${envFile}`);
            } else {
              this.addWarning('Environment', `${name} found in ${envFile} - ensure it's not committed to version control`);
            }
          }
        });
        
        // Check for weak JWT secrets
        const jwtMatch = content.match(/JWT_SECRET=(.+)/);
        if (jwtMatch) {
          const secret = jwtMatch[1].trim();
          if (secret.length < 32) {
            this.addIssue('Environment', `JWT secret in ${envFile} is too short (${secret.length} chars, minimum 32)`, 'high');
          } else if (['secret', 'password', 'test', 'dev'].some(weak => secret.toLowerCase().includes(weak))) {
            this.addIssue('Environment', `JWT secret in ${envFile} contains weak patterns`, 'medium');
          } else {
            this.addPassed('Environment', `JWT secret in ${envFile} appears strong`);
          }
        }
      }
    });
  }

  // Check for hardcoded secrets in source code
  checkSourceCodeSecrets() {
    console.log('🔍 Checking source code for hardcoded secrets...');
    
    const scanDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !['node_modules', '.git', 'coverage', 'dist'].includes(file)) {
          scanDirectory(filePath);
        } else if (stat.isFile() && ['.js', '.ts', '.json'].includes(path.extname(file))) {
          const content = fs.readFileSync(filePath, 'utf8');
          
          // Check for potential secrets
          const secretPatterns = [
            /discord.*secret.*[=:]\s*['"][^'"]{20,}['"]/i,
            /bot.*token.*[=:]\s*['"][^'"]{50,}['"]/i,
            /jwt.*secret.*[=:]\s*['"][^'"]{20,}['"]/i,
            /api.*key.*[=:]\s*['"][^'"]{20,}['"]/i,
            /password.*[=:]\s*['"][^'"]{8,}['"]/i
          ];
          
          secretPatterns.forEach(pattern => {
            if (pattern.test(content)) {
              this.addIssue('Source Code', `Potential hardcoded secret in ${filePath}`, 'high');
            }
          });
        }
      });
    };
    
    scanDirectory(path.join(__dirname, '..', 'src'));
  }

  // Check dependency vulnerabilities
  checkDependencies() {
    console.log('🔍 Checking dependencies...');
    
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // Check for known vulnerable packages (simplified check)
      const vulnerablePackages = [
        'lodash', // Check for old versions
        'moment', // Deprecated
        'request' // Deprecated
      ];
      
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      vulnerablePackages.forEach(pkg => {
        if (allDeps[pkg]) {
          this.addWarning('Dependencies', `Package '${pkg}' may have known vulnerabilities - check with npm audit`);
        }
      });
      
      // Check for security-related packages
      const securityPackages = ['helmet', 'express-rate-limit', 'express-mongo-sanitize'];
      securityPackages.forEach(pkg => {
        if (allDeps[pkg]) {
          this.addPassed('Dependencies', `Security package '${pkg}' is installed`);
        } else {
          this.addWarning('Dependencies', `Consider installing security package '${pkg}'`);
        }
      });
    }
  }

  // Check file permissions and structure
  checkFilePermissions() {
    console.log('🔍 Checking file permissions...');
    
    const sensitiveFiles = ['.env', '.env.production', 'package.json'];
    
    sensitiveFiles.forEach(file => {
      const filePath = path.join(__dirname, '..', file);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const mode = stats.mode & parseInt('777', 8);
        
        if (mode & parseInt('044', 8)) { // World readable
          this.addWarning('File Permissions', `${file} is world-readable`);
        } else {
          this.addPassed('File Permissions', `${file} has appropriate permissions`);
        }
      }
    });
  }

  // Check security middleware configuration
  checkSecurityMiddleware() {
    console.log('🔍 Checking security middleware...');
    
    const appJsPath = path.join(__dirname, '..', 'src', 'app.js');
    
    if (fs.existsSync(appJsPath)) {
      const content = fs.readFileSync(appJsPath, 'utf8');
      
      const securityChecks = [
        { pattern: /helmet/, name: 'Helmet security headers' },
        { pattern: /cors/, name: 'CORS configuration' },
        { pattern: /mongoSanitize/, name: 'MongoDB sanitization' },
        { pattern: /express\.json.*limit/, name: 'Request size limiting' }
      ];
      
      securityChecks.forEach(({ pattern, name }) => {
        if (pattern.test(content)) {
          this.addPassed('Middleware', `${name} is configured`);
        } else {
          this.addWarning('Middleware', `${name} may not be configured`);
        }
      });
    }
  }

  // Run all security checks
  async runAudit() {
    console.log('🛡️  Starting HypeHive Security Audit\n');
    
    this.checkEnvironmentConfig();
    this.checkSourceCodeSecrets();
    this.checkDependencies();
    this.checkFilePermissions();
    this.checkSecurityMiddleware();
    
    // Run security config validation
    console.log('🔍 Validating security configuration...');
    if (validateSecurityConfig()) {
      this.addPassed('Configuration', 'Security configuration validation passed');
    } else {
      this.addIssue('Configuration', 'Security configuration validation failed', 'high');
    }
    
    this.generateReport();
  }

  // Generate audit report
  generateReport() {
    console.log('\n📊 Security Audit Report\n');
    console.log('=' .repeat(50));
    
    // Critical issues
    const criticalIssues = this.issues.filter(issue => issue.severity === 'high');
    if (criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES (Immediate Action Required):');
      criticalIssues.forEach(issue => {
        console.log(`   ❌ [${issue.category}] ${issue.message}`);
      });
    }
    
    // Medium issues
    const mediumIssues = this.issues.filter(issue => issue.severity === 'medium');
    if (mediumIssues.length > 0) {
      console.log('\n⚠️  MEDIUM ISSUES:');
      mediumIssues.forEach(issue => {
        console.log(`   🔶 [${issue.category}] ${issue.message}`);
      });
    }
    
    // Warnings
    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach(warning => {
        console.log(`   🔸 [${warning.category}] ${warning.message}`);
      });
    }
    
    // Passed checks
    if (this.passed.length > 0) {
      console.log('\n✅ PASSED CHECKS:');
      this.passed.forEach(passed => {
        console.log(`   ✓ [${passed.category}] ${passed.message}`);
      });
    }
    
    // Summary
    console.log('\n📈 SUMMARY:');
    console.log(`   Critical Issues: ${criticalIssues.length}`);
    console.log(`   Medium Issues: ${mediumIssues.length}`);
    console.log(`   Warnings: ${this.warnings.length}`);
    console.log(`   Passed Checks: ${this.passed.length}`);
    
    const totalIssues = this.issues.length + this.warnings.length;
    if (totalIssues === 0) {
      console.log('\n🎉 No security issues found!');
    } else if (criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL: Address high-severity issues immediately!');
      process.exit(1);
    } else {
      console.log('\n✅ No critical issues found, but review warnings and medium issues.');
    }
  }
}

// Run the audit
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.runAudit().catch(console.error);
}

module.exports = SecurityAuditor;
