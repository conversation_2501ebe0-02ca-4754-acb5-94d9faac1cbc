<div class="chat-list-container">
  <div class="chat-list-header">
    <h1>Messages</h1>
    <button mat-fab color="primary" (click)="createNewChat()" matTooltip="New Message">
      <mat-icon>add</mat-icon>
    </button>
  </div>

  <div class="connection-status" *ngIf="!socketConnected && currentUser">
    <mat-icon color="warn">wifi_off</mat-icon>
    <span>Disconnected. Reconnecting...</span>
  </div>

  <div class="auth-required" *ngIf="!currentUser">
    <mat-icon color="primary">login</mat-icon>
    <span>Please log in to access chat features</span>
  </div>

  <div class="chat-list-content">
    <ng-container *ngIf="isLoading && chats.length === 0">
      <div class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading chats...</p>
      </div>
    </ng-container>

    <ng-container *ngIf="errorMessage">
      <div class="error-container">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="loadChats()">Try Again</button>
      </div>
    </ng-container>

    <ng-container *ngIf="!isLoading && !errorMessage && chats.length === 0">
      <div class="empty-state">
        <mat-icon>chat</mat-icon>
        <h3>No messages yet</h3>
        <p>Start a conversation with a mentor or another user</p>
        <button mat-raised-button color="primary" (click)="createNewChat()">
          <mat-icon>add</mat-icon>
          New Message
        </button>
      </div>
    </ng-container>

    <mat-nav-list *ngIf="chats.length > 0">
      <a mat-list-item *ngFor="let chat of chats" (click)="openChat(chat._id)" class="chat-item">
        <div class="chat-avatar">
          <img [src]="getChatAvatar(chat)" 
               (error)="$any($event.target).src='/helplogo.png'"
               alt="User Avatar" 
               class="avatar-img">
          <div class="discord-indicator" *ngIf="hasDiscordAvatar(chat)" matTooltip="Discord User">
            <mat-icon>chat</mat-icon>
          </div>
        </div>
        <div class="chat-info">
          <div class="chat-header">
            <h3 class="chat-name">{{ getChatDisplayName(chat) }}</h3>
            <span class="chat-time">{{ formatDate(chat.lastMessage?.createdAt) }}</span>
          </div>
          <div class="chat-preview">
            <p class="message-preview" [class.unread]="hasUnreadMessages(chat)">
              {{ getLastMessagePreview(chat) }}
            </p>
            <div class="unread-badge" *ngIf="hasUnreadMessages(chat)">
              <span class="unread-count">{{ getUnreadCount(chat) }}</span>
            </div>
          </div>
        </div>
      </a>
    </mat-nav-list>

    <div class="load-more" *ngIf="hasMoreChats">
      <button mat-button color="primary" (click)="loadMoreChats()" [disabled]="isLoading">
        <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
        <span *ngIf="isLoading">Loading...</span>
        <span *ngIf="!isLoading">Load More</span>
      </button>
    </div>
  </div>
</div>
