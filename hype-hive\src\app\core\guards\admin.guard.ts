import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, map, take, combineLatest } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return combineLatest([
      this.authService.isAuthenticated$,
      this.authService.currentUser$
    ]).pipe(
      take(1),
      map(([isAuthenticated, user]) => {
        if (!isAuthenticated) {
          this.router.navigate(['/auth/login'], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        }

        if (!user || user.role !== 'admin') {
          this.router.navigate(['/'], {
            queryParams: { error: 'Access denied. Admin privileges required.' }
          });
          return false;
        }

        return true;
      })
    );
  }
}
