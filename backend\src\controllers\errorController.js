const logger = require('../config/logger');
const { AppError, catchAsync } = require('../middleware/errorHandler');

/**
 * Log client-side errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const logClientError = catchAsync(async (req, res, next) => {
  const { message, stack, timestamp, url, userAgent, type } = req.body;

  // Validate required fields
  if (!message) {
    return next(new AppError('Error message is required', 400));
  }

  // Sanitize and prepare error data
  const errorData = {
    message: message.substring(0, 1000), // Limit message length
    stack: stack ? stack.substring(0, 5000) : '', // Limit stack trace length
    timestamp: timestamp || new Date().toISOString(),
    url: url ? url.substring(0, 500) : '', // Limit URL length
    userAgent: userAgent ? userAgent.substring(0, 500) : '', // Limit user agent length
    type: type || 'client-error',
    userId: req.user ? req.user._id : 'anonymous',
    ip: req.ip
  };

  // Log the client error
  logger.error('Client-side error reported:', errorData);

  res.status(200).json({
    success: true,
    message: 'Error logged successfully'
  });
});

/**
 * Get error statistics (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getErrorStats = catchAsync(async (req, res, next) => {
  // This would typically query a database or log aggregation service
  // For now, return a placeholder response
  const stats = {
    totalErrors: 0,
    errorsByType: {},
    recentErrors: [],
    errorTrends: []
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

module.exports = {
  logClientError,
  getErrorStats
};
