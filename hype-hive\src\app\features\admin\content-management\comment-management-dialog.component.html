<h1 mat-dialog-title>Manage Comments</h1>
<div mat-dialog-content>
  <table mat-table [dataSource]="comments" class="full-width mat-elevation-z8">

    <ng-container matColumnDef="user">
      <th mat-header-cell *matHeaderCellDef> User </th>
      <td mat-cell *matCellDef="let comment"> {{ comment.user?.username || comment.user }} </td>
    </ng-container>

    <ng-container matColumnDef="text">
      <th mat-header-cell *matHeaderCellDef> Comment </th>
      <td mat-cell *matCellDef="let comment"> {{ comment.text }} </td>
    </ng-container>

    <ng-container matColumnDef="createdAt">
      <th mat-header-cell *matHeaderCellDef> Date </th>
      <td mat-cell *matCellDef="let comment"> {{ comment.createdAt | date }} </td>
    </ng-container>

    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef> Actions </th>
      <td mat-cell *matCellDef="let comment">
        <button mat-icon-button color="accent" (click)="editComment(comment._id, comment.text)">
          <mat-icon>edit</mat-icon>
        </button>
        <button mat-icon-button color="warn" (click)="deleteComment(comment._id)">
          <mat-icon>delete</mat-icon>
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
</div>
<div mat-dialog-actions align="end">
  <button mat-button (click)="close()">Close</button>
</div>
