version: '3.8'

services:
  discord-bot:
    build: .
    container_name: hive-discord-bot
    restart: unless-stopped
    environment:
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - DISCORD_GUILD_ID=${DISCORD_GUILD_ID}
      - BACKEND_API_URL=${BACKEND_API_URL:-http://backend:3000/api}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - hype-hive-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "python", "-c", "import os; exit(0 if os.path.exists('/app/discord_bot.log') else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Backend service (if running in same compose)
  backend:
    image: hype-hive-backend:latest
    container_name: hype-hive-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - DISCORD_CLIENT_SECRET=${DISCORD_CLIENT_SECRET}
    ports:
      - "3000:3000"
    networks:
      - hype-hive-network
    depends_on:
      - mongodb

  # MongoDB service (if needed)
  mongodb:
    image: mongo:7
    container_name: hype-hive-mongodb
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - hype-hive-network

networks:
  hype-hive-network:
    driver: bridge

volumes:
  mongodb_data:
