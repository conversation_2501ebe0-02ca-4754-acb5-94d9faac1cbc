const Notification = require('../models/Notification');
const User = require('../models/User');
const logger = require('../config/logger');

/**
 * Create a new notification
 * @param {Object} notificationData - Notification data
 * @returns {Promise<Object>} - Created notification document
 */
const createNotification = async (notificationData) => {
  try {
    const notification = new Notification(notificationData);
    await notification.save();
    
    // Populate sender info
    if (notification.sender) {
      await notification.populate('sender', 'discordUsername discordAvatar');
    }
    
    return notification;
  } catch (error) {
    logger.error(`Error creating notification: ${error.message}`);
    throw new Error('Failed to create notification');
  }
};

/**
 * Get notifications for a user
 * @param {string} userId - User ID
 * @param {Object} query - Query parameters
 * @returns {Promise<Array>} - Array of notifications
 */
const getUserNotifications = async (userId, query = {}) => {
  try {
    const filter = { recipient: userId };
    
    // Filter by read status if provided
    if (query.isRead !== undefined) {
      filter.isRead = query.isRead === 'true';
    }
    
    // Filter by type if provided
    if (query.type) {
      filter.type = query.type;
    }
    
    // Pagination
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 20;
    const skip = (page - 1) * limit;
    
    // Execute query with aggregation for better performance
    const aggregationPipeline = [
      { $match: filter },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'users',
          localField: 'sender',
          foreignField: '_id',
          as: 'senderInfo'
        }
      },
      {
        $project: {
          _id: 1,
          recipient: 1,
          type: 1,
          content: 1,
          isRead: 1,
          relatedContent: 1,
          createdAt: 1,
          updatedAt: 1,
          'senderInfo._id': 1,
          'senderInfo.discordUsername': 1,
          'senderInfo.discordAvatar': 1
        }
      }
    ];
    
    const notifications = await Notification.aggregate(aggregationPipeline);
    
    // Get total count
    const total = await Notification.countDocuments(filter);
    
    return {
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting user notifications: ${error.message}`);
    throw new Error('Failed to get notifications');
  }
};

/**
 * Mark notification as read
 * @param {string} notificationId - Notification ID
 * @param {string} userId - User ID (for authorization)
 * @returns {Promise<Object>} - Updated notification document
 */
const markAsRead = async (notificationId, userId) => {
  try {
    const notification = await Notification.findOneAndUpdate(
      { _id: notificationId, recipient: userId },
      { $set: { isRead: true } },
      { new: true }
    );
    
    if (!notification) {
      throw new Error('Notification not found or you do not have access');
    }
    
    return notification;
  } catch (error) {
    logger.error(`Error marking notification as read: ${error.message}`);
    throw new Error(error.message || 'Failed to mark notification as read');
  }
};

/**
 * Mark all notifications as read for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Result with count of updated notifications
 */
const markAllAsRead = async (userId) => {
  try {
    const result = await Notification.updateMany(
      { recipient: userId, isRead: false },
      { $set: { isRead: true } }
    );
    
    return {
      success: true,
      count: result.modifiedCount
    };
  } catch (error) {
    logger.error(`Error marking all notifications as read: ${error.message}`);
    throw new Error('Failed to mark all notifications as read');
  }
};

/**
 * Delete a notification
 * @param {string} notificationId - Notification ID
 * @param {string} userId - User ID (for authorization)
 * @returns {Promise<boolean>} - Success status
 */
const deleteNotification = async (notificationId, userId) => {
  try {
    const result = await Notification.deleteOne({
      _id: notificationId,
      recipient: userId
    });
    
    if (result.deletedCount === 0) {
      throw new Error('Notification not found or you do not have access');
    }
    
    return true;
  } catch (error) {
    logger.error(`Error deleting notification: ${error.message}`);
    throw new Error(error.message || 'Failed to delete notification');
  }
};

/**
 * Create a like notification
 * @param {string} contentId - Content ID
 * @param {string} senderId - Sender user ID
 * @param {string} recipientId - Recipient user ID
 * @returns {Promise<Object>} - Created notification document
 */
const createLikeNotification = async (contentId, senderId, recipientId) => {
  try {
    // Don't notify yourself
    if (senderId === recipientId) {
      return null;
    }
    
    const sender = await User.findById(senderId).select('discordUsername');
    
    if (!sender) {
      throw new Error('Sender not found');
    }
    
    const notificationData = {
      recipient: recipientId,
      sender: senderId,
      type: 'like',
      content: `${sender.discordUsername} liked your content`,
      relatedContent: {
        contentType: 'content',
        contentId: contentId
      }
    };
    
    return await createNotification(notificationData);
  } catch (error) {
    logger.error(`Error creating like notification: ${error.message}`);
    throw new Error('Failed to create like notification');
  }
};

module.exports = {
  createNotification,
  getUserNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createLikeNotification
};
