<div class="recommended-content-container">
  <div class="section-header">
    <h2>{{ isAuthenticated ? 'Recommended For You' : 'Popular Content' }}</h2>
    <a routerLink="/content" class="view-all">View All</a>
  </div>
  
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading recommendations...</p>
    </div>
  } @else if (errorMessage) {
    <div class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="loadRecommendedContent()">Try Again</button>
    </div>
  } @else if (recommendedContent.length === 0) {
    <div class="no-content">
      <mat-icon>video_library</mat-icon>
      <p>No content available yet. Check back later!</p>
    </div>
  } @else {
    <div class="content-grid">
      @for (content of recommendedContent; track content._id) {
        <mat-card class="content-card" [routerLink]="['/content', content._id]" (click)="trackContentView(content._id)">
          <div class="content-thumbnail" [style.background-image]="content.thumbnailUrl ? 'url(' + content.thumbnailUrl + ')' : ''">
            @if (!content.thumbnailUrl) {
              <mat-icon>video_library</mat-icon>
            }
            <div class="content-category">{{ content.category }}</div>
          </div>
          <mat-card-content>
            <h3 class="content-title">{{ content.title }}</h3>
            <p class="content-description">{{ content.description.substring(0, 100) }}{{ content.description.length > 100 ? '...' : '' }}</p>
            <div class="content-meta">
              <div class="content-author">
                <div class="author-avatar" [style.background-image]="content.createdBy.discordAvatar ? 'url(' + content.createdBy.discordAvatar + ')' : ''">
                  @if (!content.createdBy.discordAvatar) {
                    <mat-icon>person</mat-icon>
                  }
                </div>
                <span>{{ content.createdBy.discordUsername }}</span>
              </div>
              <div class="content-stats">
                <span class="views"><mat-icon>visibility</mat-icon> {{ content.views }}</span>
                <span class="likes"><mat-icon>favorite</mat-icon> {{ content.likes }}</span>
              </div>
            </div>
            <div class="content-difficulty" [ngClass]="getDifficultyClass(content.difficulty)">
              {{ content.difficulty }}
            </div>
          </mat-card-content>
        </mat-card>
      }
    </div>
  }
</div>
