<header class="app-header">
  <div class="header-content">
    <div class="header-left">
      <div class="logo-container">
        <a routerLink="/">
          <img src="/hivelogo.gif" alt="HypeHive Logo" />
        </a>
      </div>
      <nav class="dropdown-nav">
        <ul>
          <li><a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a></li>
          <li><a routerLink="/content" routerLinkActive="active">Content</a></li>
          <li><a routerLink="/mentors" routerLinkActive="active">Mentors</a></li>
          <li *ngIf="isAuthenticated"><a routerLink="/chat" routerLinkActive="active">Messages</a></li>
        </ul>
      </nav>
    </div>

    <div class="header-center">
      <img src="/hive-helper-logo.png" alt="Hive Helper" />
    </div>

    <div class="header-right">
      <div class="user-actions">
        <ng-container *ngIf="!isAuthenticated">
          <button mat-raised-button color="primary" routerLink="/auth/login">
            <mat-icon>login</mat-icon>
            Login
          </button>
        </ng-container>

        <ng-container *ngIf="isAuthenticated">
          <!-- Notifications -->
          <div class="notifications-container">
            <div (click)="toggleNotifications()" class="notification-trigger">
              <app-notification-badge></app-notification-badge>
            </div>

            <div class="notifications-dropdown" *ngIf="isNotificationsOpen">
              <app-notification-list></app-notification-list>
            </div>
          </div>

          <!-- User Menu -->
          <div class="user-menu-container">
            <div class="user-avatar" (click)="toggleMenu()">
              <img [src]="currentUser?.discordAvatar || '/helplogo.png'" alt="User Avatar">
            </div>

            <div class="user-dropdown" *ngIf="isMenuOpen">
              <div class="user-info">
                <div class="user-avatar-large">
                  <img [src]="currentUser?.discordAvatar || '/helplogo.png'" alt="User Avatar">
                </div>
                <div class="user-details">
                  <h3>{{ currentUser?.discordUsername }}</h3>
                  <span *ngIf="currentUser?.isMentor" class="mentor-badge">Mentor</span>
                </div>
              </div>

              <mat-divider></mat-divider>

              <div class="menu-items">
                <a routerLink="/profile" (click)="isMenuOpen = false">
                  <mat-icon>person</mat-icon>
                  <span>Profile</span>
                </a>

                <a routerLink="/settings" (click)="isMenuOpen = false">
                  <mat-icon>settings</mat-icon>
                  <span>Settings</span>
                </a>

                <button (click)="logout()">
                  <mat-icon>logout</mat-icon>
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</header>

<!-- Backdrop for closing dropdowns -->
<div
  class="backdrop"
  *ngIf="isMenuOpen || isNotificationsOpen"
  (click)="isMenuOpen = false; isNotificationsOpen = false">
</div>
