<div class="mentor-search-container">
  <div class="search-header">
    <div class="header-content">
      <h1>Find a Mentor</h1>
      <p>Connect with experienced streamers who can help you grow your channel</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="/mentors/apply" class="become-mentor-btn">
        <mat-icon>school</mat-icon>
        Become a Mentor
      </button>
    </div>
  </div>
  
  <div class="search-filters">
    <form [formGroup]="searchForm" class="search-form">
      <mat-form-field appearance="outline" class="search-input">
        <mat-label>Search mentors</mat-label>
        <input matInput formControlName="search" placeholder="Search by name or skills">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      
      <mat-form-field appearance="outline" class="category-select">
        <mat-label>Filter by category</mat-label>
        <mat-select formControlName="category">
          <mat-option value="">All Categories</mat-option>
          @for (category of categories; track category) {
            <mat-option [value]="category">{{ category }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
      
      <button mat-stroked-button color="primary" (click)="resetFilters()">
        <mat-icon>refresh</mat-icon> Reset
      </button>
    </form>
  </div>
  
  @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading mentors...</p>
    </div>
  } @else if (errorMessage) {
    <div class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="loadMentors()">Try Again</button>
    </div>
  } @else if (filteredMentors.length === 0) {
    <div class="no-results">
      <mat-icon>search_off</mat-icon>
      <h3>No mentors found</h3>
      <p>Try adjusting your search filters or check back later</p>
    </div>
  } @else {
    <div class="mentors-grid">
      @for (mentor of filteredMentors; track mentor._id) {
        <app-mentor-card [mentor]="mentor"></app-mentor-card>
      }
    </div>
    
    <mat-paginator
      [length]="totalMentors"
      [pageSize]="pageSize"
      [pageSizeOptions]="pageSizeOptions"
      [pageIndex]="pageIndex"
      (page)="onPageChange($event)"
      aria-label="Select page">
    </mat-paginator>
  }
</div>
