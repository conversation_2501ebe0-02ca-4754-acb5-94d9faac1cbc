import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

export interface VersionInfo {
  version: string;
  buildDate: string;
  buildNumber: string;
  gitCommit: string;
  environment: string;
}

@Injectable({
  providedIn: 'root'
})
export class VersionService {
  private versionInfo$ = new BehaviorSubject<VersionInfo | null>(null);
  private updateAvailable$ = new BehaviorSubject<boolean>(false);

  constructor(private http: HttpClient) {
    this.loadVersionInfo();
  }

  /**
   * Load version information from assets
   */
  private loadVersionInfo(): void {
    this.http.get<VersionInfo>('/assets/version.json')
      .pipe(
        catchError(() => of(null))
      )
      .subscribe(version => {
        if (version) {
          this.versionInfo$.next(version);
          this.checkForUpdates();
        }
      });
  }

  /**
   * Get version information
   */
  getVersionInfo(): Observable<VersionInfo | null> {
    return this.versionInfo$.asObservable();
  }

  /**
   * Get current version string
   */
  getCurrentVersion(): string {
    return this.versionInfo$.value?.version || 'Unknown';
  }

  /**
   * Get build date
   */
  getBuildDate(): Date | null {
    const buildDate = this.versionInfo$.value?.buildDate;
    return buildDate ? new Date(buildDate) : null;
  }

  /**
   * Get build number
   */
  getBuildNumber(): string {
    return this.versionInfo$.value?.buildNumber || 'Unknown';
  }

  /**
   * Get git commit hash
   */
  getGitCommit(): string {
    return this.versionInfo$.value?.gitCommit || 'Unknown';
  }

  /**
   * Get environment
   */
  getEnvironment(): string {
    return this.versionInfo$.value?.environment || 'Unknown';
  }

  /**
   * Check for updates by comparing with server version
   */
  private checkForUpdates(): void {
    const currentVersion = this.getCurrentVersion();
    if (currentVersion === 'Unknown') {
      return;
    }

    this.http.get<VersionInfo>('/api/version')
      .pipe(
        catchError(() => of(null))
      )
      .subscribe(serverVersion => {
        if (serverVersion && this.isNewerVersion(serverVersion.version, currentVersion)) {
          this.updateAvailable$.next(true);
        }
      });
  }

  /**
   * Check if an update is available
   */
  isUpdateAvailable(): Observable<boolean> {
    return this.updateAvailable$.asObservable();
  }

  /**
   * Compare version strings
   */
  private isNewerVersion(newVersion: string, currentVersion: string): boolean {
    const newParts = newVersion.split('.').map(Number);
    const currentParts = currentVersion.split('.').map(Number);

    for (let i = 0; i < Math.max(newParts.length, currentParts.length); i++) {
      const newPart = newParts[i] || 0;
      const currentPart = currentParts[i] || 0;

      if (newPart > currentPart) {
        return true;
      } else if (newPart < currentPart) {
        return false;
      }
    }

    return false;
  }

  /**
   * Force reload the application (for updates)
   */
  reloadApplication(): void {
    window.location.reload();
  }

  /**
   * Get version display string
   */
  getVersionDisplay(): string {
    const version = this.versionInfo$.value;
    if (!version) {
      return 'Unknown';
    }

    const parts = [version.version];
    
    if (version.buildNumber && version.buildNumber !== 'local') {
      parts.push(`Build ${version.buildNumber}`);
    }

    if (version.gitCommit && version.gitCommit !== 'unknown') {
      parts.push(`(${version.gitCommit.substring(0, 7)})`);
    }

    return parts.join(' ');
  }

  /**
   * Get full version information as string
   */
  getFullVersionInfo(): string {
    const version = this.versionInfo$.value;
    if (!version) {
      return 'Version information not available';
    }

    return `
Version: ${version.version}
Build Date: ${new Date(version.buildDate).toLocaleString()}
Build Number: ${version.buildNumber}
Git Commit: ${version.gitCommit}
Environment: ${version.environment}
    `.trim();
  }

  /**
   * Log version information to console
   */
  logVersionInfo(): void {
    const version = this.versionInfo$.value;
    if (!version) {
      console.log('Version information not available');
      return;
    }

    console.group('🚀 HypeHive Version Info');
    console.log(`Version: ${version.version}`);
    console.log(`Build Date: ${new Date(version.buildDate).toLocaleString()}`);
    console.log(`Build Number: ${version.buildNumber}`);
    console.log(`Git Commit: ${version.gitCommit}`);
    console.log(`Environment: ${version.environment}`);
    console.groupEnd();
  }

  /**
   * Get time since build
   */
  getTimeSinceBuild(): string {
    const buildDate = this.getBuildDate();
    if (!buildDate) {
      return 'Unknown';
    }

    const now = new Date();
    const diff = now.getTime() - buildDate.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
