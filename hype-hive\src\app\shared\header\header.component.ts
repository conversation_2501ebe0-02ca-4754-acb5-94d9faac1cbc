import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { Subscription } from 'rxjs';
import { AuthService } from '../../core/services/auth.service';
import { SocketService } from '../../core/services/socket.service';
import { User } from '../../core/models/user.model';
import { NotificationBadgeComponent } from '../notification-badge/notification-badge.component';
import { NotificationListComponent } from '../notification-list/notification-list.component';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    RouterLinkActive,
    MatIconModule,
    MatDividerModule,
    MatButtonModule,
    NotificationBadgeComponent,
    NotificationListComponent
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  currentUser: User | null = null;
  isMenuOpen = false;
  isNotificationsOpen = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private socketService: SocketService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Check authentication status
    this.isAuthenticated = this.authService.isAuthenticated();

    // Get current user
    if (this.isAuthenticated) {
      this.currentUser = this.authService.getCurrentUser();

      // Initialize socket connection
      this.socketService.initializeSocket();
    }

    // Subscribe to auth changes
    this.subscriptions.push(
      this.authService.isAuthenticated$.subscribe((isAuthenticated: boolean) => {
        this.isAuthenticated = isAuthenticated;

        if (isAuthenticated) {
          this.currentUser = this.authService.getCurrentUser();

          // Initialize socket connection
          this.socketService.initializeSocket();
        } else {
          this.currentUser = null;

          // Disconnect socket
          this.socketService.disconnect();
        }
      })
    );
  }

  /**
   * Toggle user menu
   */
  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;

    // Close notifications if open
    if (this.isMenuOpen && this.isNotificationsOpen) {
      this.isNotificationsOpen = false;
    }
  }

  /**
   * Toggle notifications panel
   */
  toggleNotifications(): void {
    this.isNotificationsOpen = !this.isNotificationsOpen;

    // Close menu if open
    if (this.isNotificationsOpen && this.isMenuOpen) {
      this.isMenuOpen = false;
    }
  }

  /**
   * Logout user
   */
  logout(): void {
    this.authService.logout();
    this.router.navigate(['/']);
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
