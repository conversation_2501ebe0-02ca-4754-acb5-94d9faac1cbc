import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AdminService } from '../admin.service';
import { Content } from '../../../core/services/content.service';

@Component({
  selector: 'app-comment-management-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './comment-management-dialog.component.html',
  styleUrls: ['./comment-management-dialog.component.scss']
})
export class CommentManagementDialogComponent implements OnInit {
  comments: any[] = [];
  displayedColumns: string[] = ['user', 'text', 'createdAt', 'actions'];

  constructor(
    public dialogRef: MatDialogRef<CommentManagementDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { content: Content },
    private adminService: AdminService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.comments = this.data.content.comments;
  }

  deleteComment(commentId: string): void {
    if (confirm('Are you sure you want to delete this comment?')) {
      this.adminService.deleteContentComment(this.data.content._id, commentId).subscribe({
        next: () => {
          this.comments = this.comments.filter(c => c._id !== commentId);
          this.snackBar.open('Comment deleted', 'Close', { duration: 2000 });
        },
        error: () => {
          this.snackBar.open('Failed to delete comment', 'Close', { duration: 2000 });
        }
      });
    }
  }

  editComment(commentId: string, currentText: string): void {
    const newText = prompt('Edit comment:', currentText);
    if (newText !== null && newText.trim() !== '') {
      this.adminService.editContentComment(this.data.content._id, commentId, newText.trim()).subscribe({
        next: (updated: any) => {
          const idx = this.comments.findIndex(c => c._id === commentId);
          if (idx > -1) {
            this.comments[idx].text = updated.text;
          }
          this.snackBar.open('Comment updated', 'Close', { duration: 2000 });
        },
        error: () => {
          this.snackBar.open('Failed to update comment', 'Close', { duration: 2000 });
        }
      });
    }
  }

  close(): void {
    this.dialogRef.close();
  }
}
