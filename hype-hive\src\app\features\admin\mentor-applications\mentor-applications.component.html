<div class="mentor-applications">
  <div class="page-header">
    <h1>Mentor Applications</h1>
    <button mat-raised-button color="primary" (click)="loadApplications()" [disabled]="loading">
      <mat-icon>refresh</mat-icon>
      Refresh
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading mentor applications...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-button color="primary" (click)="loadApplications()">Try Again</button>
  </div>

  <!-- No Applications -->
  <div *ngIf="!loading && !error && applications.length === 0" class="no-applications">
    <mat-icon>assignment_turned_in</mat-icon>
    <h2>No Pending Applications</h2>
    <p>All mentor applications have been reviewed.</p>
  </div>

  <!-- Applications List -->
  <div *ngIf="!loading && !error && applications.length > 0" class="applications-list">
    <mat-accordion>
      <mat-expansion-panel *ngFor="let application of applications" class="application-panel">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="application-header">
              <img [src]="application.discordAvatar" [alt]="application.discordUsername" class="avatar">
              <div class="user-info">
                <h3>{{ application.discordUsername }}</h3>
                <p>{{ application.email }}</p>
              </div>
              <mat-chip color="accent">Pending Review</mat-chip>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <div class="application-details">
          <!-- Basic Information -->
          <div class="section">
            <h4>Basic Information</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Discord Username:</label>
                <span>{{ application.discordUsername }}</span>
              </div>
              <div class="info-item">
                <label>Email:</label>
                <span>{{ application.email || 'Not provided' }}</span>
              </div>
              <div class="info-item">
                <label>Application Date:</label>
                <span>{{ formatDate(application.mentorApplication?.submittedAt || application.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>User Since:</label>
                <span>{{ formatDate(application.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Bio -->
          <div class="section" *ngIf="application.bio">
            <h4>Bio</h4>
            <p class="bio-text">{{ application.bio }}</p>
          </div>

          <!-- Proficiencies -->
          <div class="section" *ngIf="getSelectedProficiencies(application).length > 0">
            <h4>Areas of Expertise</h4>
            <div class="proficiencies">
              <mat-chip *ngFor="let proficiency of getSelectedProficiencies(application)" color="primary">
                {{ proficiency }}
              </mat-chip>
            </div>
          </div>

          <!-- Social Links -->
          <div class="section" *ngIf="getSocialLinks(application).length > 0">
            <h4>Social Links</h4>
            <div class="social-links">
              <div *ngFor="let link of getSocialLinks(application)" class="social-link">
                <strong>{{ link.platform }}:</strong>
                <a [href]="link.url" target="_blank" rel="noopener noreferrer">{{ link.url }}</a>
              </div>
            </div>
          </div>

          <!-- Application Reason -->
          <div class="section" *ngIf="application.mentorApplication?.reason">
            <h4>Why do you want to become a mentor?</h4>
            <p class="reason-text">{{ application.mentorApplication?.reason }}</p>
          </div>

          <!-- Experience -->
          <div class="section" *ngIf="application.mentorApplication?.experience">
            <h4>Relevant Experience</h4>
            <p class="experience-text">{{ application.mentorApplication?.experience }}</p>
          </div>

          <!-- Action Buttons -->
          <div class="action-section">
            <h4>Review Actions</h4>
            <div class="action-buttons">
              <button mat-raised-button color="primary" (click)="approveApplication(application)">
                <mat-icon>check</mat-icon>
                Approve Application
              </button>
              <button mat-raised-button color="warn" (click)="rejectApplication(application)">
                <mat-icon>close</mat-icon>
                Reject Application
              </button>
            </div>
            <p class="action-note">
              <mat-icon>info</mat-icon>
              Approving will grant mentor privileges and update the user's role.
            </p>
          </div>
        </div>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
