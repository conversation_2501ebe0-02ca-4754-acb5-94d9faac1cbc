import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule } from '@angular/router';

import { ProfileService } from '../../../core/services/profile.service';
import { User, InstructionCategory } from '../../../core/models/user.model';
import { MentorCardComponent } from '../mentor-card/mentor-card.component';

@Component({
  selector: 'app-mentor-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    RouterModule,
    MentorCardComponent
  ],
  templateUrl: './mentor-search.component.html',
  styleUrls: ['./mentor-search.component.scss']
})
export class MentorSearchComponent implements OnInit {
  mentors: User[] = [];
  filteredMentors: User[] = [];
  isLoading = false;
  errorMessage = '';
  searchForm: FormGroup;
  categories = Object.values(InstructionCategory);
  
  // Pagination
  totalMentors = 0;
  pageSize = 6;
  pageIndex = 0;
  pageSizeOptions = [6, 12, 24];
  
  constructor(
    private profileService: ProfileService,
    private fb: FormBuilder
  ) {
    this.searchForm = this.fb.group({
      search: [''],
      category: ['']
    });
  }
  
  ngOnInit(): void {
    this.loadMentors();
    
    // Subscribe to form changes
    this.searchForm.valueChanges.subscribe(() => {
      this.filterMentors();
    });
  }
  
  /**
   * Load mentors from the API
   */
  loadMentors(page: number = 0, limit: number = this.pageSize): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    const query = {
      page: page + 1, // API uses 1-based indexing
      limit,
      ...this.searchForm.value
    };
    
    this.profileService.getMentors(query).subscribe({
      next: (result) => {
        this.mentors = result.mentors;
        this.filteredMentors = this.mentors;
        this.totalMentors = result.pagination.total;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load mentors. Please try again.';
        this.isLoading = false;
        console.error('Error loading mentors:', error);
      }
    });
  }
  
  /**
   * Filter mentors based on search form
   */
  filterMentors(): void {
    // If we're using server-side filtering, reload mentors
    this.loadMentors(this.pageIndex, this.pageSize);
    
    // For client-side filtering (if needed)
    // const { search, category } = this.searchForm.value;
    // this.filteredMentors = this.mentors.filter(mentor => {
    //   // Filter by search term
    //   const matchesSearch = !search || 
    //     mentor.discordUsername.toLowerCase().includes(search.toLowerCase()) ||
    //     (mentor.bio && mentor.bio.toLowerCase().includes(search.toLowerCase()));
      
    //   // Filter by category
    //   const matchesCategory = !category || 
    //     mentor.proficiencies.some(p => p.category === category && p.isSelected);
      
    //   return matchesSearch && matchesCategory;
    // });
  }
  
  /**
   * Handle page change event
   */
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadMentors(this.pageIndex, this.pageSize);
  }
  
  /**
   * Reset search filters
   */
  resetFilters(): void {
    this.searchForm.reset({
      search: '',
      category: ''
    });
    this.pageIndex = 0;
    this.loadMentors();
  }
}
