<div class="notification-list-container">
  <div class="notification-header">
    <h2>Notifications</h2>
    <button 
      mat-button 
      color="primary" 
      (click)="markAllAsRead()" 
      [disabled]="notifications.length === 0 || notifications.every(n => n.isRead)">
      Mark All as Read
    </button>
  </div>

  <div class="notification-content">
    <ng-container *ngIf="isLoading && notifications.length === 0">
      <div class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading notifications...</p>
      </div>
    </ng-container>

    <ng-container *ngIf="errorMessage">
      <div class="error-container">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="loadNotifications()">Try Again</button>
      </div>
    </ng-container>

    <ng-container *ngIf="!isLoading && !errorMessage && notifications.length === 0">
      <div class="empty-state">
        <mat-icon>notifications_none</mat-icon>
        <p>No notifications yet</p>
      </div>
    </ng-container>

    <mat-nav-list *ngIf="notifications.length > 0">
      <a 
        mat-list-item 
        *ngFor="let notification of notifications" 
        (click)="handleNotificationClick(notification)"
        [ngClass]="{'unread': !notification.isRead}">
        
        <div class="notification-icon" [ngClass]="notification.type">
          <mat-icon *ngIf="notification.type === 'like'">favorite</mat-icon>
          <mat-icon *ngIf="notification.type === 'comment'">comment</mat-icon>
          <mat-icon *ngIf="notification.type === 'mention'">alternate_email</mat-icon>
          <mat-icon *ngIf="notification.type === 'message'">chat</mat-icon>
          <mat-icon *ngIf="notification.type === 'mentor_application'">school</mat-icon>
          <mat-icon *ngIf="notification.type === 'system'">info</mat-icon>
        </div>
        
        <div class="notification-content">
          <p class="notification-text">{{ notification.content }}</p>
          <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
        </div>
        
        <button 
          mat-icon-button 
          class="delete-button" 
          (click)="deleteNotification($event, notification._id)" 
          aria-label="Delete notification">
          <mat-icon>close</mat-icon>
        </button>
      </a>
    </mat-nav-list>

    <div class="load-more" *ngIf="hasMoreNotifications">
      <button mat-button color="primary" (click)="loadMoreNotifications()" [disabled]="isLoading">
        <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
        <span *ngIf="isLoading">Loading...</span>
        <span *ngIf="!isLoading">Load More</span>
      </button>
    </div>
  </div>
</div>
