/**
 * Performance monitoring middleware for HypeHive backend
 * Tracks response times, memory usage, and other performance metrics
 */

const logger = require('../config/logger');

// Performance metrics storage
const performanceMetrics = {
  requests: {
    total: 0,
    byMethod: {},
    byRoute: {},
    byStatus: {}
  },
  responseTimes: {
    total: 0,
    count: 0,
    min: Infinity,
    max: 0,
    avg: 0,
    p95: 0,
    p99: 0,
    samples: []
  },
  memory: {
    samples: [],
    lastSample: null
  },
  errors: {
    total: 0,
    byType: {},
    recent: []
  }
};

/**
 * Performance monitoring middleware
 */
const performanceMiddleware = (req, res, next) => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();
  
  // Track request
  performanceMetrics.requests.total++;
  performanceMetrics.requests.byMethod[req.method] = (performanceMetrics.requests.byMethod[req.method] || 0) + 1;
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  
  res.end = function(...args) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const endMemory = process.memoryUsage();
    
    // Track response time
    performanceMetrics.responseTimes.total += responseTime;
    performanceMetrics.responseTimes.count++;
    performanceMetrics.responseTimes.min = Math.min(performanceMetrics.responseTimes.min, responseTime);
    performanceMetrics.responseTimes.max = Math.max(performanceMetrics.responseTimes.max, responseTime);
    performanceMetrics.responseTimes.avg = performanceMetrics.responseTimes.total / performanceMetrics.responseTimes.count;
    
    // Store sample for percentile calculation (keep last 1000)
    performanceMetrics.responseTimes.samples.push(responseTime);
    if (performanceMetrics.responseTimes.samples.length > 1000) {
      performanceMetrics.responseTimes.samples.shift();
    }
    
    // Calculate percentiles
    const sortedSamples = [...performanceMetrics.responseTimes.samples].sort((a, b) => a - b);
    const p95Index = Math.floor(sortedSamples.length * 0.95);
    const p99Index = Math.floor(sortedSamples.length * 0.99);
    performanceMetrics.responseTimes.p95 = sortedSamples[p95Index] || 0;
    performanceMetrics.responseTimes.p99 = sortedSamples[p99Index] || 0;
    
    // Track by route and status
    const route = req.route ? req.route.path : req.path;
    performanceMetrics.requests.byRoute[route] = (performanceMetrics.requests.byRoute[route] || 0) + 1;
    performanceMetrics.requests.byStatus[res.statusCode] = (performanceMetrics.requests.byStatus[res.statusCode] || 0) + 1;
    
    // Track memory usage
    const memoryDelta = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    };
    
    performanceMetrics.memory.samples.push({
      timestamp: endTime,
      usage: endMemory,
      delta: memoryDelta
    });
    
    // Keep only last 100 memory samples
    if (performanceMetrics.memory.samples.length > 100) {
      performanceMetrics.memory.samples.shift();
    }
    
    performanceMetrics.memory.lastSample = endMemory;
    
    // Log slow requests
    if (responseTime > 1000) {
      logger.warn(`Slow request detected: ${req.method} ${req.originalUrl} - ${responseTime}ms`, {
        method: req.method,
        url: req.originalUrl,
        responseTime,
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    // Log errors
    if (res.statusCode >= 400) {
      performanceMetrics.errors.total++;
      const errorType = res.statusCode >= 500 ? 'server' : 'client';
      performanceMetrics.errors.byType[errorType] = (performanceMetrics.errors.byType[errorType] || 0) + 1;
      
      performanceMetrics.errors.recent.push({
        timestamp: endTime,
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        responseTime,
        ip: req.ip
      });
      
      // Keep only last 50 errors
      if (performanceMetrics.errors.recent.length > 50) {
        performanceMetrics.errors.recent.shift();
      }
    }
    
    // Add performance headers (only if headers haven't been sent)
    if (!res.headersSent) {
      res.set({
        'X-Response-Time': `${responseTime}ms`,
        'X-Memory-Usage': `${Math.round(endMemory.heapUsed / 1024 / 1024)}MB`
      });
    }
    
    return originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Get performance metrics
 */
const getPerformanceMetrics = () => {
  const currentMemory = process.memoryUsage();
  const uptime = process.uptime();
  
  return {
    timestamp: new Date().toISOString(),
    uptime: {
      seconds: Math.floor(uptime),
      formatted: formatUptime(uptime)
    },
    requests: performanceMetrics.requests,
    responseTimes: {
      ...performanceMetrics.responseTimes,
      samples: undefined // Don't include raw samples in response
    },
    memory: {
      current: currentMemory,
      currentFormatted: {
        rss: `${Math.round(currentMemory.rss / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(currentMemory.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(currentMemory.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(currentMemory.external / 1024 / 1024)}MB`
      },
      samplesCount: performanceMetrics.memory.samples.length
    },
    errors: performanceMetrics.errors,
    cpu: process.cpuUsage(),
    nodeVersion: process.version,
    platform: process.platform
  };
};

/**
 * Reset performance metrics
 */
const resetPerformanceMetrics = () => {
  performanceMetrics.requests = {
    total: 0,
    byMethod: {},
    byRoute: {},
    byStatus: {}
  };
  performanceMetrics.responseTimes = {
    total: 0,
    count: 0,
    min: Infinity,
    max: 0,
    avg: 0,
    p95: 0,
    p99: 0,
    samples: []
  };
  performanceMetrics.memory.samples = [];
  performanceMetrics.errors = {
    total: 0,
    byType: {},
    recent: []
  };
  
  logger.info('Performance metrics reset');
};

/**
 * Format uptime in human readable format
 */
const formatUptime = (seconds) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${days}d ${hours}h ${minutes}m ${secs}s`;
};

/**
 * Performance metrics endpoint middleware
 */
const performanceStatsMiddleware = (req, res) => {
  const metrics = getPerformanceMetrics();
  res.json({
    success: true,
    performance: metrics
  });
};

/**
 * Health check middleware with performance data
 */
const healthCheckMiddleware = (req, res) => {
  const metrics = getPerformanceMetrics();
  const isHealthy = metrics.memory.current.heapUsed < 500 * 1024 * 1024; // 500MB threshold
  
  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    timestamp: metrics.timestamp,
    uptime: metrics.uptime,
    memory: metrics.memory.currentFormatted,
    requests: {
      total: metrics.requests.total,
      avgResponseTime: Math.round(metrics.responseTimes.avg)
    },
    errors: {
      total: metrics.errors.total,
      recent: metrics.errors.recent.length
    }
  });
};

// Log performance summary every 5 minutes
setInterval(() => {
  const metrics = getPerformanceMetrics();
  
  if (metrics.requests.total > 0) {
    logger.info('Performance Summary', {
      requests: metrics.requests.total,
      avgResponseTime: Math.round(metrics.responseTimes.avg),
      p95ResponseTime: metrics.responseTimes.p95,
      memoryUsage: metrics.memory.currentFormatted.heapUsed,
      errors: metrics.errors.total
    });
  }
}, 5 * 60 * 1000);

module.exports = {
  performanceMiddleware,
  getPerformanceMetrics,
  resetPerformanceMetrics,
  performanceStatsMiddleware,
  healthCheckMiddleware
};
