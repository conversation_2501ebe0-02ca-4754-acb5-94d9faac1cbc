# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/hype-hive

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
JWT_EXPIRES_IN=7d

# Discord OAuth Configuration
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback
DISCORD_API_ENDPOINT=https://discord.com/api/v10
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# Frontend URL
FRONTEND_URL=http://localhost:4200

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760 # 10MB

# Logging Configuration
LOG_LEVEL=debug

# Discord Bot Configuration (for Discord bot service)
DISCORD_GUILD_ID=your_discord_guild_id_here
BACKEND_API_URL=http://localhost:3000/api
