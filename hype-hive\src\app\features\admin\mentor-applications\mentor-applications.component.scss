.mentor-applications {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }
  }

  .loading-container, .error-container, .no-applications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      color: #666;
    }

    h2 {
      margin: 16px 0 8px 0;
      color: #333;
    }

    p {
      margin: 8px 0;
      color: #666;
    }
  }

  .applications-list {
    .application-panel {
      margin-bottom: 16px;

      .application-header {
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;

        .avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
        }

        .user-info {
          flex: 1;

          h3 {
            margin: 0 0 4px 0;
            font-weight: 500;
            font-size: 1.1rem;
          }

          p {
            margin: 0;
            color: #666;
            font-size: 0.875rem;
          }
        }

        mat-chip {
          font-size: 0.75rem;
        }
      }

      .application-details {
        padding: 16px 0;

        .section {
          margin-bottom: 24px;

          h4 {
            margin: 0 0 12px 0;
            color: #333;
            font-weight: 500;
            font-size: 1rem;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
          }

          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;

            .info-item {
              display: flex;
              flex-direction: column;
              gap: 4px;

              label {
                font-weight: 500;
                color: #666;
                font-size: 0.875rem;
              }

              span {
                color: #333;
              }
            }
          }

          .bio-text, .reason-text, .experience-text {
            background: #f5f5f5;
            padding: 16px;
            border-radius: 8px;
            margin: 0;
            line-height: 1.6;
            white-space: pre-wrap;
          }

          .proficiencies {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            mat-chip {
              font-size: 0.75rem;
            }
          }

          .social-links {
            .social-link {
              margin-bottom: 8px;

              strong {
                display: inline-block;
                width: 100px;
                color: #666;
              }

              a {
                color: #1976d2;
                text-decoration: none;

                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }
        }

        .action-section {
          border-top: 1px solid #e0e0e0;
          padding-top: 16px;

          .action-buttons {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;

            button {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }

          .action-note {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 0.875rem;
            margin: 0;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .mentor-applications {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      button {
        align-self: flex-end;
      }
    }

    .applications-list {
      .application-panel {
        .application-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .user-info {
            order: -1;
          }

          mat-chip {
            align-self: flex-end;
          }
        }

        .application-details {
          .section {
            .info-grid {
              grid-template-columns: 1fr;
            }

            .action-buttons {
              flex-direction: column;

              button {
                justify-content: center;
              }
            }
          }
        }
      }
    }
  }
}
