const express = require('express');
const { handleDiscordCallback, handleDiscordCallbackServer, getCurrentUser, logout } = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { authValidation } = require('../middleware/validation');
const { authLimiter } = require('../middleware/security');

const router = express.Router();

// GET /api/auth/discord/callback - Handle Discord OAuth callback (server-side)
router.get('/discord/callback', authLimiter, handleDiscordCallbackServer);

// POST /api/auth/discord - Handle Discord OAuth callback (legacy API endpoint)
router.post('/discord', authLimiter, authValidation.discordCallback, handleDiscordCallback);

// GET /api/auth/me - Get current user profile
router.get('/me', protect, getCurrentUser);

// POST /api/auth/logout - Logout user
router.post('/logout', protect, logout);

module.exports = router;
