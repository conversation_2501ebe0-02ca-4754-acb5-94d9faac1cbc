const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { protect } = require('../middleware/auth');

/**
 * @route   GET /api/auth/socket-token
 * @desc    Get a socket-specific auth token for authenticated users
 * @access  Private
 */
router.get('/socket-token', protect, (req, res) => {
  try {
    // User is already authenticated via middleware
    const user = req.user;
    
    // Create a short-lived token specifically for socket connections
    const token = jwt.sign(
      { id: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '1h' } // Short expiry for socket tokens
    );
    
    return res.status(200).json({
      success: true,
      token
    });
  } catch (error) {
    console.error('Error generating socket token:', error);
    return res.status(500).json({
      success: false,
      message: 'Could not generate socket token'
    });
  }
});

module.exports = router;
