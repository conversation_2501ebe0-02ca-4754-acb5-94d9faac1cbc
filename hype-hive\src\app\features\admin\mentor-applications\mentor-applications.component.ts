import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatExpansionModule } from '@angular/material/expansion';

import { AdminService } from '../../../core/services/admin.service';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-mentor-applications',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatExpansionModule
  ],
  templateUrl: './mentor-applications.component.html',
  styleUrl: './mentor-applications.component.scss'
})
export class MentorApplicationsComponent implements OnInit {
  applications: User[] = [];
  loading = true;
  error: string | null = null;

  constructor(
    private adminService: AdminService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadApplications();
  }

  loadApplications(): void {
    this.loading = true;
    this.error = null;

    this.adminService.getPendingMentorApplications().subscribe({
      next: (applications) => {
        this.applications = applications;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading applications:', error);
        this.error = 'Failed to load mentor applications';
        this.loading = false;
      }
    });
  }

  approveApplication(application: User): void {
    this.adminService.reviewMentorApplication(application.id, 'approved').subscribe({
      next: () => {
        this.snackBar.open('Mentor application approved', 'Close', { duration: 3000 });
        this.loadApplications(); // Refresh the list
      },
      error: (error) => {
        console.error('Error approving application:', error);
        this.snackBar.open('Failed to approve application', 'Close', { duration: 3000 });
      }
    });
  }

  rejectApplication(application: User): void {
    this.adminService.reviewMentorApplication(application.id, 'rejected').subscribe({
      next: () => {
        this.snackBar.open('Mentor application rejected', 'Close', { duration: 3000 });
        this.loadApplications(); // Refresh the list
      },
      error: (error) => {
        console.error('Error rejecting application:', error);
        this.snackBar.open('Failed to reject application', 'Close', { duration: 3000 });
      }
    });
  }

  formatDate(date: string | Date): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }

  getSelectedProficiencies(user: User): string[] {
    if (!user.proficiencies) return [];
    return user.proficiencies
      .filter(prof => prof.isSelected)
      .map(prof => prof.name);
  }

  getSocialLinks(user: User): { platform: string; url: string }[] {
    if (!user.socialLinks) return [];
    return Object.entries(user.socialLinks)
      .filter(([_, url]) => url && url.trim() !== '')
      .map(([platform, url]) => ({ platform, url: url as string }));
  }
}
