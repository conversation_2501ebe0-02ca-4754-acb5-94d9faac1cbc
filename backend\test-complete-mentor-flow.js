const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

const BASE_URL = 'http://localhost:3000/api/discord-bot';

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hypehive');
    console.log('📦 Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

// User model
const userSchema = new mongoose.Schema({
  discordId: String,
  discordUsername: String,
  discordAvatar: String,
  email: String,
  isMentor: { type: Boolean, default: false },
  proficiencies: [{
    category: String,
    isSelected: { type: Boolean, default: false }
  }],
  mentorApplication: {
    status: { type: String, default: 'none' },
    appliedAt: Date,
    reviewedAt: Date
  },
  role: { type: String, default: 'user' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function testCompleteMentorFlow() {
  console.log('🧪 Testing Complete Mentor Approval Flow with User Sync...\n');

  await connectDB();

  const testDiscordId = '999888777666555444';
  const testUsername = 'CompleteFlowTest#0001';

  try {
    // Step 1: Clean up any existing test user
    console.log('🧹 Cleaning up any existing test user...');
    await User.deleteOne({ discordId: testDiscordId });
    console.log('✅ Cleanup complete');

    // Step 2: Test the complete flow - sync user first, then approve
    console.log('\n👤 Step 1: Sync user (simulating Discord bot user sync)...');
    const syncResponse = await axios.post(`${BASE_URL}/sync-user`, {
      discordId: testDiscordId,
      discordUsername: testUsername,
      discordAvatar: null
    });
    console.log('✅ User sync response:', syncResponse.data);

    // Step 3: Verify user was created
    let user = await User.findOne({ discordId: testDiscordId });
    console.log('✅ User after sync:', {
      discordId: user.discordId,
      discordUsername: user.discordUsername,
      isMentor: user.isMentor,
      mentorApplication: user.mentorApplication
    });

    // Step 4: Now approve the mentor (simulating Discord bot approval)
    console.log('\n🤖 Step 2: Approve mentor (simulating Discord bot approval)...');
    const approvalResponse = await axios.post(`${BASE_URL}/approve-mentor`, {
      discordId: testDiscordId
    });
    console.log('✅ Mentor approval response:', approvalResponse.data);

    // Step 5: Verify the user is now a mentor
    user = await User.findOne({ discordId: testDiscordId });
    console.log('✅ User after approval:', {
      discordId: user.discordId,
      discordUsername: user.discordUsername,
      isMentor: user.isMentor,
      mentorApplication: user.mentorApplication
    });

    // Step 6: Test the mentors list endpoint
    console.log('\n📋 Step 3: Check mentors list...');
    const mentorsResponse = await axios.get(`${BASE_URL}/mentors`);
    console.log('✅ Mentors list retrieved, count:', mentorsResponse.data.count);
    
    const ourMentor = mentorsResponse.data.data.find(m => m.discordId === testDiscordId);
    if (ourMentor) {
      console.log('✅ Our new mentor found in list:', ourMentor.discordUsername);
    } else {
      console.log('❌ Our new mentor NOT found in list');
    }

    // Step 7: Test approval of non-existent user (should sync first)
    console.log('\n👻 Step 4: Test approval of non-existent user...');
    const nonExistentId = '111000111000111000';
    const nonExistentUsername = 'NonExistent#9999';
    
    // First sync the non-existent user
    const syncResponse2 = await axios.post(`${BASE_URL}/sync-user`, {
      discordId: nonExistentId,
      discordUsername: nonExistentUsername,
      discordAvatar: null
    });
    console.log('✅ Non-existent user sync response:', syncResponse2.data);
    
    // Then approve them
    const approvalResponse2 = await axios.post(`${BASE_URL}/approve-mentor`, {
      discordId: nonExistentId
    });
    console.log('✅ Non-existent user approval response:', approvalResponse2.data);

    // Clean up
    console.log('\n🧹 Cleaning up test users...');
    await User.deleteOne({ discordId: testDiscordId });
    await User.deleteOne({ discordId: nonExistentId });
    console.log('✅ Test users cleaned up');

    console.log('\n🎉 COMPLETE MENTOR FLOW TEST PASSED!');
    console.log('✅ User sync works correctly');
    console.log('✅ Mentor approval works after sync');
    console.log('✅ Approved mentors appear in mentors list');
    console.log('✅ Flow works for both existing and new users');
    console.log('\n📝 The Discord bot should now work without "error updating website" messages!');

  } catch (error) {
    console.error('❌ Complete flow test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   URL:', error.config?.url);
    }
  } finally {
    await mongoose.connection.close();
  }
}

testCompleteMentorFlow().catch(console.error);
