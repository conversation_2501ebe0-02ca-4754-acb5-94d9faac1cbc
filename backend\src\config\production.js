/**
 * Production configuration
 */
module.exports = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    cors: {
      origin: process.env.FRONTEND_URL || 'https://your-production-url.com',
      credentials: true
    }
  },
  
  // Database configuration
  database: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true
    }
  },
  
  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  
  // Discord OAuth configuration
  discord: {
    clientId: process.env.DISCORD_CLIENT_ID,
    clientSecret: process.env.DISCORD_CLIENT_SECRET,
    redirectUri: process.env.DISCORD_REDIRECT_URI || 'https://your-production-url.com/auth/callback',
    apiEndpoint: process.env.DISCORD_API_ENDPOINT || 'https://discord.com/api/v10',
    scope: 'identify email'
  },
  
  // File upload configuration
  upload: {
    dir: process.env.UPLOAD_DIR || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760 // 10MB
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: 'json'
  }
};
