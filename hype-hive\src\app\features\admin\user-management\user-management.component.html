<div class="user-management">
  <div class="page-header">
    <h1>User Management</h1>
    <button mat-raised-button color="primary" (click)="loadUsers()" [disabled]="loading">
      <mat-icon>refresh</mat-icon>
      Refresh
    </button>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters">
        <mat-form-field appearance="outline">
          <mat-label>Search users</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()" placeholder="Username or email">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Role</mat-label>
          <mat-select [(ngModel)]="roleFilter" (selectionChange)="onFilterChange()">
            <mat-option value="">All Roles</mat-option>
            <mat-option value="user">User</mat-option>
            <mat-option value="mentor">Mentor</mat-option>
            <mat-option value="admin">Admin</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="statusFilter" (selectionChange)="onFilterChange()">
            <mat-option value="">All Status</mat-option>
            <mat-option value="true">Active</mat-option>
            <mat-option value="false">Inactive</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="filter-actions">
          <button mat-button color="primary" (click)="onSearch()">
            <mat-icon>search</mat-icon>
            Search
          </button>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading users...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-button color="primary" (click)="loadUsers()">Try Again</button>
  </div>

  <!-- Users Table -->
  <mat-card *ngIf="!loading && !error" class="users-table-card">
    <mat-card-header>
      <mat-card-title>Users ({{ totalUsers }})</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <table mat-table [dataSource]="users" class="users-table">
        <!-- User Column -->
        <ng-container matColumnDef="user">
          <th mat-header-cell *matHeaderCellDef>User</th>
          <td mat-cell *matCellDef="let user">
            <div class="user-cell">
              <img [src]="user.discordAvatar" [alt]="user.discordUsername" class="avatar">
              <div class="user-info">
                <div class="username">{{ user.discordUsername }}</div>
                <div class="user-id">ID: {{ user.id }}</div>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Email Column -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef>Email</th>
          <td mat-cell *matCellDef="let user">{{ user.email || 'Not provided' }}</td>
        </ng-container>

        <!-- Role Column -->
        <ng-container matColumnDef="role">
          <th mat-header-cell *matHeaderCellDef>Role</th>
          <td mat-cell *matCellDef="let user">
            <mat-form-field appearance="outline" class="role-select">
              <mat-select [value]="user.role" (selectionChange)="changeUserRole(user, $event.value)">
                <mat-option value="user">User</mat-option>
                <mat-option value="mentor">Mentor</mat-option>
                <mat-option value="admin">Admin</mat-option>
              </mat-select>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let user">
            <mat-chip [color]="getStatusColor(user.isActive)">
              {{ user.isActive ? 'Active' : 'Inactive' }}
            </mat-chip>
          </td>
        </ng-container>

        <!-- Last Login Column -->
        <ng-container matColumnDef="lastLogin">
          <th mat-header-cell *matHeaderCellDef>Last Login</th>
          <td mat-cell *matCellDef="let user">
            {{ user.lastLogin ? formatDate(user.lastLogin) : 'Never' }}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let user">
            <div class="action-buttons">
              <button mat-icon-button 
                      [color]="user.isActive ? 'warn' : 'primary'"
                      (click)="toggleUserStatus(user)"
                      [matTooltip]="user.isActive ? 'Deactivate User' : 'Activate User'">
                <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>
              </button>
              
              <button mat-icon-button 
                      color="primary"
                      [routerLink]="['/profile', user.id]"
                      matTooltip="View Profile">
                <mat-icon>visibility</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- Pagination -->
      <mat-paginator 
        [length]="totalUsers"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        [pageIndex]="currentPage"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
