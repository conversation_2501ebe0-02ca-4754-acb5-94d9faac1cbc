import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { HttpEventType } from '@angular/common/http';

import { ContentService } from '../../../core/services/content.service';
import { UploadService } from '../../../core/services/upload.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-content-upload',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  templateUrl: './content-upload.component.html',
  styleUrl: './content-upload.component.scss'
})
export class ContentUploadComponent implements OnInit {
  contentForm!: FormGroup;
  isLoading = false;
  isUploading = false;
  uploadProgress = 0;
  
  // File handling
  selectedVideoFile: File | null = null;
  selectedThumbnailFile: File | null = null;
  videoPreviewUrl: string | null = null;
  thumbnailPreviewUrl: string | null = null;
  
  // Form options
  categories = [
    'Account Setup',
    'Bots',
    'Networking',
    'Emotes',
    'Streaming Platforms',
    'Affiliate Roles',
    'Console Help',
    'PC Help'
  ];
  
  difficulties = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' }
  ];
  
  // Tags
  tags: string[] = [];
  tagInput = '';
  
  constructor(
    private fb: FormBuilder,
    private contentService: ContentService,
    private uploadService: UploadService,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}
  
  ngOnInit(): void {
    this.initializeForm();
  }
  
  /**
   * Initialize the content form
   */
  initializeForm(): void {
    this.contentForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(1000)]],
      category: ['', Validators.required],
      embedUrl: [''],
      difficulty: ['beginner', Validators.required],
      isPublished: [false]
    });
  }
  
  /**
   * Handle video file selection
   */
  onVideoFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      
      // Validate file type
      if (!file.type.startsWith('video/')) {
        this.snackBar.open('Please select a valid video file', 'Close', { duration: 3000 });
        return;
      }
      
      // Validate file size (100MB limit)
      if (file.size > 100 * 1024 * 1024) {
        this.snackBar.open('Video file size must be less than 100MB', 'Close', { duration: 3000 });
        return;
      }
      
      this.selectedVideoFile = file;
      
      // Create preview URL
      if (this.videoPreviewUrl) {
        URL.revokeObjectURL(this.videoPreviewUrl);
      }
      this.videoPreviewUrl = URL.createObjectURL(file);
    }
  }
  
  /**
   * Handle thumbnail file selection
   */
  onThumbnailFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.snackBar.open('Please select a valid image file', 'Close', { duration: 3000 });
        return;
      }
      
      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        this.snackBar.open('Image file size must be less than 5MB', 'Close', { duration: 3000 });
        return;
      }
      
      this.selectedThumbnailFile = file;
      
      // Create preview URL
      if (this.thumbnailPreviewUrl) {
        URL.revokeObjectURL(this.thumbnailPreviewUrl);
      }
      this.thumbnailPreviewUrl = URL.createObjectURL(file);
    }
  }
  
  /**
   * Add tag to the list
   */
  addTag(): void {
    const tag = this.tagInput.trim().toLowerCase();
    if (tag && !this.tags.includes(tag) && this.tags.length < 10) {
      this.tags.push(tag);
      this.tagInput = '';
    }
  }
  
  /**
   * Remove tag from the list
   */
  removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
    }
  }
  
  /**
   * Handle tag input keypress
   */
  onTagKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.addTag();
    }
  }
  
  /**
   * Remove selected video file
   */
  removeVideoFile(): void {
    this.selectedVideoFile = null;
    if (this.videoPreviewUrl) {
      URL.revokeObjectURL(this.videoPreviewUrl);
      this.videoPreviewUrl = null;
    }
  }
  
  /**
   * Remove selected thumbnail file
   */
  removeThumbnailFile(): void {
    this.selectedThumbnailFile = null;
    if (this.thumbnailPreviewUrl) {
      URL.revokeObjectURL(this.thumbnailPreviewUrl);
      this.thumbnailPreviewUrl = null;
    }
  }
  
  /**
   * Upload files and create content
   */
  async onSubmit(): Promise<void> {
    if (this.contentForm.invalid) {
      this.markFormGroupTouched();
      return;
    }
    
    this.isLoading = true;
    this.isUploading = true;
    this.uploadProgress = 0;
    
    try {
      let videoUrl = '';
      let thumbnailUrl = '';
      
      // Upload video file if selected
      if (this.selectedVideoFile) {
        videoUrl = await this.uploadFileWithProgress(this.selectedVideoFile);
      }
      
      // Upload thumbnail file if selected
      if (this.selectedThumbnailFile) {
        thumbnailUrl = await this.uploadFileWithProgress(this.selectedThumbnailFile);
      }
      
      // Create content
      const contentData = {
        ...this.contentForm.value,
        videoUrl,
        thumbnailUrl,
        tags: this.tags
      };
      
      this.contentService.createContent(contentData).subscribe({
        next: (content) => {
          this.snackBar.open('Content created successfully!', 'Close', { duration: 3000 });
          this.router.navigate(['/content', content._id]);
        },
        error: (error) => {
          console.error('Error creating content:', error);
          this.snackBar.open('Failed to create content. Please try again.', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.isUploading = false;
        }
      });
      
    } catch (error) {
      console.error('Error uploading files:', error);
      this.snackBar.open('Failed to upload files. Please try again.', 'Close', { duration: 3000 });
      this.isLoading = false;
      this.isUploading = false;
    }
  }
  
  /**
   * Upload file with progress tracking
   */
  private uploadFileWithProgress(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      this.uploadService.uploadFileWithProgress(file).subscribe({
        next: (event) => {
          if (event.type === HttpEventType.UploadProgress && event.total) {
            this.uploadProgress = Math.round(100 * event.loaded / event.total);
          } else if (event.type === HttpEventType.Response) {
            if (event.body?.success) {
              resolve(event.body.data.url);
            } else {
              reject(new Error(event.body?.message || 'Upload failed'));
            }
          }
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  }
  
  /**
   * Mark all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.contentForm.controls).forEach(key => {
      this.contentForm.get(key)?.markAsTouched();
    });
  }
  
  /**
   * Cancel content creation
   */
  cancel(): void {
    this.router.navigate(['/content']);
  }
  
  /**
   * Clean up object URLs on component destroy
   */
  ngOnDestroy(): void {
    if (this.videoPreviewUrl) {
      URL.revokeObjectURL(this.videoPreviewUrl);
    }
    if (this.thumbnailPreviewUrl) {
      URL.revokeObjectURL(this.thumbnailPreviewUrl);
    }
  }
}
