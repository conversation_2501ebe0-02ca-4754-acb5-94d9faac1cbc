#!/usr/bin/env node

/**
 * Performance audit script for HypeHive backend
 * Analyzes and provides recommendations for performance optimization
 */

const fs = require('fs');
const path = require('path');

class PerformanceAuditor {
  constructor() {
    this.recommendations = [];
    this.issues = [];
    this.metrics = {};
  }

  addRecommendation(category, message, impact = 'medium') {
    this.recommendations.push({ category, message, impact });
  }

  addIssue(category, message, severity = 'medium') {
    this.issues.push({ category, message, severity });
  }

  // Analyze package.json for optimization opportunities
  analyzeDependencies() {
    console.log('📦 Analyzing dependencies...');
    
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      const deps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      
      this.metrics.totalDependencies = Object.keys(deps).length;
      this.metrics.totalDevDependencies = Object.keys(devDeps).length;
      
      // Check for heavy dependencies
      const heavyPackages = [
        'lodash', 'moment', 'axios', 'express', 'mongoose'
      ];
      
      heavyPackages.forEach(pkg => {
        if (deps[pkg]) {
          if (pkg === 'lodash') {
            this.addRecommendation('Dependencies', 
              'Consider using individual lodash functions instead of the full library', 'high');
          }
          if (pkg === 'moment') {
            this.addRecommendation('Dependencies', 
              'Consider replacing moment.js with day.js for smaller bundle size', 'medium');
          }
        }
      });
      
      // Check for unused dependencies
      this.addRecommendation('Dependencies', 
        'Run `npm-check-unused` to identify unused dependencies', 'low');
      
      // Check for duplicate dependencies
      this.addRecommendation('Dependencies', 
        'Run `npm ls` to check for duplicate dependencies', 'low');
    }
  }

  // Analyze source code for performance issues
  analyzeSourceCode() {
    console.log('🔍 Analyzing source code...');
    
    const srcDir = path.join(__dirname, '..', 'src');
    let totalFiles = 0;
    let totalLines = 0;
    
    const analyzeDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          analyzeDirectory(filePath);
        } else if (file.endsWith('.js')) {
          totalFiles++;
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.split('\n').length;
          totalLines += lines;
          
          // Check for performance anti-patterns
          if (content.includes('console.log') && process.env.NODE_ENV === 'production') {
            this.addIssue('Code Quality', 
              `Console.log found in ${filePath} - remove for production`, 'low');
          }
          
          if (content.includes('JSON.parse(JSON.stringify')) {
            this.addIssue('Code Quality', 
              `Deep clone anti-pattern found in ${filePath}`, 'medium');
          }
          
          if (content.includes('require(') && content.includes('import ')) {
            this.addIssue('Code Quality', 
              `Mixed require/import in ${filePath} - use consistent module system`, 'low');
          }
          
          // Check for large files
          if (lines > 500) {
            this.addRecommendation('Code Quality', 
              `Large file detected: ${filePath} (${lines} lines) - consider splitting`, 'medium');
          }
        }
      });
    };
    
    if (fs.existsSync(srcDir)) {
      analyzeDirectory(srcDir);
    }
    
    this.metrics.totalSourceFiles = totalFiles;
    this.metrics.totalSourceLines = totalLines;
    this.metrics.averageLinesPerFile = Math.round(totalLines / totalFiles);
  }

  // Analyze database queries and models
  analyzeDatabasePerformance() {
    console.log('🗄️ Analyzing database performance...');
    
    const modelsDir = path.join(__dirname, '..', 'src', 'models');
    
    if (fs.existsSync(modelsDir)) {
      const modelFiles = fs.readdirSync(modelsDir).filter(f => f.endsWith('.js'));
      
      modelFiles.forEach(file => {
        const filePath = path.join(modelsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for missing indexes
        if (content.includes('unique: true') && !content.includes('index: true')) {
          this.addRecommendation('Database', 
            `Consider adding explicit index for unique fields in ${file}`, 'medium');
        }
        
        // Check for text search without indexes
        if (content.includes('$text') || content.includes('$regex')) {
          this.addRecommendation('Database', 
            `Text search detected in ${file} - ensure proper text indexes`, 'high');
        }
        
        // Check for population without select
        if (content.includes('.populate(') && !content.includes('select:')) {
          this.addRecommendation('Database', 
            `Population without field selection in ${file} - consider limiting fields`, 'medium');
        }
      });
    }
  }

  // Analyze middleware and route performance
  analyzeMiddlewarePerformance() {
    console.log('⚡ Analyzing middleware performance...');
    
    const middlewareDir = path.join(__dirname, '..', 'src', 'middleware');
    const routesDir = path.join(__dirname, '..', 'src', 'routes');
    
    // Check middleware
    if (fs.existsSync(middlewareDir)) {
      const middlewareFiles = fs.readdirSync(middlewareDir).filter(f => f.endsWith('.js'));
      
      middlewareFiles.forEach(file => {
        const filePath = path.join(middlewareDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for synchronous operations in middleware
        if (content.includes('fs.readFileSync') || content.includes('JSON.parse')) {
          this.addIssue('Middleware', 
            `Synchronous operations detected in ${file} - consider async alternatives`, 'medium');
        }
        
        // Check for heavy operations in middleware
        if (content.includes('bcrypt.hash') && !content.includes('await')) {
          this.addRecommendation('Middleware', 
            `Heavy crypto operations in ${file} - ensure they're async`, 'high');
        }
      });
    }
    
    // Check routes
    if (fs.existsSync(routesDir)) {
      const routeFiles = fs.readdirSync(routesDir).filter(f => f.endsWith('.js'));
      
      routeFiles.forEach(file => {
        const filePath = path.join(routesDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for missing error handling
        if (content.includes('async') && !content.includes('catch')) {
          this.addRecommendation('Routes', 
            `Missing error handling in async route ${file}`, 'high');
        }
        
        // Check for N+1 query patterns
        if (content.includes('forEach') && content.includes('await')) {
          this.addIssue('Routes', 
            `Potential N+1 query pattern in ${file}`, 'high');
        }
      });
    }
  }

  // Check for caching opportunities
  analyzeCaching() {
    console.log('🚀 Analyzing caching opportunities...');
    
    const appJsPath = path.join(__dirname, '..', 'src', 'app.js');
    
    if (fs.existsSync(appJsPath)) {
      const content = fs.readFileSync(appJsPath, 'utf8');
      
      // Check for caching middleware
      if (!content.includes('cache') && !content.includes('redis')) {
        this.addRecommendation('Caching', 
          'Consider implementing response caching for static content', 'medium');
      }
      
      // Check for compression
      if (!content.includes('compression')) {
        this.addRecommendation('Caching', 
          'Consider adding gzip compression middleware', 'high');
      }
    }
    
    // Check for database query caching
    this.addRecommendation('Caching', 
      'Consider implementing query result caching for frequently accessed data', 'medium');
  }

  // Generate performance report
  generateReport() {
    console.log('\n📊 Performance Audit Report\n');
    console.log('=' .repeat(50));
    
    // Metrics
    console.log('\n📈 METRICS:');
    Object.entries(this.metrics).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    // High impact recommendations
    const highImpact = this.recommendations.filter(r => r.impact === 'high');
    if (highImpact.length > 0) {
      console.log('\n🚀 HIGH IMPACT RECOMMENDATIONS:');
      highImpact.forEach(rec => {
        console.log(`   ⚡ [${rec.category}] ${rec.message}`);
      });
    }
    
    // Medium impact recommendations
    const mediumImpact = this.recommendations.filter(r => r.impact === 'medium');
    if (mediumImpact.length > 0) {
      console.log('\n📈 MEDIUM IMPACT RECOMMENDATIONS:');
      mediumImpact.forEach(rec => {
        console.log(`   🔧 [${rec.category}] ${rec.message}`);
      });
    }
    
    // Issues
    if (this.issues.length > 0) {
      console.log('\n⚠️  PERFORMANCE ISSUES:');
      this.issues.forEach(issue => {
        const icon = issue.severity === 'high' ? '🔴' : issue.severity === 'medium' ? '🟡' : '🟢';
        console.log(`   ${icon} [${issue.category}] ${issue.message}`);
      });
    }
    
    // Low impact recommendations
    const lowImpact = this.recommendations.filter(r => r.impact === 'low');
    if (lowImpact.length > 0) {
      console.log('\n💡 LOW IMPACT RECOMMENDATIONS:');
      lowImpact.forEach(rec => {
        console.log(`   💡 [${rec.category}] ${rec.message}`);
      });
    }
    
    // Summary
    console.log('\n📋 SUMMARY:');
    console.log(`   High Impact Recommendations: ${highImpact.length}`);
    console.log(`   Medium Impact Recommendations: ${mediumImpact.length}`);
    console.log(`   Low Impact Recommendations: ${lowImpact.length}`);
    console.log(`   Performance Issues: ${this.issues.length}`);
    
    if (this.recommendations.length === 0 && this.issues.length === 0) {
      console.log('\n🎉 No performance issues or recommendations found!');
    } else {
      console.log('\n✅ Review recommendations to improve application performance.');
    }
  }

  // Run all performance checks
  async runAudit() {
    console.log('⚡ Starting HypeHive Performance Audit\n');
    
    this.analyzeDependencies();
    this.analyzeSourceCode();
    this.analyzeDatabasePerformance();
    this.analyzeMiddlewarePerformance();
    this.analyzeCaching();
    
    this.generateReport();
  }
}

// Run the audit
if (require.main === module) {
  const auditor = new PerformanceAuditor();
  auditor.runAudit().catch(console.error);
}

module.exports = PerformanceAuditor;
