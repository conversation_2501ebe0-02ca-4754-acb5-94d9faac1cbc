# Notifications API

The Notifications API provides endpoints for managing user notifications on the HypeHive platform.

## Endpoints

### Get User Notifications

Retrieves all notifications for the current user.

```
GET /api/notifications
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Query Parameters

| Parameter | Type    | Required | Description                                      |
|-----------|---------|----------|--------------------------------------------------|
| page      | integer | No       | Page number (default: 1)                         |
| limit     | integer | No       | Number of items per page (default: 20)           |
| isRead    | boolean | No       | Filter by read status (true, false)              |
| type      | string  | No       | Filter by notification type                      |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "60d21b4667d0d8992e610c85",
      "recipient": "60d21b4667d0d8992e610c86",
      "sender": {
        "_id": "60d21b4667d0d8992e610c87",
        "discordUsername": "user2",
        "discordAvatar": "avatar_hash"
      },
      "type": "like",
      "content": "user2 liked your content",
      "isRead": false,
      "relatedContent": {
        "contentType": "content",
        "contentId": "60d21b4667d0d8992e610c88"
      },
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
    // More notifications...
  ],
  "pagination": {
    "total": 10,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

### Mark Notification as Read

Marks a specific notification as read.

```
PUT /api/notifications/:id/read
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "60d21b4667d0d8992e610c85",
    "recipient": "60d21b4667d0d8992e610c86",
    "sender": "60d21b4667d0d8992e610c87",
    "type": "like",
    "content": "user2 liked your content",
    "isRead": true,
    "relatedContent": {
      "contentType": "content",
      "contentId": "60d21b4667d0d8992e610c88"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  },
  "message": "Notification marked as read"
}
```

### Mark All Notifications as Read

Marks all notifications for the current user as read.

```
PUT /api/notifications/read-all
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "count": 5
  },
  "message": "5 notifications marked as read"
}
```

### Delete Notification

Deletes a specific notification.

```
DELETE /api/notifications/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "message": "Notification deleted successfully"
}
```

## Notification Types

The platform supports the following notification types:

| Type              | Description                                                  |
|-------------------|--------------------------------------------------------------|
| like              | Someone liked your content                                   |
| comment           | Someone commented on your content                            |
| mention           | Someone mentioned you in a comment                           |
| message           | You received a new message                                   |
| mentor_application| Your mentor application status changed                       |
| system            | System notification                                          |

## Real-time Notifications

Notifications are delivered in real-time using Socket.io, enabling instant notification of various events such as likes, comments, mentions, and messages. For a comprehensive guide to all real-time features, see the [Real-time Features API](./realtime.md) documentation.

### Socket.io Events

#### Server Events (Emitted by the server)

| Event            | Data                                | Description                                      |
|------------------|-------------------------------------|--------------------------------------------------|
| new_notification | notification                        | New notification received                         |

### Socket.io Authentication

To authenticate with Socket.io, include your JWT token in the connection options:

```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  },
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000
});
```

### Example: Notification Implementation

```javascript
// Connect to Socket.io server
const socket = io('http://localhost:3000', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  }
});

// Listen for new notifications
socket.on('new_notification', (notification) => {
  console.log('New notification:', notification);

  // Update UI based on notification type
  switch (notification.type) {
    case 'like':
      showLikeNotification(notification);
      break;
    case 'comment':
      showCommentNotification(notification);
      break;
    case 'mention':
      showMentionNotification(notification);
      break;
    case 'message':
      showMessageNotification(notification);
      break;
    case 'mentor_application':
      showMentorApplicationNotification(notification);
      break;
    case 'system':
      showSystemNotification(notification);
      break;
  }

  // Update notification badge count
  updateNotificationCount();

  // Play notification sound
  playNotificationSound();

  // Show browser notification if page is not active
  if (!document.hasFocus()) {
    showBrowserNotification(notification);
  }
});

// Example function to show browser notification
function showBrowserNotification(notification) {
  // Check if browser notifications are supported and permitted
  if ('Notification' in window && Notification.permission === 'granted') {
    const title = 'HypeHive Notification';
    const options = {
      body: notification.content,
      icon: '/assets/images/logo.png'
    };

    const browserNotification = new Notification(title, options);

    // Handle notification click
    browserNotification.onclick = () => {
      window.focus();
      navigateToNotificationContent(notification);
    };
  }
}

// Example function to navigate to notification content
function navigateToNotificationContent(notification) {
  if (notification.relatedContent) {
    const { contentType, contentId } = notification.relatedContent;

    switch (contentType) {
      case 'chat':
        window.location.href = `/chat/${contentId}`;
        break;
      case 'content':
        window.location.href = `/content/${contentId}`;
        break;
      case 'user':
        window.location.href = `/profile/${contentId}`;
        break;
    }
  }
}
```

### Handling Notification Badges

```javascript
// Track unread notification count
let unreadCount = 0;

// Update notification badge count
function updateNotificationCount() {
  // Increment unread count
  unreadCount++;

  // Update badge in UI
  const badge = document.getElementById('notification-badge');
  if (badge) {
    badge.textContent = unreadCount;
    badge.style.display = unreadCount > 0 ? 'block' : 'none';
  }
}

// Reset notification count when notifications are viewed
function resetNotificationCount() {
  unreadCount = 0;

  // Update badge in UI
  const badge = document.getElementById('notification-badge');
  if (badge) {
    badge.textContent = '0';
    badge.style.display = 'none';
  }
}
```
