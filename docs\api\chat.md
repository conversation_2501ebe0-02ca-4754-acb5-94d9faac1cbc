# Chat API

The Chat API provides endpoints for real-time messaging between users on the HypeHive platform.

## Endpoints

### Get User Chats

Retrieves all chats for the current user.

```
GET /api/chat
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Query Parameters

| Parameter | Type    | Required | Description                                      |
|-----------|---------|----------|--------------------------------------------------|
| page      | integer | No       | Page number (default: 1)                         |
| limit     | integer | No       | Number of items per page (default: 20)           |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "60d21b4667d0d8992e610c85",
      "participants": [
        {
          "_id": "60d21b4667d0d8992e610c86",
          "discordUsername": "user1",
          "discordAvatar": "avatar_hash",
          "isMentor": true
        },
        {
          "_id": "60d21b4667d0d8992e610c87",
          "discordUsername": "user2",
          "discordAvatar": "avatar_hash",
          "isMentor": false
        }
      ],
      "lastMessage": {
        "content": "Hello, how can I help you?",
        "sender": {
          "_id": "60d21b4667d0d8992e610c86",
          "discordUsername": "user1",
          "discordAvatar": "avatar_hash"
        },
        "createdAt": "2023-01-01T00:00:00.000Z"
      },
      "isGroupChat": false,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
    // More chats...
  ],
  "pagination": {
    "total": 10,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

### Get Chat by ID

Retrieves a specific chat by its ID.

```
GET /api/chat/:id
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "60d21b4667d0d8992e610c85",
    "participants": [
      {
        "_id": "60d21b4667d0d8992e610c86",
        "discordUsername": "user1",
        "discordAvatar": "avatar_hash",
        "isMentor": true
      },
      {
        "_id": "60d21b4667d0d8992e610c87",
        "discordUsername": "user2",
        "discordAvatar": "avatar_hash",
        "isMentor": false
      }
    ],
    "messages": [
      {
        "_id": "60d21b4667d0d8992e610c88",
        "sender": {
          "_id": "60d21b4667d0d8992e610c86",
          "discordUsername": "user1",
          "discordAvatar": "avatar_hash"
        },
        "content": "Hello, how can I help you?",
        "readBy": ["60d21b4667d0d8992e610c86"],
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
      // More messages...
    ],
    "lastMessage": {
      "content": "Hello, how can I help you?",
      "sender": {
        "_id": "60d21b4667d0d8992e610c86",
        "discordUsername": "user1",
        "discordAvatar": "avatar_hash"
      },
      "createdAt": "2023-01-01T00:00:00.000Z"
    },
    "isGroupChat": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Create Chat

Creates a new chat.

```
POST /api/chat
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "participants": ["60d21b4667d0d8992e610c87"],
  "isGroupChat": false
}
```

For group chats:

```json
{
  "participants": ["60d21b4667d0d8992e610c87", "60d21b4667d0d8992e610c89"],
  "isGroupChat": true,
  "groupName": "Streaming Tips Group"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "60d21b4667d0d8992e610c85",
    "participants": [
      {
        "_id": "60d21b4667d0d8992e610c86",
        "discordUsername": "user1",
        "discordAvatar": "avatar_hash",
        "isMentor": true
      },
      {
        "_id": "60d21b4667d0d8992e610c87",
        "discordUsername": "user2",
        "discordAvatar": "avatar_hash",
        "isMentor": false
      }
    ],
    "messages": [],
    "isGroupChat": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  "message": "Chat created successfully"
}
```

### Add Message to Chat

Adds a new message to a chat.

```
POST /api/chat/:id/message
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Request Body

```json
{
  "content": "Hello, how can I help you?"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "60d21b4667d0d8992e610c88",
    "sender": {
      "_id": "60d21b4667d0d8992e610c86",
      "discordUsername": "user1",
      "discordAvatar": "avatar_hash"
    },
    "content": "Hello, how can I help you?",
    "readBy": ["60d21b4667d0d8992e610c86"],
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  "message": "Message sent successfully"
}
```

### Mark Messages as Read

Marks all messages in a chat as read by the current user.

```
PUT /api/chat/:id/read
```

#### Headers

| Header        | Value                 | Required |
|---------------|------------------------|----------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes      |

#### Response

```json
{
  "success": true,
  "message": "Messages marked as read"
}
```

## Real-time Communication

The Chat API supports real-time communication using Socket.io, enabling instant messaging, typing indicators, and presence detection. For a comprehensive guide to all real-time features, see the [Real-time Features API](./realtime.md) documentation.

### Socket.io Events

#### Client Events (Emitted by the client)

| Event         | Data                                | Description                                      |
|---------------|-------------------------------------|--------------------------------------------------|
| join_chat     | chatId                              | Join a chat room                                 |
| leave_chat    | chatId                              | Leave a chat room                                |
| send_message  | { chatId, content }                 | Send a message to a chat                         |
| typing        | chatId                              | Indicate that the user is typing                 |
| stop_typing   | chatId                              | Indicate that the user has stopped typing        |

#### Server Events (Emitted by the server)

| Event         | Data                                | Description                                      |
|---------------|-------------------------------------|--------------------------------------------------|
| new_message   | { chatId, message }                 | New message received                             |
| typing        | { chatId, user }                    | User is typing                                   |
| stop_typing   | { chatId, user }                    | User has stopped typing                          |
| error         | { message }                         | Error message                                    |

### Socket.io Authentication

To authenticate with Socket.io, include your JWT token in the connection options:

```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  },
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000
});
```

### Example: Chat Implementation

```javascript
// Join a chat room
function joinChat(chatId) {
  socket.emit('join_chat', chatId);
}

// Send a message
function sendMessage(chatId, content) {
  // Record start time for delivery time tracking
  const startTime = Date.now();

  socket.emit('send_message', { chatId, content });

  // Return a promise that resolves when the message is delivered
  return new Promise((resolve, reject) => {
    socket.on('new_message', (data) => {
      if (data.message.content === content) {
        // Calculate delivery time
        const deliveryTime = Date.now() - startTime;
        console.log(`Message delivered in ${deliveryTime}ms`);

        resolve(data.message);
      }
    });

    // Timeout after 5 seconds
    setTimeout(() => {
      reject(new Error('Message delivery timeout'));
    }, 5000);
  });
}

// Listen for new messages
socket.on('new_message', (data) => {
  const { chatId, message } = data;
  console.log(`New message in chat ${chatId}:`, message);
  // Update UI with new message
});

// Implement typing indicators with debouncing
let typingTimeout;
function handleTyping(chatId, isTyping) {
  clearTimeout(typingTimeout);

  if (isTyping) {
    socket.emit('typing', chatId);

    // Automatically stop typing after 5 seconds of inactivity
    typingTimeout = setTimeout(() => {
      socket.emit('stop_typing', chatId);
    }, 5000);
  } else {
    socket.emit('stop_typing', chatId);
  }
}
```
