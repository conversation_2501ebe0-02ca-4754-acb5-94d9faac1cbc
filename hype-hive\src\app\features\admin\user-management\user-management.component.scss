.user-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }
  }

  .filters-card {
    margin-bottom: 24px;

    .filters {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr auto;
      gap: 16px;
      align-items: end;

      .filter-actions {
        display: flex;
        gap: 8px;

        button {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }

  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 16px 0;
      color: #666;
    }
  }

  .users-table-card {
    .users-table {
      width: 100%;

      .user-cell {
        display: flex;
        align-items: center;
        gap: 12px;

        .avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }

        .user-info {
          .username {
            font-weight: 500;
            margin-bottom: 2px;
          }

          .user-id {
            font-size: 0.75rem;
            color: #666;
          }
        }
      }

      .role-select {
        width: 100px;

        .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }

      .action-buttons {
        display: flex;
        gap: 4px;
      }

      mat-chip {
        font-size: 0.75rem;
        min-height: 24px;
      }

      th {
        font-weight: 600;
        color: #333;
      }

      td {
        padding: 12px 8px;
      }
    }

    mat-paginator {
      border-top: 1px solid #e0e0e0;
      margin-top: 16px;
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .user-management {
    .filters-card .filters {
      grid-template-columns: 1fr;
      gap: 12px;

      .filter-actions {
        justify-content: center;
      }
    }

    .users-table-card .users-table {
      font-size: 0.875rem;

      .user-cell {
        gap: 8px;

        .avatar {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .user-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      button {
        align-self: flex-end;
      }
    }

    .users-table-card {
      .users-table {
        .mat-column-email,
        .mat-column-lastLogin {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .user-management {
    .users-table-card {
      .users-table {
        .mat-column-status {
          display: none;
        }

        .user-cell {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .avatar {
            width: 24px;
            height: 24px;
          }
        }

        .action-buttons {
          flex-direction: column;
        }
      }
    }
  }
}
