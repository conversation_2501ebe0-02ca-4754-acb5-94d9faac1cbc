const express = require('express');
const { upload, uploadFile, deleteFile } = require('../controllers/uploadController');
const { protect } = require('../middleware/auth');
const { uploadLimiter, fileUploadSecurity } = require('../middleware/security');

const router = express.Router();

// POST /api/upload - Upload file
router.post('/', protect, uploadLimiter, upload.single('file'), fileUploadSecurity, uploadFile);

// DELETE /api/upload - Delete file
router.delete('/', protect, deleteFile);

module.exports = router;
