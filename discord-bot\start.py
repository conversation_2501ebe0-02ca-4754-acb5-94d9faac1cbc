#!/usr/bin/env python3
"""
HiveHelper Discord Bot Startup Script
Provides proper error handling and restart capabilities
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_startup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if all required environment variables are set"""
    # Load environment variables from .env file
    load_dotenv()

    required_vars = ['DISCORD_BOT_TOKEN', 'DISCORD_GUILD_ID', 'BACKEND_API_URL']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file or environment configuration")
        return False
    
    return True

def install_dependencies():
    """Install required Python packages"""
    try:
        logger.info("Installing dependencies...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        logger.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False

def start_bot():
    """Start the Discord bot"""
    try:
        logger.info("Starting HiveHelper Discord Bot...")
        subprocess.run([sys.executable, 'hive_helper.py'], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Bot process failed with exit code {e.returncode}")
        return False
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
        return True
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def main():
    """Main startup function"""
    logger.info("HiveHelper Discord Bot Startup")
    
    # Check if we're in the right directory
    if not Path('hive_helper.py').exists():
        logger.error("hive_helper.py not found. Please run this script from the discord-bot directory")
        sys.exit(1)
    
    # Check environment variables
    if not check_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Start bot with restart capability
    max_restarts = 5
    restart_count = 0
    
    while restart_count < max_restarts:
        if start_bot():
            logger.info("Bot stopped normally")
            break
        
        restart_count += 1
        if restart_count < max_restarts:
            logger.warning(f"Bot crashed. Restarting in 10 seconds... (Attempt {restart_count}/{max_restarts})")
            time.sleep(10)
        else:
            logger.error("Maximum restart attempts reached. Exiting.")
            sys.exit(1)

if __name__ == '__main__':
    main()
