const logger = require('../config/logger');

/**
 * Log page views from frontend
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const logPageView = async (req, res) => {
  try {
    const { url, timestamp, userAgent } = req.body;

    // Log page view data
    logger.info('Page view tracked', {
      url,
      timestamp,
      userAgent: userAgent ? userAgent.substring(0, 200) : 'Unknown', // Truncate user agent
      ip: req.ip,
      userId: req.user?.id || 'anonymous'
    });

    return res.status(200).json({
      success: true,
      message: 'Page view logged successfully'
    });
  } catch (error) {
    logger.error(`Error logging page view: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to log page view',
      error: error.message
    });
  }
};

/**
 * Log custom events from frontend
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const logEvent = async (req, res) => {
  try {
    const { name, data, timestamp, url, userAgent } = req.body;

    // Log custom event
    logger.info('Custom event tracked', {
      eventName: name,
      eventData: data,
      url,
      timestamp,
      userAgent: userAgent ? userAgent.substring(0, 200) : 'Unknown',
      ip: req.ip,
      userId: req.user?.id || 'anonymous'
    });

    return res.status(200).json({
      success: true,
      message: 'Event logged successfully'
    });
  } catch (error) {
    logger.error(`Error logging event: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to log event',
      error: error.message
    });
  }
};

/**
 * Log performance metrics from frontend
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const logPerformanceMetric = async (req, res) => {
  try {
    const { name, value, timestamp, url } = req.body;

    // Log performance metric
    logger.info('Performance metric tracked', {
      metricName: name,
      metricValue: value,
      url,
      timestamp,
      ip: req.ip,
      userId: req.user?.id || 'anonymous'
    });

    return res.status(200).json({
      success: true,
      message: 'Performance metric logged successfully'
    });
  } catch (error) {
    logger.error(`Error logging performance metric: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to log performance metric',
      error: error.message
    });
  }
};

/**
 * Get monitoring health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMonitoringHealth = async (req, res) => {
  try {
    return res.status(200).json({
      success: true,
      status: 'healthy',
      data: {
        timestamp: new Date().toISOString(),
        service: 'monitoring',
        version: '1.0.0'
      }
    });
  } catch (error) {
    logger.error(`Monitoring health check error: ${error.message}`);
    return res.status(500).json({
      success: false,
      status: 'unhealthy',
      message: 'Monitoring service error',
      error: error.message
    });
  }
};

module.exports = {
  logPageView,
  logEvent,
  logPerformanceMetric,
  getMonitoringHealth
};
