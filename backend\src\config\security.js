/**
 * Security configuration for HypeHive backend
 * Centralizes all security-related settings and constants
 */

const logger = require('./logger');

// Security constants
const SECURITY_CONFIG = {
  // JWT Configuration
  JWT: {
    ALGORITHM: 'HS256',
    ISSUER: 'hypehive-backend',
    AUDIENCE: 'hypehive-users',
    MIN_SECRET_LENGTH: 32
  },
  
  // Password/Token Requirements
  PASSWORDS: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: false
  },
  
  // File Upload Security
  UPLOADS: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_MIME_TYPES: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'application/pdf'
    ],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm', '.pdf'],
    SCAN_FOR_MALWARE: true,
    QUARANTINE_SUSPICIOUS: true
  },
  
  // Rate Limiting
  RATE_LIMITS: {
    GENERAL: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      MAX_REQUESTS_PROD: 100,
      MAX_REQUESTS_DEV: 1000
    },
    AUTH: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      MAX_REQUESTS_PROD: 5,
      MAX_REQUESTS_DEV: 50
    },
    UPLOAD: {
      WINDOW_MS: 60 * 60 * 1000, // 1 hour
      MAX_REQUESTS_PROD: 10,
      MAX_REQUESTS_DEV: 100
    }
  },
  
  // CORS Configuration
  CORS: {
    ALLOWED_ORIGINS: [
      'https://hypehive.linksynk.info',
      'http://localhost:4200',
      'http://127.0.0.1:4200'
    ],
    ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    ALLOWED_HEADERS: ['Content-Type', 'Authorization', 'X-Requested-With'],
    CREDENTIALS: true,
    MAX_AGE: 86400 // 24 hours
  },
  
  // Content Security Policy
  CSP: {
    DEFAULT_SRC: ["'self'"],
    STYLE_SRC: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    FONT_SRC: ["'self'", "https://fonts.gstatic.com"],
    IMG_SRC: ["'self'", "data:", "https:", "blob:"],
    SCRIPT_SRC: ["'self'"],
    CONNECT_SRC: ["'self'", "https://discord.com", "https://discordapp.com"],
    FRAME_SRC: ["'none'"],
    OBJECT_SRC: ["'none'"]
  },
  
  // Input Validation
  VALIDATION: {
    MAX_STRING_LENGTH: 10000,
    MAX_ARRAY_LENGTH: 1000,
    MAX_OBJECT_DEPTH: 10,
    SANITIZE_HTML: true,
    ESCAPE_SQL: true
  },
  
  // Session Security
  SESSION: {
    SECURE: true, // HTTPS only
    HTTP_ONLY: true,
    SAME_SITE: 'none', // For cross-domain
    MAX_AGE: 7 * 24 * 60 * 60 * 1000, // 7 days
    REGENERATE_ON_LOGIN: true
  },
  
  // Monitoring and Logging
  MONITORING: {
    LOG_FAILED_LOGINS: true,
    LOG_SUSPICIOUS_ACTIVITY: true,
    ALERT_ON_MULTIPLE_FAILURES: true,
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 30 * 60 * 1000 // 30 minutes
  }
};

// Admin Discord IDs with system privileges
const SYSTEM_ADMIN_DISCORD_IDS = [
  '323345923964928001', // captain.sexy - System Administrator
  // Add additional admin Discord IDs here
];

/**
 * Validate JWT secret strength
 */
const validateJWTSecret = () => {
  const secret = process.env.JWT_SECRET;
  
  if (!secret) {
    logger.error('JWT_SECRET environment variable is not set');
    return false;
  }
  
  if (secret.length < SECURITY_CONFIG.JWT.MIN_SECRET_LENGTH) {
    logger.error(`JWT_SECRET is too short. Minimum length: ${SECURITY_CONFIG.JWT.MIN_SECRET_LENGTH}`);
    return false;
  }
  
  // Check for common weak secrets
  const weakSecrets = ['secret', 'password', 'test', 'dev', 'development'];
  if (weakSecrets.some(weak => secret.toLowerCase().includes(weak))) {
    logger.warn('JWT_SECRET appears to contain common weak patterns');
  }
  
  return true;
};

/**
 * Validate environment security configuration
 */
const validateSecurityConfig = () => {
  const issues = [];
  
  // Check JWT secret
  if (!validateJWTSecret()) {
    issues.push('Invalid JWT secret configuration');
  }
  
  // Check if running in production with debug settings
  if (process.env.NODE_ENV === 'production') {
    if (process.env.LOG_LEVEL === 'debug') {
      issues.push('Debug logging enabled in production');
    }
    
    if (!process.env.MONGODB_URI || process.env.MONGODB_URI.includes('localhost')) {
      issues.push('Using localhost MongoDB in production');
    }
  }
  
  // Check Discord configuration
  if (!process.env.DISCORD_CLIENT_ID || !process.env.DISCORD_CLIENT_SECRET) {
    issues.push('Discord OAuth credentials not configured');
  }
  
  if (issues.length > 0) {
    logger.error('Security configuration issues found:', issues);
    return false;
  }
  
  logger.info('Security configuration validation passed');
  return true;
};

/**
 * Check if a Discord ID has system admin privileges
 */
const isSystemAdmin = (discordId) => {
  return SYSTEM_ADMIN_DISCORD_IDS.includes(discordId);
};

/**
 * Get security headers for responses
 */
const getSecurityHeaders = () => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
  };
};

/**
 * Sanitize user input to prevent XSS and injection attacks
 */
const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[sanitizeInput(key)] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
};

module.exports = {
  SECURITY_CONFIG,
  SYSTEM_ADMIN_DISCORD_IDS,
  validateSecurityConfig,
  validateJWTSecret,
  isSystemAdmin,
  getSecurityHeaders,
  sanitizeInput
};
