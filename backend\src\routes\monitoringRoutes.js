const express = require('express');
const {
  logPageView,
  logEvent,
  logPerformanceMetric,
  getMonitoringHealth
} = require('../controllers/monitoringController');
const { monitoringLimiter } = require('../middleware/security');

const router = express.Router();

// Health check for monitoring service
router.get('/health', getMonitoringHealth);

// POST /api/monitoring/pageviews - Log page views (rate limited)
router.post('/pageviews', monitoringLimiter, logPageView);

// POST /api/monitoring/events - Log custom events (rate limited)
router.post('/events', monitoringLimiter, logEvent);

// POST /api/monitoring/performance - Log performance metrics (rate limited)
router.post('/performance', monitoringLimiter, logPerformanceMetric);

module.exports = router;
