import { User } from './user.model';

/**
 * Chat message model
 */
export interface ChatMessage {
  _id?: string;
  sender: User | string;
  content: string;
  readBy: (User | string)[];
  createdAt: Date;
}

/**
 * Last message info
 */
export interface LastMessage {
  content: string;
  sender: User | string;
  createdAt: Date;
}

/**
 * Chat model
 */
export interface Chat {
  _id: string;
  participants: User[];
  messages: ChatMessage[];
  lastMessage?: LastMessage;
  isGroupChat: boolean;
  groupName?: string;
  groupAdmin?: User | string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Chat creation data
 */
export interface ChatCreationData {
  participants: string[];
  isGroupChat?: boolean;
  groupName?: string;
}

/**
 * Typing indicator data
 */
export interface TypingIndicator {
  chatId: string;
  user: {
    _id: string;
    discordUsername: string;
  };
}

/**
 * New message event data
 */
export interface NewMessageEvent {
  chatId: string;
  message: ChatMessage;
}

/**
 * Chat pagination result
 */
export interface ChatPaginationResult {
  chats: Chat[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}
