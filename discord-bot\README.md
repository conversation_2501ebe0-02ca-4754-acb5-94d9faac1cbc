# HiveHelper Discord Bot

A Discord bot for the HypeHive platform that provides mentor management and user interaction features.

## Features

- **Mentor Status Checking**: Users can check their mentor status and application progress
- **Mentor Listing**: Display all current mentors and their specializations
- **Automatic Mentor List Updates**: Keeps a dedicated channel updated with current mentors
- **Admin Commands**: Administrative tools for managing the mentor system
- **Error Handling**: Comprehensive error handling and logging
- **API Integration**: Seamless integration with the HypeHive backend

## Commands

### User Commands
- `/mentor_status` - Check your mentor status and application progress
- `/list_mentors` - Show all current mentors and their specializations
- `/help` - Display available commands

### Admin Commands
- `/sync_mentors` - Manually sync the mentor list (requires administrator permissions)

## Setup

### Prerequisites
- Python 3.8 or higher
- Discord Bot Token
- Access to HypeHive backend API

### Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd discord-bot
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   Copy `.env.example` to `.env` and fill in your values:
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration:
   ```env
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DISCORD_GUILD_ID=your_discord_guild_id_here
   BACKEND_API_URL=http://localhost:3000/api
   LOG_LEVEL=INFO
   ```

### Discord Bot Setup

1. **Create a Discord Application**:
   - Go to https://discord.com/developers/applications
   - Click "New Application"
   - Give it a name (e.g., "HiveHelper")

2. **Create a Bot**:
   - Go to the "Bot" section
   - Click "Add Bot"
   - Copy the bot token and add it to your `.env` file

3. **Set Bot Permissions**:
   The bot needs the following permissions:
   - Send Messages
   - Use Slash Commands
   - Manage Roles
   - Manage Channels
   - Read Message History
   - Embed Links

4. **Invite Bot to Server**:
   - Go to the "OAuth2" > "URL Generator" section
   - Select "bot" and "applications.commands" scopes
   - Select the required permissions
   - Use the generated URL to invite the bot to your server

### Running the Bot

#### Option 1: Direct Run
```bash
python hive_helper.py
```

#### Option 2: Using Startup Script (Recommended)
```bash
python start.py
```

The startup script provides:
- Automatic dependency installation
- Environment validation
- Restart capabilities on crashes
- Better logging

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `DISCORD_BOT_TOKEN` | Discord bot token | Yes | - |
| `DISCORD_GUILD_ID` | Discord server ID | Yes | - |
| `BACKEND_API_URL` | HypeHive backend API URL | Yes | `http://localhost:3000/api` |
| `LOG_LEVEL` | Logging level | No | `INFO` |

### Bot Behavior

- **Mentor Role**: The bot automatically creates and manages a "Mentor" role
- **Mentor Channel**: Creates a "mentors-list" channel for displaying current mentors
- **Auto-sync**: The mentor list is automatically updated when the bot starts
- **Error Handling**: All errors are logged and users receive friendly error messages

## Integration with HypeHive Backend

The bot integrates with dedicated Discord bot API endpoints:

### Discord Bot Endpoints
- `GET /api/discord-bot/health` - Bot health check
- `GET /api/discord-bot/user/{discordId}` - Get user by Discord ID
- `GET /api/discord-bot/mentors` - Fetch all mentors (formatted for Discord)
- `POST /api/discord-bot/sync-user` - Create/update user from Discord
- `PUT /api/discord-bot/user/{discordId}/proficiencies` - Update user proficiencies

### Data Formats

#### User Data Response
```json
{
  "success": true,
  "data": {
    "discordId": "*********",
    "discordUsername": "username",
    "isMentor": true,
    "proficiencies": [
      {
        "category": "Account Setup",
        "isSelected": true
      }
    ],
    "mentorApplication": {
      "status": "approved"
    },
    "role": "user",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Mentors List Response
```json
{
  "success": true,
  "data": [
    {
      "discordId": "*********",
      "discordUsername": "username",
      "proficiencies": [...],
      "selectedProficiencies": ["Account Setup", "Bots"],
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

#### Health Check Response
```json
{
  "success": true,
  "status": "healthy",
  "data": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "database": "connected",
    "userCount": 100,
    "mentorCount": 10
  }
}
```

## Logging

The bot creates the following log files:
- `discord_bot.log` - Main bot activity and errors
- `bot_startup.log` - Startup script logs

Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

## Troubleshooting

### Common Issues

1. **Bot not responding to commands**:
   - Check if the bot has the required permissions
   - Verify the bot token is correct
   - Ensure slash commands are synced (restart the bot)

2. **API connection errors**:
   - Verify the backend API is running
   - Check the `BACKEND_API_URL` configuration
   - Ensure the backend is accessible from the bot's network

3. **Permission errors**:
   - Verify the bot has "Manage Roles" and "Manage Channels" permissions
   - Check if the bot's role is high enough in the role hierarchy

### Getting Help

If you encounter issues:
1. Check the log files for error messages
2. Verify all environment variables are set correctly
3. Ensure the backend API is running and accessible
4. Check Discord bot permissions and server settings

## Development

### Adding New Commands

1. Add the command function to `hive_helper.py`
2. Use proper error handling and logging
3. Update this README with the new command documentation
4. Test thoroughly before deployment

### Code Structure

- `hive_helper.py` - Main bot code
- `start.py` - Startup script with error handling
- `error_handler.py` - Error handling and monitoring
- `monitor.py` - Health monitoring script
- `test_integration.py` - Integration testing script
- `requirements.txt` - Python dependencies
- `.env.example` - Environment configuration template
- `Dockerfile` - Docker container configuration
- `docker-compose.yml` - Docker Compose configuration
- `deploy.sh` - Linux/macOS deployment script
- `deploy.ps1` - Windows PowerShell deployment script
- `README.md` - This documentation

## Deployment

### Prerequisites
- Docker and Docker Compose installed
- Discord bot token and guild ID
- Backend API running and accessible
- MongoDB database configured

### Production Deployment

#### Option 1: Docker (Recommended)
```bash
# Linux/macOS
./deploy.sh build && ./deploy.sh deploy

# Windows PowerShell
.\deploy.ps1 build; .\deploy.ps1 deploy
```

#### Option 2: Docker Compose (Full Stack)
```bash
# Linux/macOS
./deploy.sh compose

# Windows PowerShell
.\deploy.ps1 compose
```

#### Option 3: Manual Docker
```bash
# Build image
docker build -t hype-hive/discord-bot .

# Run container
docker run -d \
  --name hive-discord-bot \
  --restart unless-stopped \
  --env-file .env \
  -v ./logs:/app/logs \
  hype-hive/discord-bot
```

### Deployment Commands

#### Linux/macOS (deploy.sh)
- `./deploy.sh build` - Build Docker image
- `./deploy.sh deploy` - Deploy locally with Docker
- `./deploy.sh compose` - Deploy with Docker Compose
- `./deploy.sh health` - Check bot health
- `./deploy.sh logs` - Show bot logs
- `./deploy.sh stop` - Stop the bot
- `./deploy.sh status` - Show bot status

#### Windows PowerShell (deploy.ps1)
- `.\deploy.ps1 build` - Build Docker image
- `.\deploy.ps1 deploy` - Deploy locally with Docker
- `.\deploy.ps1 compose` - Deploy with Docker Compose
- `.\deploy.ps1 health` - Check bot health
- `.\deploy.ps1 logs` - Show bot logs
- `.\deploy.ps1 stop` - Stop the bot
- `.\deploy.ps1 status` - Show bot status

### Health Monitoring
The bot includes comprehensive health monitoring:
- Container health checks
- Backend API connectivity monitoring
- Automatic restart on failures
- Detailed logging and error reporting

### Testing Integration
Before deployment, test the integration:
```bash
# Test backend connectivity and API endpoints
python test_integration.py
```

### Production Considerations
- Use proper logging levels (INFO or WARNING in production)
- Set up log rotation to prevent disk space issues
- Monitor bot uptime and performance
- Configure alerts for critical failures
- Use secrets management for sensitive environment variables
- Set up backup and recovery procedures

## License

This project is part of the HypeHive platform.
