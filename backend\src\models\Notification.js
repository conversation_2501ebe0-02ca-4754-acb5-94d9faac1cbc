const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema({
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String,
    enum: ['like', 'comment', 'mention', 'message', 'mentor_application', 'system'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  isRead: {
    type: Boolean,
    default: false
  },
  relatedContent: {
    contentType: {
      type: String,
      enum: ['content', 'chat', 'user', 'system'],
      required: true
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'relatedContent.contentType'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Add indexes for better query performance
NotificationSchema.index({ recipient: 1, isRead: 1 });
NotificationSchema.index({ createdAt: -1 });
NotificationSchema.index({ type: 1 });
NotificationSchema.index({ 'relatedContent.contentType': 1, 'relatedContent.contentId': 1 });

const Notification = mongoose.model('Notification', NotificationSchema);

module.exports = Notification;
